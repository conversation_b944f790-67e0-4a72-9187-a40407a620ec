import { type Container, type Engine } from "tsparticles-engine";
import type { IParticlesJS } from "./VincentGarreau/IParticlesJS";
import { Particles } from "./marcbruederlin/Particles";
declare global {
    interface Window {
        Particles: typeof Particles;
        pJSDom: Container[];
        particlesJS: IParticlesJS;
    }
}
declare const initPjs: (engine: Engine) => {
    Particles: typeof Particles;
    pJSDom: Container[];
    particlesJS: IParticlesJS;
};
export { initPjs };
