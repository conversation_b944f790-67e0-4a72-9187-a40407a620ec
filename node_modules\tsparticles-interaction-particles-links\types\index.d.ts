import type { Engine } from "tsparticles-engine";
export declare function loadParticlesLinksInteraction(engine: Engine, refresh?: boolean): Promise<void>;
export * from "./Options/Classes/Links";
export * from "./Options/Classes/LinksShadow";
export * from "./Options/Classes/LinksTriangle";
export * from "./Options/Interfaces/ILinks";
export * from "./Options/Interfaces/ILinksShadow";
export * from "./Options/Interfaces/ILinksTriangle";
