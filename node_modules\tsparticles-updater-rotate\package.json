{"name": "tsparticles-updater-rotate", "version": "2.12.0", "description": "tsParticles particles rotate updater", "homepage": "https://particles.js.org", "repository": {"type": "git", "url": "git+https://github.com/matteobruni/tsparticles.git", "directory": "updaters/rotate"}, "keywords": ["front-end", "frontend", "tsparticles", "particles.js", "<PERSON><PERSON>s", "particles", "particle", "canvas", "jsparticles", "xparticles", "particles-js", "particles-bg", "particles-bg-vue", "particles-ts", "particles.ts", "react-particles-js", "react-particles.js", "react-particles", "react", "reactjs", "vue-particles", "ngx-particles", "angular-particles", "particleground", "vue", "v<PERSON><PERSON><PERSON>", "preact", "preactjs", "j<PERSON>y", "<PERSON><PERSON>s", "angular", "typescript", "javascript", "animation", "web", "html5", "web-design", "webdesign", "css", "html", "css3", "animated", "background", "confetti", "canvas", "fireworks", "fireworks-js", "confetti-js", "confettijs", "<PERSON>js", "canvas-confetti", "tsparticles-plugin", "tsparticles-updater"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/matteobruni/tsparticles/issues"}, "main": "cjs/index.js", "jsdelivr": "tsparticles.updater.rotate.min.js", "unpkg": "tsparticles.updater.rotate.min.js", "module": "esm/index.js", "types": "types/index.d.ts", "sideEffects": false, "dependencies": {"tsparticles-engine": "^2.12.0"}}