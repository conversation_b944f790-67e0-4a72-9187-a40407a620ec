import type { Container } from "../Core/Container";
import type { <PERSON>el<PERSON> } from "../Core/Interfaces/IDelta";
import type { Particle } from "../Core/Particle";
export type ShapeDrawerDrawFunction = (context: CanvasRenderingContext2D, particle: Particle, radius: number, opacity: number, delta: IDelta, pixelRatio: number) => void;
export type ShapeDrawerInitFunction = (container: Container) => Promise<void>;
export type ShapeDrawerParticleInitFunction = (container: Container, particle: Particle) => void;
export type ShapeDrawerAfterEffectFunction = (context: CanvasRenderingContext2D, particle: Particle, radius: number, opacity: number, delta: IDelta, pixelRatio: number) => void;
export type ShapeDrawerDestroyFunction = (container: Container) => void;
export type ShapeDrawerSidesCountFunction = (particle: Particle) => number;
export type ShapeDrawerLoadFunction = (particle: Particle) => void;
