/*! For license information please see tsparticles.shape.circle.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var r="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var n in r)("object"==typeof exports?exports:e)[n]=r[n]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return(()=>{n.r(o),n.d(o,{loadCircleShape:()=>r});var e=n(961);class t{draw(e,t,r){t.circleRange||(t.circleRange={min:0,max:2*Math.PI});const n=t.circleRange;e.arc(0,0,r,n.min,n.max,!1)}getSidesCount(){return 12}particleInit(t,r){const n=r.shapeData,o=n?.angle??{max:360,min:0};r.circleRange=(0,e.isObject)(o)?{min:o.min*Math.PI/180,max:o.max*Math.PI/180}:{min:0,max:o*Math.PI/180}}}async function r(e,r=!0){await e.addShape("circle",new t,r)}})(),o})()));