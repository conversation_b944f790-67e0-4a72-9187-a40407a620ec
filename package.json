{"name": "appointment-management-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "vite": "^7.0.4"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.51.0", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.83.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-big-calendar": "^1.19.4", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^7.6.3", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.17", "zod": "^4.0.5", "zustand": "^5.0.6"}}