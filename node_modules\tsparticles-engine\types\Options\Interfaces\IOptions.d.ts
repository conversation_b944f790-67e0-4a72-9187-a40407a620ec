import type { IBackground } from "./Background/IBackground";
import type { IBackgroundMask } from "./BackgroundMask/IBackgroundMask";
import type { IFullScreen } from "./FullScreen/IFullScreen";
import type { IInteractivity } from "./Interactivity/IInteractivity";
import type { IManualParticle } from "./IManualParticle";
import type { IParticlesOptions } from "./Particles/IParticlesOptions";
import type { IResponsive } from "./IResponsive";
import type { ITheme } from "./Theme/ITheme";
import type { RangeValue } from "../../Types/RangeValue";
import type { RecursivePartial } from "../../Types/RecursivePartial";
import type { SingleOrMultiple } from "../../Types/SingleOrMultiple";
export interface IOptions {
    [name: string]: unknown;
    autoPlay: boolean;
    background: IBackground;
    backgroundMask: IBackgroundMask;
    backgroundMode: RecursivePartial<IFullScreen> | boolean;
    delay: RangeValue;
    detectRetina: boolean;
    duration: RangeValue;
    fpsLimit: number;
    fps_limit: number;
    fullScreen: RecursivePartial<IFullScreen> | boolean;
    interactivity: IInteractivity;
    manualParticles: IManualParticle[];
    name?: string;
    particles: IParticlesOptions;
    pauseOnBlur: boolean;
    pauseOnOutsideViewport: boolean;
    preset?: SingleOrMultiple<string>;
    responsive: IResponsive[];
    retina_detect: boolean;
    smooth: boolean;
    style: RecursivePartial<CSSStyleDeclaration>;
    themes: ITheme[];
    zLayers: number;
}
