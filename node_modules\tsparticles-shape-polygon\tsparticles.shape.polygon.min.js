/*! For license information please see tsparticles.shape.polygon.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var n="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var o in n)("object"==typeof exports?exports:e)[o]=n[o]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},n={};function o(e){var r=n[e];if(void 0!==r)return r.exports;var a=n[e]={exports:{}};return t[e](a,a.exports,o),a.exports}o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};return(()=>{o.r(r),o.d(r,{loadGenericPolygonShape:()=>i,loadPolygonShape:()=>d,loadTriangleShape:()=>s});var e=o(961);class t{draw(e,t,n){const o=this.getCenter(t,n),r=this.getSidesData(t,n),a=r.count.numerator*r.count.denominator,i=r.count.numerator/r.count.denominator,s=180*(i-2)/i,d=Math.PI-Math.PI*s/180;if(e){e.beginPath(),e.translate(o.x,o.y),e.moveTo(0,0);for(let t=0;t<a;t++)e.lineTo(r.length,0),e.translate(r.length,0),e.rotate(d)}}getSidesCount(t){const n=t.shapeData;return Math.round((0,e.getRangeValue)(n?.sides??n?.nb_sides??5))}}class n extends t{getCenter(e,t){return{x:-t/(e.sides/3.5),y:-t/.76}}getSidesData(e,t){const n=e.sides;return{count:{denominator:1,numerator:n},length:2.66*t/(n/3)}}}class a extends t{getCenter(e,t){return{x:-t,y:t/1.66}}getSidesCount(){return 3}getSidesData(e,t){return{count:{denominator:2,numerator:3},length:2*t}}}async function i(e,t=!0){await e.addShape("polygon",new n,t)}async function s(e,t=!0){await e.addShape("triangle",new a,t)}async function d(e,t=!0){await i(e,t),await s(e,t)}})(),r})()));