(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const f of o)if(f.type==="childList")for(const h of f.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&s(h)}).observe(document,{childList:!0,subtree:!0});function i(o){const f={};return o.integrity&&(f.integrity=o.integrity),o.referrerPolicy&&(f.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?f.credentials="include":o.crossOrigin==="anonymous"?f.credentials="omit":f.credentials="same-origin",f}function s(o){if(o.ep)return;o.ep=!0;const f=i(o);fetch(o.href,f)}})();function Py(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var eo={exports:{}},ju={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ym;function fg(){if(Ym)return ju;Ym=1;var n=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function i(s,o,f){var h=null;if(f!==void 0&&(h=""+f),o.key!==void 0&&(h=""+o.key),"key"in o){f={};for(var y in o)y!=="key"&&(f[y]=o[y])}else f=o;return o=f.ref,{$$typeof:n,type:s,key:h,ref:o!==void 0?o:null,props:f}}return ju.Fragment=l,ju.jsx=i,ju.jsxs=i,ju}var $m;function dg(){return $m||($m=1,eo.exports=fg()),eo.exports}var g=dg(),to={exports:{}},me={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qm;function hg(){if(Qm)return me;Qm=1;var n=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),f=Symbol.for("react.consumer"),h=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),v=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),w=Symbol.iterator;function A(_){return _===null||typeof _!="object"?null:(_=w&&_[w]||_["@@iterator"],typeof _=="function"?_:null)}var L={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,M={};function G(_,Y,I){this.props=_,this.context=Y,this.refs=M,this.updater=I||L}G.prototype.isReactComponent={},G.prototype.setState=function(_,Y){if(typeof _!="object"&&typeof _!="function"&&_!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,_,Y,"setState")},G.prototype.forceUpdate=function(_){this.updater.enqueueForceUpdate(this,_,"forceUpdate")};function Z(){}Z.prototype=G.prototype;function B(_,Y,I){this.props=_,this.context=Y,this.refs=M,this.updater=I||L}var Q=B.prototype=new Z;Q.constructor=B,C(Q,G.prototype),Q.isPureReactComponent=!0;var ee=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},ze=Object.prototype.hasOwnProperty;function Ee(_,Y,I,P,ie,Se){return I=Se.ref,{$$typeof:n,type:_,key:Y,ref:I!==void 0?I:null,props:Se}}function Me(_,Y){return Ee(_.type,Y,void 0,void 0,void 0,_.props)}function be(_){return typeof _=="object"&&_!==null&&_.$$typeof===n}function Ge(_){var Y={"=":"=0",":":"=2"};return"$"+_.replace(/[=:]/g,function(I){return Y[I]})}var Xe=/\/+/g;function ce(_,Y){return typeof _=="object"&&_!==null&&_.key!=null?Ge(""+_.key):Y.toString(36)}function xe(){}function Re(_){switch(_.status){case"fulfilled":return _.value;case"rejected":throw _.reason;default:switch(typeof _.status=="string"?_.then(xe,xe):(_.status="pending",_.then(function(Y){_.status==="pending"&&(_.status="fulfilled",_.value=Y)},function(Y){_.status==="pending"&&(_.status="rejected",_.reason=Y)})),_.status){case"fulfilled":return _.value;case"rejected":throw _.reason}}throw _}function Te(_,Y,I,P,ie){var Se=typeof _;(Se==="undefined"||Se==="boolean")&&(_=null);var fe=!1;if(_===null)fe=!0;else switch(Se){case"bigint":case"string":case"number":fe=!0;break;case"object":switch(_.$$typeof){case n:case l:fe=!0;break;case x:return fe=_._init,Te(fe(_._payload),Y,I,P,ie)}}if(fe)return ie=ie(_),fe=P===""?"."+ce(_,0):P,ee(ie)?(I="",fe!=null&&(I=fe.replace(Xe,"$&/")+"/"),Te(ie,Y,I,"",function(Bt){return Bt})):ie!=null&&(be(ie)&&(ie=Me(ie,I+(ie.key==null||_&&_.key===ie.key?"":(""+ie.key).replace(Xe,"$&/")+"/")+fe)),Y.push(ie)),1;fe=0;var mt=P===""?".":P+":";if(ee(_))for(var He=0;He<_.length;He++)P=_[He],Se=mt+ce(P,He),fe+=Te(P,Y,I,Se,ie);else if(He=A(_),typeof He=="function")for(_=He.call(_),He=0;!(P=_.next()).done;)P=P.value,Se=mt+ce(P,He++),fe+=Te(P,Y,I,Se,ie);else if(Se==="object"){if(typeof _.then=="function")return Te(Re(_),Y,I,P,ie);throw Y=String(_),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(_).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return fe}function U(_,Y,I){if(_==null)return _;var P=[],ie=0;return Te(_,P,"","",function(Se){return Y.call(I,Se,ie++)}),P}function K(_){if(_._status===-1){var Y=_._result;Y=Y(),Y.then(function(I){(_._status===0||_._status===-1)&&(_._status=1,_._result=I)},function(I){(_._status===0||_._status===-1)&&(_._status=2,_._result=I)}),_._status===-1&&(_._status=0,_._result=Y)}if(_._status===1)return _._result.default;throw _._result}var ue=typeof reportError=="function"?reportError:function(_){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof _=="object"&&_!==null&&typeof _.message=="string"?String(_.message):String(_),error:_});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",_);return}console.error(_)};function Ae(){}return me.Children={map:U,forEach:function(_,Y,I){U(_,function(){Y.apply(this,arguments)},I)},count:function(_){var Y=0;return U(_,function(){Y++}),Y},toArray:function(_){return U(_,function(Y){return Y})||[]},only:function(_){if(!be(_))throw Error("React.Children.only expected to receive a single React element child.");return _}},me.Component=G,me.Fragment=i,me.Profiler=o,me.PureComponent=B,me.StrictMode=s,me.Suspense=v,me.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,me.__COMPILER_RUNTIME={__proto__:null,c:function(_){return F.H.useMemoCache(_)}},me.cache=function(_){return function(){return _.apply(null,arguments)}},me.cloneElement=function(_,Y,I){if(_==null)throw Error("The argument must be a React element, but you passed "+_+".");var P=C({},_.props),ie=_.key,Se=void 0;if(Y!=null)for(fe in Y.ref!==void 0&&(Se=void 0),Y.key!==void 0&&(ie=""+Y.key),Y)!ze.call(Y,fe)||fe==="key"||fe==="__self"||fe==="__source"||fe==="ref"&&Y.ref===void 0||(P[fe]=Y[fe]);var fe=arguments.length-2;if(fe===1)P.children=I;else if(1<fe){for(var mt=Array(fe),He=0;He<fe;He++)mt[He]=arguments[He+2];P.children=mt}return Ee(_.type,ie,void 0,void 0,Se,P)},me.createContext=function(_){return _={$$typeof:h,_currentValue:_,_currentValue2:_,_threadCount:0,Provider:null,Consumer:null},_.Provider=_,_.Consumer={$$typeof:f,_context:_},_},me.createElement=function(_,Y,I){var P,ie={},Se=null;if(Y!=null)for(P in Y.key!==void 0&&(Se=""+Y.key),Y)ze.call(Y,P)&&P!=="key"&&P!=="__self"&&P!=="__source"&&(ie[P]=Y[P]);var fe=arguments.length-2;if(fe===1)ie.children=I;else if(1<fe){for(var mt=Array(fe),He=0;He<fe;He++)mt[He]=arguments[He+2];ie.children=mt}if(_&&_.defaultProps)for(P in fe=_.defaultProps,fe)ie[P]===void 0&&(ie[P]=fe[P]);return Ee(_,Se,void 0,void 0,null,ie)},me.createRef=function(){return{current:null}},me.forwardRef=function(_){return{$$typeof:y,render:_}},me.isValidElement=be,me.lazy=function(_){return{$$typeof:x,_payload:{_status:-1,_result:_},_init:K}},me.memo=function(_,Y){return{$$typeof:m,type:_,compare:Y===void 0?null:Y}},me.startTransition=function(_){var Y=F.T,I={};F.T=I;try{var P=_(),ie=F.S;ie!==null&&ie(I,P),typeof P=="object"&&P!==null&&typeof P.then=="function"&&P.then(Ae,ue)}catch(Se){ue(Se)}finally{F.T=Y}},me.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},me.use=function(_){return F.H.use(_)},me.useActionState=function(_,Y,I){return F.H.useActionState(_,Y,I)},me.useCallback=function(_,Y){return F.H.useCallback(_,Y)},me.useContext=function(_){return F.H.useContext(_)},me.useDebugValue=function(){},me.useDeferredValue=function(_,Y){return F.H.useDeferredValue(_,Y)},me.useEffect=function(_,Y,I){var P=F.H;if(typeof I=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return P.useEffect(_,Y)},me.useId=function(){return F.H.useId()},me.useImperativeHandle=function(_,Y,I){return F.H.useImperativeHandle(_,Y,I)},me.useInsertionEffect=function(_,Y){return F.H.useInsertionEffect(_,Y)},me.useLayoutEffect=function(_,Y){return F.H.useLayoutEffect(_,Y)},me.useMemo=function(_,Y){return F.H.useMemo(_,Y)},me.useOptimistic=function(_,Y){return F.H.useOptimistic(_,Y)},me.useReducer=function(_,Y,I){return F.H.useReducer(_,Y,I)},me.useRef=function(_){return F.H.useRef(_)},me.useState=function(_){return F.H.useState(_)},me.useSyncExternalStore=function(_,Y,I){return F.H.useSyncExternalStore(_,Y,I)},me.useTransition=function(){return F.H.useTransition()},me.version="19.1.0",me}var Gm;function No(){return Gm||(Gm=1,to.exports=hg()),to.exports}var R=No();const Dt=Py(R);var no={exports:{}},Mu={},ao={exports:{}},lo={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Xm;function mg(){return Xm||(Xm=1,function(n){function l(U,K){var ue=U.length;U.push(K);e:for(;0<ue;){var Ae=ue-1>>>1,_=U[Ae];if(0<o(_,K))U[Ae]=K,U[ue]=_,ue=Ae;else break e}}function i(U){return U.length===0?null:U[0]}function s(U){if(U.length===0)return null;var K=U[0],ue=U.pop();if(ue!==K){U[0]=ue;e:for(var Ae=0,_=U.length,Y=_>>>1;Ae<Y;){var I=2*(Ae+1)-1,P=U[I],ie=I+1,Se=U[ie];if(0>o(P,ue))ie<_&&0>o(Se,P)?(U[Ae]=Se,U[ie]=ue,Ae=ie):(U[Ae]=P,U[I]=ue,Ae=I);else if(ie<_&&0>o(Se,ue))U[Ae]=Se,U[ie]=ue,Ae=ie;else break e}}return K}function o(U,K){var ue=U.sortIndex-K.sortIndex;return ue!==0?ue:U.id-K.id}if(n.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var f=performance;n.unstable_now=function(){return f.now()}}else{var h=Date,y=h.now();n.unstable_now=function(){return h.now()-y}}var v=[],m=[],x=1,w=null,A=3,L=!1,C=!1,M=!1,G=!1,Z=typeof setTimeout=="function"?setTimeout:null,B=typeof clearTimeout=="function"?clearTimeout:null,Q=typeof setImmediate<"u"?setImmediate:null;function ee(U){for(var K=i(m);K!==null;){if(K.callback===null)s(m);else if(K.startTime<=U)s(m),K.sortIndex=K.expirationTime,l(v,K);else break;K=i(m)}}function F(U){if(M=!1,ee(U),!C)if(i(v)!==null)C=!0,ze||(ze=!0,ce());else{var K=i(m);K!==null&&Te(F,K.startTime-U)}}var ze=!1,Ee=-1,Me=5,be=-1;function Ge(){return G?!0:!(n.unstable_now()-be<Me)}function Xe(){if(G=!1,ze){var U=n.unstable_now();be=U;var K=!0;try{e:{C=!1,M&&(M=!1,B(Ee),Ee=-1),L=!0;var ue=A;try{t:{for(ee(U),w=i(v);w!==null&&!(w.expirationTime>U&&Ge());){var Ae=w.callback;if(typeof Ae=="function"){w.callback=null,A=w.priorityLevel;var _=Ae(w.expirationTime<=U);if(U=n.unstable_now(),typeof _=="function"){w.callback=_,ee(U),K=!0;break t}w===i(v)&&s(v),ee(U)}else s(v);w=i(v)}if(w!==null)K=!0;else{var Y=i(m);Y!==null&&Te(F,Y.startTime-U),K=!1}}break e}finally{w=null,A=ue,L=!1}K=void 0}}finally{K?ce():ze=!1}}}var ce;if(typeof Q=="function")ce=function(){Q(Xe)};else if(typeof MessageChannel<"u"){var xe=new MessageChannel,Re=xe.port2;xe.port1.onmessage=Xe,ce=function(){Re.postMessage(null)}}else ce=function(){Z(Xe,0)};function Te(U,K){Ee=Z(function(){U(n.unstable_now())},K)}n.unstable_IdlePriority=5,n.unstable_ImmediatePriority=1,n.unstable_LowPriority=4,n.unstable_NormalPriority=3,n.unstable_Profiling=null,n.unstable_UserBlockingPriority=2,n.unstable_cancelCallback=function(U){U.callback=null},n.unstable_forceFrameRate=function(U){0>U||125<U?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Me=0<U?Math.floor(1e3/U):5},n.unstable_getCurrentPriorityLevel=function(){return A},n.unstable_next=function(U){switch(A){case 1:case 2:case 3:var K=3;break;default:K=A}var ue=A;A=K;try{return U()}finally{A=ue}},n.unstable_requestPaint=function(){G=!0},n.unstable_runWithPriority=function(U,K){switch(U){case 1:case 2:case 3:case 4:case 5:break;default:U=3}var ue=A;A=U;try{return K()}finally{A=ue}},n.unstable_scheduleCallback=function(U,K,ue){var Ae=n.unstable_now();switch(typeof ue=="object"&&ue!==null?(ue=ue.delay,ue=typeof ue=="number"&&0<ue?Ae+ue:Ae):ue=Ae,U){case 1:var _=-1;break;case 2:_=250;break;case 5:_=1073741823;break;case 4:_=1e4;break;default:_=5e3}return _=ue+_,U={id:x++,callback:K,priorityLevel:U,startTime:ue,expirationTime:_,sortIndex:-1},ue>Ae?(U.sortIndex=ue,l(m,U),i(v)===null&&U===i(m)&&(M?(B(Ee),Ee=-1):M=!0,Te(F,ue-Ae))):(U.sortIndex=_,l(v,U),C||L||(C=!0,ze||(ze=!0,ce()))),U},n.unstable_shouldYield=Ge,n.unstable_wrapCallback=function(U){var K=A;return function(){var ue=A;A=K;try{return U.apply(this,arguments)}finally{A=ue}}}}(lo)),lo}var Km;function yg(){return Km||(Km=1,ao.exports=mg()),ao.exports}var uo={exports:{}},xt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fm;function pg(){if(Fm)return xt;Fm=1;var n=No();function l(v){var m="https://react.dev/errors/"+v;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)m+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+v+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var s={d:{f:i,r:function(){throw Error(l(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},o=Symbol.for("react.portal");function f(v,m,x){var w=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:w==null?null:""+w,children:v,containerInfo:m,implementation:x}}var h=n.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function y(v,m){if(v==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return xt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,xt.createPortal=function(v,m){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(l(299));return f(v,m,null,x)},xt.flushSync=function(v){var m=h.T,x=s.p;try{if(h.T=null,s.p=2,v)return v()}finally{h.T=m,s.p=x,s.d.f()}},xt.preconnect=function(v,m){typeof v=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,s.d.C(v,m))},xt.prefetchDNS=function(v){typeof v=="string"&&s.d.D(v)},xt.preinit=function(v,m){if(typeof v=="string"&&m&&typeof m.as=="string"){var x=m.as,w=y(x,m.crossOrigin),A=typeof m.integrity=="string"?m.integrity:void 0,L=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;x==="style"?s.d.S(v,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:w,integrity:A,fetchPriority:L}):x==="script"&&s.d.X(v,{crossOrigin:w,integrity:A,fetchPriority:L,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},xt.preinitModule=function(v,m){if(typeof v=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var x=y(m.as,m.crossOrigin);s.d.M(v,{crossOrigin:x,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&s.d.M(v)},xt.preload=function(v,m){if(typeof v=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var x=m.as,w=y(x,m.crossOrigin);s.d.L(v,x,{crossOrigin:w,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},xt.preloadModule=function(v,m){if(typeof v=="string")if(m){var x=y(m.as,m.crossOrigin);s.d.m(v,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:x,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else s.d.m(v)},xt.requestFormReset=function(v){s.d.r(v)},xt.unstable_batchedUpdates=function(v,m){return v(m)},xt.useFormState=function(v,m,x){return h.H.useFormState(v,m,x)},xt.useFormStatus=function(){return h.H.useHostTransitionStatus()},xt.version="19.1.0",xt}var Jm;function vg(){if(Jm)return uo.exports;Jm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(l){console.error(l)}}return n(),uo.exports=pg(),uo.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pm;function gg(){if(Pm)return Mu;Pm=1;var n=yg(),l=No(),i=vg();function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function f(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function y(e){if(f(e)!==e)throw Error(s(188))}function v(e){var t=e.alternate;if(!t){if(t=f(e),t===null)throw Error(s(188));return t!==e?null:e}for(var a=e,u=t;;){var r=a.return;if(r===null)break;var c=r.alternate;if(c===null){if(u=r.return,u!==null){a=u;continue}break}if(r.child===c.child){for(c=r.child;c;){if(c===a)return y(r),e;if(c===u)return y(r),t;c=c.sibling}throw Error(s(188))}if(a.return!==u.return)a=r,u=c;else{for(var d=!1,p=r.child;p;){if(p===a){d=!0,a=r,u=c;break}if(p===u){d=!0,u=r,a=c;break}p=p.sibling}if(!d){for(p=c.child;p;){if(p===a){d=!0,a=c,u=r;break}if(p===u){d=!0,u=c,a=r;break}p=p.sibling}if(!d)throw Error(s(189))}}if(a.alternate!==u)throw Error(s(190))}if(a.tag!==3)throw Error(s(188));return a.stateNode.current===a?e:t}function m(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=m(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,w=Symbol.for("react.element"),A=Symbol.for("react.transitional.element"),L=Symbol.for("react.portal"),C=Symbol.for("react.fragment"),M=Symbol.for("react.strict_mode"),G=Symbol.for("react.profiler"),Z=Symbol.for("react.provider"),B=Symbol.for("react.consumer"),Q=Symbol.for("react.context"),ee=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),ze=Symbol.for("react.suspense_list"),Ee=Symbol.for("react.memo"),Me=Symbol.for("react.lazy"),be=Symbol.for("react.activity"),Ge=Symbol.for("react.memo_cache_sentinel"),Xe=Symbol.iterator;function ce(e){return e===null||typeof e!="object"?null:(e=Xe&&e[Xe]||e["@@iterator"],typeof e=="function"?e:null)}var xe=Symbol.for("react.client.reference");function Re(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===xe?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case C:return"Fragment";case G:return"Profiler";case M:return"StrictMode";case F:return"Suspense";case ze:return"SuspenseList";case be:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case L:return"Portal";case Q:return(e.displayName||"Context")+".Provider";case B:return(e._context.displayName||"Context")+".Consumer";case ee:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ee:return t=e.displayName||null,t!==null?t:Re(e.type)||"Memo";case Me:t=e._payload,e=e._init;try{return Re(e(t))}catch{}}return null}var Te=Array.isArray,U=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ue={pending:!1,data:null,method:null,action:null},Ae=[],_=-1;function Y(e){return{current:e}}function I(e){0>_||(e.current=Ae[_],Ae[_]=null,_--)}function P(e,t){_++,Ae[_]=e.current,e.current=t}var ie=Y(null),Se=Y(null),fe=Y(null),mt=Y(null);function He(e,t){switch(P(fe,t),P(Se,e),P(ie,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?vm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=vm(t),e=gm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}I(ie),P(ie,e)}function Bt(){I(ie),I(Se),I(fe)}function ma(e){e.memoizedState!==null&&P(mt,e);var t=ie.current,a=gm(t,e.type);t!==a&&(P(Se,e),P(ie,a))}function Ba(e){Se.current===e&&(I(ie),I(Se)),mt.current===e&&(I(mt),Tu._currentValue=ue)}var Va=Object.prototype.hasOwnProperty,Ul=n.unstable_scheduleCallback,Ya=n.unstable_cancelCallback,ei=n.unstable_shouldYield,Br=n.unstable_requestPaint,Vt=n.unstable_now,Wo=n.unstable_getCurrentPriorityLevel,Zl=n.unstable_ImmediatePriority,S=n.unstable_UserBlockingPriority,N=n.unstable_NormalPriority,q=n.unstable_LowPriority,W=n.unstable_IdlePriority,J=n.log,X=n.unstable_setDisableYieldValue,ae=null,he=null;function _e(e){if(typeof J=="function"&&X(e),he&&typeof he.setStrictMode=="function")try{he.setStrictMode(ae,e)}catch{}}var ke=Math.clz32?Math.clz32:Vr,$a=Math.log,rn=Math.LN2;function Vr(e){return e>>>=0,e===0?32:31-($a(e)/rn|0)|0}var Ln=256,kn=4194304;function vn(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ya(e,t,a){var u=e.pendingLanes;if(u===0)return 0;var r=0,c=e.suspendedLanes,d=e.pingedLanes;e=e.warmLanes;var p=u&134217727;return p!==0?(u=p&~c,u!==0?r=vn(u):(d&=p,d!==0?r=vn(d):a||(a=p&~e,a!==0&&(r=vn(a))))):(p=u&~c,p!==0?r=vn(p):d!==0?r=vn(d):a||(a=u&~e,a!==0&&(r=vn(a)))),r===0?0:t!==0&&t!==r&&(t&c)===0&&(c=r&-r,a=t&-t,c>=a||c===32&&(a&4194048)!==0)?t:r}function pa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function ti(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Io(){var e=Ln;return Ln<<=1,(Ln&4194048)===0&&(Ln=256),e}function ef(){var e=kn;return kn<<=1,(kn&62914560)===0&&(kn=4194304),e}function Yr(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Hl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ip(e,t,a,u,r,c){var d=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var p=e.entanglements,b=e.expirationTimes,O=e.hiddenUpdates;for(a=d&~a;0<a;){var H=31-ke(a),V=1<<H;p[H]=0,b[H]=-1;var D=O[H];if(D!==null)for(O[H]=null,H=0;H<D.length;H++){var j=D[H];j!==null&&(j.lane&=-536870913)}a&=~V}u!==0&&tf(e,u,0),c!==0&&r===0&&e.tag!==0&&(e.suspendedLanes|=c&~(d&~t))}function tf(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var u=31-ke(t);e.entangledLanes|=t,e.entanglements[u]=e.entanglements[u]|1073741824|a&4194090}function nf(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var u=31-ke(a),r=1<<u;r&t|e[u]&t&&(e[u]|=t),a&=~r}}function $r(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Qr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function af(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:Hm(e.type))}function e0(e,t){var a=K.p;try{return K.p=e,t()}finally{K.p=a}}var Bn=Math.random().toString(36).slice(2),gt="__reactFiber$"+Bn,zt="__reactProps$"+Bn,Qa="__reactContainer$"+Bn,Gr="__reactEvents$"+Bn,t0="__reactListeners$"+Bn,n0="__reactHandles$"+Bn,lf="__reactResources$"+Bn,ql="__reactMarker$"+Bn;function Xr(e){delete e[gt],delete e[zt],delete e[Gr],delete e[t0],delete e[n0]}function Ga(e){var t=e[gt];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Qa]||a[gt]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=_m(e);e!==null;){if(a=e[gt])return a;e=_m(e)}return t}e=a,a=e.parentNode}return null}function Xa(e){if(e=e[gt]||e[Qa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Ll(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(s(33))}function Ka(e){var t=e[lf];return t||(t=e[lf]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function st(e){e[ql]=!0}var uf=new Set,rf={};function va(e,t){Fa(e,t),Fa(e+"Capture",t)}function Fa(e,t){for(rf[e]=t,e=0;e<t.length;e++)uf.add(t[e])}var a0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),sf={},cf={};function l0(e){return Va.call(cf,e)?!0:Va.call(sf,e)?!1:a0.test(e)?cf[e]=!0:(sf[e]=!0,!1)}function ni(e,t,a){if(l0(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var u=t.toLowerCase().slice(0,5);if(u!=="data-"&&u!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function ai(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function gn(e,t,a,u){if(u===null)e.removeAttribute(a);else{switch(typeof u){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+u)}}var Kr,of;function Ja(e){if(Kr===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Kr=t&&t[1]||"",of=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Kr+e+of}var Fr=!1;function Jr(e,t){if(!e||Fr)return"";Fr=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var u={DetermineComponentFrameRoot:function(){try{if(t){var V=function(){throw Error()};if(Object.defineProperty(V.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(V,[])}catch(j){var D=j}Reflect.construct(e,[],V)}else{try{V.call()}catch(j){D=j}e.call(V.prototype)}}else{try{throw Error()}catch(j){D=j}(V=e())&&typeof V.catch=="function"&&V.catch(function(){})}}catch(j){if(j&&D&&typeof j.stack=="string")return[j.stack,D.stack]}return[null,null]}};u.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(u.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(u.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var c=u.DetermineComponentFrameRoot(),d=c[0],p=c[1];if(d&&p){var b=d.split(`
`),O=p.split(`
`);for(r=u=0;u<b.length&&!b[u].includes("DetermineComponentFrameRoot");)u++;for(;r<O.length&&!O[r].includes("DetermineComponentFrameRoot");)r++;if(u===b.length||r===O.length)for(u=b.length-1,r=O.length-1;1<=u&&0<=r&&b[u]!==O[r];)r--;for(;1<=u&&0<=r;u--,r--)if(b[u]!==O[r]){if(u!==1||r!==1)do if(u--,r--,0>r||b[u]!==O[r]){var H=`
`+b[u].replace(" at new "," at ");return e.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",e.displayName)),H}while(1<=u&&0<=r);break}}}finally{Fr=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Ja(a):""}function u0(e){switch(e.tag){case 26:case 27:case 5:return Ja(e.type);case 16:return Ja("Lazy");case 13:return Ja("Suspense");case 19:return Ja("SuspenseList");case 0:case 15:return Jr(e.type,!1);case 11:return Jr(e.type.render,!1);case 1:return Jr(e.type,!0);case 31:return Ja("Activity");default:return""}}function ff(e){try{var t="";do t+=u0(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function Yt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function df(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function i0(e){var t=df(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),u=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var r=a.get,c=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(d){u=""+d,c.call(this,d)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return u},setValue:function(d){u=""+d},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function li(e){e._valueTracker||(e._valueTracker=i0(e))}function hf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),u="";return e&&(u=df(e)?e.checked?"true":"false":e.value),e=u,e!==a?(t.setValue(e),!0):!1}function ui(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var r0=/[\n"\\]/g;function $t(e){return e.replace(r0,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Pr(e,t,a,u,r,c,d,p){e.name="",d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.type=d:e.removeAttribute("type"),t!=null?d==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Yt(t)):e.value!==""+Yt(t)&&(e.value=""+Yt(t)):d!=="submit"&&d!=="reset"||e.removeAttribute("value"),t!=null?Wr(e,d,Yt(t)):a!=null?Wr(e,d,Yt(a)):u!=null&&e.removeAttribute("value"),r==null&&c!=null&&(e.defaultChecked=!!c),r!=null&&(e.checked=r&&typeof r!="function"&&typeof r!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Yt(p):e.removeAttribute("name")}function mf(e,t,a,u,r,c,d,p){if(c!=null&&typeof c!="function"&&typeof c!="symbol"&&typeof c!="boolean"&&(e.type=c),t!=null||a!=null){if(!(c!=="submit"&&c!=="reset"||t!=null))return;a=a!=null?""+Yt(a):"",t=t!=null?""+Yt(t):a,p||t===e.value||(e.value=t),e.defaultValue=t}u=u??r,u=typeof u!="function"&&typeof u!="symbol"&&!!u,e.checked=p?e.checked:!!u,e.defaultChecked=!!u,d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"&&(e.name=d)}function Wr(e,t,a){t==="number"&&ui(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Pa(e,t,a,u){if(e=e.options,t){t={};for(var r=0;r<a.length;r++)t["$"+a[r]]=!0;for(a=0;a<e.length;a++)r=t.hasOwnProperty("$"+e[a].value),e[a].selected!==r&&(e[a].selected=r),r&&u&&(e[a].defaultSelected=!0)}else{for(a=""+Yt(a),t=null,r=0;r<e.length;r++){if(e[r].value===a){e[r].selected=!0,u&&(e[r].defaultSelected=!0);return}t!==null||e[r].disabled||(t=e[r])}t!==null&&(t.selected=!0)}}function yf(e,t,a){if(t!=null&&(t=""+Yt(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+Yt(a):""}function pf(e,t,a,u){if(t==null){if(u!=null){if(a!=null)throw Error(s(92));if(Te(u)){if(1<u.length)throw Error(s(93));u=u[0]}a=u}a==null&&(a=""),t=a}a=Yt(t),e.defaultValue=a,u=e.textContent,u===a&&u!==""&&u!==null&&(e.value=u)}function Wa(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var s0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function vf(e,t,a){var u=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?u?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":u?e.setProperty(t,a):typeof a!="number"||a===0||s0.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function gf(e,t,a){if(t!=null&&typeof t!="object")throw Error(s(62));if(e=e.style,a!=null){for(var u in a)!a.hasOwnProperty(u)||t!=null&&t.hasOwnProperty(u)||(u.indexOf("--")===0?e.setProperty(u,""):u==="float"?e.cssFloat="":e[u]="");for(var r in t)u=t[r],t.hasOwnProperty(r)&&a[r]!==u&&vf(e,r,u)}else for(var c in t)t.hasOwnProperty(c)&&vf(e,c,t[c])}function Ir(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var c0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),o0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function ii(e){return o0.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var es=null;function ts(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ia=null,el=null;function bf(e){var t=Xa(e);if(t&&(e=t.stateNode)){var a=e[zt]||null;e:switch(e=t.stateNode,t.type){case"input":if(Pr(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+$t(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var u=a[t];if(u!==e&&u.form===e.form){var r=u[zt]||null;if(!r)throw Error(s(90));Pr(u,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(t=0;t<a.length;t++)u=a[t],u.form===e.form&&hf(u)}break e;case"textarea":yf(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Pa(e,!!a.multiple,t,!1)}}}var ns=!1;function xf(e,t,a){if(ns)return e(t,a);ns=!0;try{var u=e(t);return u}finally{if(ns=!1,(Ia!==null||el!==null)&&(Qi(),Ia&&(t=Ia,e=el,el=Ia=null,bf(t),e)))for(t=0;t<e.length;t++)bf(e[t])}}function kl(e,t){var a=e.stateNode;if(a===null)return null;var u=a[zt]||null;if(u===null)return null;a=u[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(e=e.type,u=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!u;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(s(231,t,typeof a));return a}var bn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),as=!1;if(bn)try{var Bl={};Object.defineProperty(Bl,"passive",{get:function(){as=!0}}),window.addEventListener("test",Bl,Bl),window.removeEventListener("test",Bl,Bl)}catch{as=!1}var Vn=null,ls=null,ri=null;function Sf(){if(ri)return ri;var e,t=ls,a=t.length,u,r="value"in Vn?Vn.value:Vn.textContent,c=r.length;for(e=0;e<a&&t[e]===r[e];e++);var d=a-e;for(u=1;u<=d&&t[a-u]===r[c-u];u++);return ri=r.slice(e,1<u?1-u:void 0)}function si(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ci(){return!0}function _f(){return!1}function Tt(e){function t(a,u,r,c,d){this._reactName=a,this._targetInst=r,this.type=u,this.nativeEvent=c,this.target=d,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(a=e[p],this[p]=a?a(c):c[p]);return this.isDefaultPrevented=(c.defaultPrevented!=null?c.defaultPrevented:c.returnValue===!1)?ci:_f,this.isPropagationStopped=_f,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=ci)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=ci)},persist:function(){},isPersistent:ci}),t}var ga={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},oi=Tt(ga),Vl=x({},ga,{view:0,detail:0}),f0=Tt(Vl),us,is,Yl,fi=x({},Vl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ss,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yl&&(Yl&&e.type==="mousemove"?(us=e.screenX-Yl.screenX,is=e.screenY-Yl.screenY):is=us=0,Yl=e),us)},movementY:function(e){return"movementY"in e?e.movementY:is}}),Ef=Tt(fi),d0=x({},fi,{dataTransfer:0}),h0=Tt(d0),m0=x({},Vl,{relatedTarget:0}),rs=Tt(m0),y0=x({},ga,{animationName:0,elapsedTime:0,pseudoElement:0}),p0=Tt(y0),v0=x({},ga,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),g0=Tt(v0),b0=x({},ga,{data:0}),Af=Tt(b0),x0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},S0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},_0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function E0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=_0[e])?!!t[e]:!1}function ss(){return E0}var A0=x({},Vl,{key:function(e){if(e.key){var t=x0[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=si(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?S0[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ss,charCode:function(e){return e.type==="keypress"?si(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?si(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),w0=Tt(A0),z0=x({},fi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),wf=Tt(z0),T0=x({},Vl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ss}),O0=Tt(T0),N0=x({},ga,{propertyName:0,elapsedTime:0,pseudoElement:0}),R0=Tt(N0),D0=x({},fi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),j0=Tt(D0),M0=x({},ga,{newState:0,oldState:0}),C0=Tt(M0),U0=[9,13,27,32],cs=bn&&"CompositionEvent"in window,$l=null;bn&&"documentMode"in document&&($l=document.documentMode);var Z0=bn&&"TextEvent"in window&&!$l,zf=bn&&(!cs||$l&&8<$l&&11>=$l),Tf=" ",Of=!1;function Nf(e,t){switch(e){case"keyup":return U0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Rf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var tl=!1;function H0(e,t){switch(e){case"compositionend":return Rf(t);case"keypress":return t.which!==32?null:(Of=!0,Tf);case"textInput":return e=t.data,e===Tf&&Of?null:e;default:return null}}function q0(e,t){if(tl)return e==="compositionend"||!cs&&Nf(e,t)?(e=Sf(),ri=ls=Vn=null,tl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zf&&t.locale!=="ko"?null:t.data;default:return null}}var L0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Df(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!L0[e.type]:t==="textarea"}function jf(e,t,a,u){Ia?el?el.push(u):el=[u]:Ia=u,t=Pi(t,"onChange"),0<t.length&&(a=new oi("onChange","change",null,a,u),e.push({event:a,listeners:t}))}var Ql=null,Gl=null;function k0(e){dm(e,0)}function di(e){var t=Ll(e);if(hf(t))return e}function Mf(e,t){if(e==="change")return t}var Cf=!1;if(bn){var os;if(bn){var fs="oninput"in document;if(!fs){var Uf=document.createElement("div");Uf.setAttribute("oninput","return;"),fs=typeof Uf.oninput=="function"}os=fs}else os=!1;Cf=os&&(!document.documentMode||9<document.documentMode)}function Zf(){Ql&&(Ql.detachEvent("onpropertychange",Hf),Gl=Ql=null)}function Hf(e){if(e.propertyName==="value"&&di(Gl)){var t=[];jf(t,Gl,e,ts(e)),xf(k0,t)}}function B0(e,t,a){e==="focusin"?(Zf(),Ql=t,Gl=a,Ql.attachEvent("onpropertychange",Hf)):e==="focusout"&&Zf()}function V0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return di(Gl)}function Y0(e,t){if(e==="click")return di(t)}function $0(e,t){if(e==="input"||e==="change")return di(t)}function Q0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ct=typeof Object.is=="function"?Object.is:Q0;function Xl(e,t){if(Ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),u=Object.keys(t);if(a.length!==u.length)return!1;for(u=0;u<a.length;u++){var r=a[u];if(!Va.call(t,r)||!Ct(e[r],t[r]))return!1}return!0}function qf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Lf(e,t){var a=qf(e);e=0;for(var u;a;){if(a.nodeType===3){if(u=e+a.textContent.length,e<=t&&u>=t)return{node:a,offset:t-e};e=u}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=qf(a)}}function kf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?kf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Bf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=ui(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=ui(e.document)}return t}function ds(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var G0=bn&&"documentMode"in document&&11>=document.documentMode,nl=null,hs=null,Kl=null,ms=!1;function Vf(e,t,a){var u=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;ms||nl==null||nl!==ui(u)||(u=nl,"selectionStart"in u&&ds(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),Kl&&Xl(Kl,u)||(Kl=u,u=Pi(hs,"onSelect"),0<u.length&&(t=new oi("onSelect","select",null,t,a),e.push({event:t,listeners:u}),t.target=nl)))}function ba(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var al={animationend:ba("Animation","AnimationEnd"),animationiteration:ba("Animation","AnimationIteration"),animationstart:ba("Animation","AnimationStart"),transitionrun:ba("Transition","TransitionRun"),transitionstart:ba("Transition","TransitionStart"),transitioncancel:ba("Transition","TransitionCancel"),transitionend:ba("Transition","TransitionEnd")},ys={},Yf={};bn&&(Yf=document.createElement("div").style,"AnimationEvent"in window||(delete al.animationend.animation,delete al.animationiteration.animation,delete al.animationstart.animation),"TransitionEvent"in window||delete al.transitionend.transition);function xa(e){if(ys[e])return ys[e];if(!al[e])return e;var t=al[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in Yf)return ys[e]=t[a];return e}var $f=xa("animationend"),Qf=xa("animationiteration"),Gf=xa("animationstart"),X0=xa("transitionrun"),K0=xa("transitionstart"),F0=xa("transitioncancel"),Xf=xa("transitionend"),Kf=new Map,ps="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ps.push("scrollEnd");function Wt(e,t){Kf.set(e,t),va(t,[e])}var Ff=new WeakMap;function Qt(e,t){if(typeof e=="object"&&e!==null){var a=Ff.get(e);return a!==void 0?a:(t={value:e,source:t,stack:ff(t)},Ff.set(e,t),t)}return{value:e,source:t,stack:ff(t)}}var Gt=[],ll=0,vs=0;function hi(){for(var e=ll,t=vs=ll=0;t<e;){var a=Gt[t];Gt[t++]=null;var u=Gt[t];Gt[t++]=null;var r=Gt[t];Gt[t++]=null;var c=Gt[t];if(Gt[t++]=null,u!==null&&r!==null){var d=u.pending;d===null?r.next=r:(r.next=d.next,d.next=r),u.pending=r}c!==0&&Jf(a,r,c)}}function mi(e,t,a,u){Gt[ll++]=e,Gt[ll++]=t,Gt[ll++]=a,Gt[ll++]=u,vs|=u,e.lanes|=u,e=e.alternate,e!==null&&(e.lanes|=u)}function gs(e,t,a,u){return mi(e,t,a,u),yi(e)}function ul(e,t){return mi(e,null,null,t),yi(e)}function Jf(e,t,a){e.lanes|=a;var u=e.alternate;u!==null&&(u.lanes|=a);for(var r=!1,c=e.return;c!==null;)c.childLanes|=a,u=c.alternate,u!==null&&(u.childLanes|=a),c.tag===22&&(e=c.stateNode,e===null||e._visibility&1||(r=!0)),e=c,c=c.return;return e.tag===3?(c=e.stateNode,r&&t!==null&&(r=31-ke(a),e=c.hiddenUpdates,u=e[r],u===null?e[r]=[t]:u.push(t),t.lane=a|536870912),c):null}function yi(e){if(50<bu)throw bu=0,Ac=null,Error(s(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var il={};function J0(e,t,a,u){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ut(e,t,a,u){return new J0(e,t,a,u)}function bs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xn(e,t){var a=e.alternate;return a===null?(a=Ut(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Pf(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function pi(e,t,a,u,r,c){var d=0;if(u=e,typeof e=="function")bs(e)&&(d=1);else if(typeof e=="string")d=Wv(e,a,ie.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case be:return e=Ut(31,a,t,r),e.elementType=be,e.lanes=c,e;case C:return Sa(a.children,r,c,t);case M:d=8,r|=24;break;case G:return e=Ut(12,a,t,r|2),e.elementType=G,e.lanes=c,e;case F:return e=Ut(13,a,t,r),e.elementType=F,e.lanes=c,e;case ze:return e=Ut(19,a,t,r),e.elementType=ze,e.lanes=c,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Z:case Q:d=10;break e;case B:d=9;break e;case ee:d=11;break e;case Ee:d=14;break e;case Me:d=16,u=null;break e}d=29,a=Error(s(130,e===null?"null":typeof e,"")),u=null}return t=Ut(d,a,t,r),t.elementType=e,t.type=u,t.lanes=c,t}function Sa(e,t,a,u){return e=Ut(7,e,u,t),e.lanes=a,e}function xs(e,t,a){return e=Ut(6,e,null,t),e.lanes=a,e}function Ss(e,t,a){return t=Ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var rl=[],sl=0,vi=null,gi=0,Xt=[],Kt=0,_a=null,Sn=1,_n="";function Ea(e,t){rl[sl++]=gi,rl[sl++]=vi,vi=e,gi=t}function Wf(e,t,a){Xt[Kt++]=Sn,Xt[Kt++]=_n,Xt[Kt++]=_a,_a=e;var u=Sn;e=_n;var r=32-ke(u)-1;u&=~(1<<r),a+=1;var c=32-ke(t)+r;if(30<c){var d=r-r%5;c=(u&(1<<d)-1).toString(32),u>>=d,r-=d,Sn=1<<32-ke(t)+r|a<<r|u,_n=c+e}else Sn=1<<c|a<<r|u,_n=e}function _s(e){e.return!==null&&(Ea(e,1),Wf(e,1,0))}function Es(e){for(;e===vi;)vi=rl[--sl],rl[sl]=null,gi=rl[--sl],rl[sl]=null;for(;e===_a;)_a=Xt[--Kt],Xt[Kt]=null,_n=Xt[--Kt],Xt[Kt]=null,Sn=Xt[--Kt],Xt[Kt]=null}var St=null,Je=null,Ne=!1,Aa=null,sn=!1,As=Error(s(519));function wa(e){var t=Error(s(418,""));throw Pl(Qt(t,e)),As}function If(e){var t=e.stateNode,a=e.type,u=e.memoizedProps;switch(t[gt]=e,t[zt]=u,a){case"dialog":ge("cancel",t),ge("close",t);break;case"iframe":case"object":case"embed":ge("load",t);break;case"video":case"audio":for(a=0;a<Su.length;a++)ge(Su[a],t);break;case"source":ge("error",t);break;case"img":case"image":case"link":ge("error",t),ge("load",t);break;case"details":ge("toggle",t);break;case"input":ge("invalid",t),mf(t,u.value,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name,!0),li(t);break;case"select":ge("invalid",t);break;case"textarea":ge("invalid",t),pf(t,u.value,u.defaultValue,u.children),li(t)}a=u.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||u.suppressHydrationWarning===!0||pm(t.textContent,a)?(u.popover!=null&&(ge("beforetoggle",t),ge("toggle",t)),u.onScroll!=null&&ge("scroll",t),u.onScrollEnd!=null&&ge("scrollend",t),u.onClick!=null&&(t.onclick=Wi),t=!0):t=!1,t||wa(e)}function ed(e){for(St=e.return;St;)switch(St.tag){case 5:case 13:sn=!1;return;case 27:case 3:sn=!0;return;default:St=St.return}}function Fl(e){if(e!==St)return!1;if(!Ne)return ed(e),Ne=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||kc(e.type,e.memoizedProps)),a=!a),a&&Je&&wa(e),ed(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(s(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Je=en(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Je=null}}else t===27?(t=Je,la(e.type)?(e=$c,$c=null,Je=e):Je=t):Je=St?en(e.stateNode.nextSibling):null;return!0}function Jl(){Je=St=null,Ne=!1}function td(){var e=Aa;return e!==null&&(Rt===null?Rt=e:Rt.push.apply(Rt,e),Aa=null),e}function Pl(e){Aa===null?Aa=[e]:Aa.push(e)}var ws=Y(null),za=null,En=null;function Yn(e,t,a){P(ws,t._currentValue),t._currentValue=a}function An(e){e._currentValue=ws.current,I(ws)}function zs(e,t,a){for(;e!==null;){var u=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,u!==null&&(u.childLanes|=t)):u!==null&&(u.childLanes&t)!==t&&(u.childLanes|=t),e===a)break;e=e.return}}function Ts(e,t,a,u){var r=e.child;for(r!==null&&(r.return=e);r!==null;){var c=r.dependencies;if(c!==null){var d=r.child;c=c.firstContext;e:for(;c!==null;){var p=c;c=r;for(var b=0;b<t.length;b++)if(p.context===t[b]){c.lanes|=a,p=c.alternate,p!==null&&(p.lanes|=a),zs(c.return,a,e),u||(d=null);break e}c=p.next}}else if(r.tag===18){if(d=r.return,d===null)throw Error(s(341));d.lanes|=a,c=d.alternate,c!==null&&(c.lanes|=a),zs(d,a,e),d=null}else d=r.child;if(d!==null)d.return=r;else for(d=r;d!==null;){if(d===e){d=null;break}if(r=d.sibling,r!==null){r.return=d.return,d=r;break}d=d.return}r=d}}function Wl(e,t,a,u){e=null;for(var r=t,c=!1;r!==null;){if(!c){if((r.flags&524288)!==0)c=!0;else if((r.flags&262144)!==0)break}if(r.tag===10){var d=r.alternate;if(d===null)throw Error(s(387));if(d=d.memoizedProps,d!==null){var p=r.type;Ct(r.pendingProps.value,d.value)||(e!==null?e.push(p):e=[p])}}else if(r===mt.current){if(d=r.alternate,d===null)throw Error(s(387));d.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(e!==null?e.push(Tu):e=[Tu])}r=r.return}e!==null&&Ts(t,e,a,u),t.flags|=262144}function bi(e){for(e=e.firstContext;e!==null;){if(!Ct(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ta(e){za=e,En=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function bt(e){return nd(za,e)}function xi(e,t){return za===null&&Ta(e),nd(e,t)}function nd(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},En===null){if(e===null)throw Error(s(308));En=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else En=En.next=t;return a}var P0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,u){e.push(u)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},W0=n.unstable_scheduleCallback,I0=n.unstable_NormalPriority,it={$$typeof:Q,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Os(){return{controller:new P0,data:new Map,refCount:0}}function Il(e){e.refCount--,e.refCount===0&&W0(I0,function(){e.controller.abort()})}var eu=null,Ns=0,cl=0,ol=null;function ev(e,t){if(eu===null){var a=eu=[];Ns=0,cl=Dc(),ol={status:"pending",value:void 0,then:function(u){a.push(u)}}}return Ns++,t.then(ad,ad),t}function ad(){if(--Ns===0&&eu!==null){ol!==null&&(ol.status="fulfilled");var e=eu;eu=null,cl=0,ol=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function tv(e,t){var a=[],u={status:"pending",value:null,reason:null,then:function(r){a.push(r)}};return e.then(function(){u.status="fulfilled",u.value=t;for(var r=0;r<a.length;r++)(0,a[r])(t)},function(r){for(u.status="rejected",u.reason=r,r=0;r<a.length;r++)(0,a[r])(void 0)}),u}var ld=U.S;U.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&ev(e,t),ld!==null&&ld(e,t)};var Oa=Y(null);function Rs(){var e=Oa.current;return e!==null?e:Be.pooledCache}function Si(e,t){t===null?P(Oa,Oa.current):P(Oa,t.pool)}function ud(){var e=Rs();return e===null?null:{parent:it._currentValue,pool:e}}var tu=Error(s(460)),id=Error(s(474)),_i=Error(s(542)),Ds={then:function(){}};function rd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Ei(){}function sd(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(Ei,Ei),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,od(e),e;default:if(typeof t.status=="string")t.then(Ei,Ei);else{if(e=Be,e!==null&&100<e.shellSuspendCounter)throw Error(s(482));e=t,e.status="pending",e.then(function(u){if(t.status==="pending"){var r=t;r.status="fulfilled",r.value=u}},function(u){if(t.status==="pending"){var r=t;r.status="rejected",r.reason=u}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,od(e),e}throw nu=t,tu}}var nu=null;function cd(){if(nu===null)throw Error(s(459));var e=nu;return nu=null,e}function od(e){if(e===tu||e===_i)throw Error(s(483))}var $n=!1;function js(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ms(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Qn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Gn(e,t,a){var u=e.updateQueue;if(u===null)return null;if(u=u.shared,(De&2)!==0){var r=u.pending;return r===null?t.next=t:(t.next=r.next,r.next=t),u.pending=t,t=yi(e),Jf(e,null,a),t}return mi(e,u,t,a),yi(e)}function au(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var u=t.lanes;u&=e.pendingLanes,a|=u,t.lanes=a,nf(e,a)}}function Cs(e,t){var a=e.updateQueue,u=e.alternate;if(u!==null&&(u=u.updateQueue,a===u)){var r=null,c=null;if(a=a.firstBaseUpdate,a!==null){do{var d={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};c===null?r=c=d:c=c.next=d,a=a.next}while(a!==null);c===null?r=c=t:c=c.next=t}else r=c=t;a={baseState:u.baseState,firstBaseUpdate:r,lastBaseUpdate:c,shared:u.shared,callbacks:u.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Us=!1;function lu(){if(Us){var e=ol;if(e!==null)throw e}}function uu(e,t,a,u){Us=!1;var r=e.updateQueue;$n=!1;var c=r.firstBaseUpdate,d=r.lastBaseUpdate,p=r.shared.pending;if(p!==null){r.shared.pending=null;var b=p,O=b.next;b.next=null,d===null?c=O:d.next=O,d=b;var H=e.alternate;H!==null&&(H=H.updateQueue,p=H.lastBaseUpdate,p!==d&&(p===null?H.firstBaseUpdate=O:p.next=O,H.lastBaseUpdate=b))}if(c!==null){var V=r.baseState;d=0,H=O=b=null,p=c;do{var D=p.lane&-536870913,j=D!==p.lane;if(j?(we&D)===D:(u&D)===D){D!==0&&D===cl&&(Us=!0),H!==null&&(H=H.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var oe=e,re=p;D=t;var Ze=a;switch(re.tag){case 1:if(oe=re.payload,typeof oe=="function"){V=oe.call(Ze,V,D);break e}V=oe;break e;case 3:oe.flags=oe.flags&-65537|128;case 0:if(oe=re.payload,D=typeof oe=="function"?oe.call(Ze,V,D):oe,D==null)break e;V=x({},V,D);break e;case 2:$n=!0}}D=p.callback,D!==null&&(e.flags|=64,j&&(e.flags|=8192),j=r.callbacks,j===null?r.callbacks=[D]:j.push(D))}else j={lane:D,tag:p.tag,payload:p.payload,callback:p.callback,next:null},H===null?(O=H=j,b=V):H=H.next=j,d|=D;if(p=p.next,p===null){if(p=r.shared.pending,p===null)break;j=p,p=j.next,j.next=null,r.lastBaseUpdate=j,r.shared.pending=null}}while(!0);H===null&&(b=V),r.baseState=b,r.firstBaseUpdate=O,r.lastBaseUpdate=H,c===null&&(r.shared.lanes=0),ea|=d,e.lanes=d,e.memoizedState=V}}function fd(e,t){if(typeof e!="function")throw Error(s(191,e));e.call(t)}function dd(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)fd(a[e],t)}var fl=Y(null),Ai=Y(0);function hd(e,t){e=Dn,P(Ai,e),P(fl,t),Dn=e|t.baseLanes}function Zs(){P(Ai,Dn),P(fl,fl.current)}function Hs(){Dn=Ai.current,I(fl),I(Ai)}var Xn=0,ye=null,Ce=null,tt=null,wi=!1,dl=!1,Na=!1,zi=0,iu=0,hl=null,nv=0;function We(){throw Error(s(321))}function qs(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Ct(e[a],t[a]))return!1;return!0}function Ls(e,t,a,u,r,c){return Xn=c,ye=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,U.H=e===null||e.memoizedState===null?Jd:Pd,Na=!1,c=a(u,r),Na=!1,dl&&(c=yd(t,a,u,r)),md(e),c}function md(e){U.H=ji;var t=Ce!==null&&Ce.next!==null;if(Xn=0,tt=Ce=ye=null,wi=!1,iu=0,hl=null,t)throw Error(s(300));e===null||ct||(e=e.dependencies,e!==null&&bi(e)&&(ct=!0))}function yd(e,t,a,u){ye=e;var r=0;do{if(dl&&(hl=null),iu=0,dl=!1,25<=r)throw Error(s(301));if(r+=1,tt=Ce=null,e.updateQueue!=null){var c=e.updateQueue;c.lastEffect=null,c.events=null,c.stores=null,c.memoCache!=null&&(c.memoCache.index=0)}U.H=cv,c=t(a,u)}while(dl);return c}function av(){var e=U.H,t=e.useState()[0];return t=typeof t.then=="function"?ru(t):t,e=e.useState()[0],(Ce!==null?Ce.memoizedState:null)!==e&&(ye.flags|=1024),t}function ks(){var e=zi!==0;return zi=0,e}function Bs(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Vs(e){if(wi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}wi=!1}Xn=0,tt=Ce=ye=null,dl=!1,iu=zi=0,hl=null}function Ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return tt===null?ye.memoizedState=tt=e:tt=tt.next=e,tt}function nt(){if(Ce===null){var e=ye.alternate;e=e!==null?e.memoizedState:null}else e=Ce.next;var t=tt===null?ye.memoizedState:tt.next;if(t!==null)tt=t,Ce=e;else{if(e===null)throw ye.alternate===null?Error(s(467)):Error(s(310));Ce=e,e={memoizedState:Ce.memoizedState,baseState:Ce.baseState,baseQueue:Ce.baseQueue,queue:Ce.queue,next:null},tt===null?ye.memoizedState=tt=e:tt=tt.next=e}return tt}function Ys(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ru(e){var t=iu;return iu+=1,hl===null&&(hl=[]),e=sd(hl,e,t),t=ye,(tt===null?t.memoizedState:tt.next)===null&&(t=t.alternate,U.H=t===null||t.memoizedState===null?Jd:Pd),e}function Ti(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ru(e);if(e.$$typeof===Q)return bt(e)}throw Error(s(438,String(e)))}function $s(e){var t=null,a=ye.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var u=ye.alternate;u!==null&&(u=u.updateQueue,u!==null&&(u=u.memoCache,u!=null&&(t={data:u.data.map(function(r){return r.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Ys(),ye.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),u=0;u<e;u++)a[u]=Ge;return t.index++,a}function wn(e,t){return typeof t=="function"?t(e):t}function Oi(e){var t=nt();return Qs(t,Ce,e)}function Qs(e,t,a){var u=e.queue;if(u===null)throw Error(s(311));u.lastRenderedReducer=a;var r=e.baseQueue,c=u.pending;if(c!==null){if(r!==null){var d=r.next;r.next=c.next,c.next=d}t.baseQueue=r=c,u.pending=null}if(c=e.baseState,r===null)e.memoizedState=c;else{t=r.next;var p=d=null,b=null,O=t,H=!1;do{var V=O.lane&-536870913;if(V!==O.lane?(we&V)===V:(Xn&V)===V){var D=O.revertLane;if(D===0)b!==null&&(b=b.next={lane:0,revertLane:0,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null}),V===cl&&(H=!0);else if((Xn&D)===D){O=O.next,D===cl&&(H=!0);continue}else V={lane:0,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},b===null?(p=b=V,d=c):b=b.next=V,ye.lanes|=D,ea|=D;V=O.action,Na&&a(c,V),c=O.hasEagerState?O.eagerState:a(c,V)}else D={lane:V,revertLane:O.revertLane,action:O.action,hasEagerState:O.hasEagerState,eagerState:O.eagerState,next:null},b===null?(p=b=D,d=c):b=b.next=D,ye.lanes|=V,ea|=V;O=O.next}while(O!==null&&O!==t);if(b===null?d=c:b.next=p,!Ct(c,e.memoizedState)&&(ct=!0,H&&(a=ol,a!==null)))throw a;e.memoizedState=c,e.baseState=d,e.baseQueue=b,u.lastRenderedState=c}return r===null&&(u.lanes=0),[e.memoizedState,u.dispatch]}function Gs(e){var t=nt(),a=t.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=e;var u=a.dispatch,r=a.pending,c=t.memoizedState;if(r!==null){a.pending=null;var d=r=r.next;do c=e(c,d.action),d=d.next;while(d!==r);Ct(c,t.memoizedState)||(ct=!0),t.memoizedState=c,t.baseQueue===null&&(t.baseState=c),a.lastRenderedState=c}return[c,u]}function pd(e,t,a){var u=ye,r=nt(),c=Ne;if(c){if(a===void 0)throw Error(s(407));a=a()}else a=t();var d=!Ct((Ce||r).memoizedState,a);d&&(r.memoizedState=a,ct=!0),r=r.queue;var p=bd.bind(null,u,r,e);if(su(2048,8,p,[e]),r.getSnapshot!==t||d||tt!==null&&tt.memoizedState.tag&1){if(u.flags|=2048,ml(9,Ni(),gd.bind(null,u,r,a,t),null),Be===null)throw Error(s(349));c||(Xn&124)!==0||vd(u,t,a)}return a}function vd(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ye.updateQueue,t===null?(t=Ys(),ye.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function gd(e,t,a,u){t.value=a,t.getSnapshot=u,xd(t)&&Sd(e)}function bd(e,t,a){return a(function(){xd(t)&&Sd(e)})}function xd(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Ct(e,a)}catch{return!0}}function Sd(e){var t=ul(e,2);t!==null&&kt(t,e,2)}function Xs(e){var t=Ot();if(typeof e=="function"){var a=e;if(e=a(),Na){_e(!0);try{a()}finally{_e(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:wn,lastRenderedState:e},t}function _d(e,t,a,u){return e.baseState=a,Qs(e,Ce,typeof u=="function"?u:wn)}function lv(e,t,a,u,r){if(Di(e))throw Error(s(485));if(e=t.action,e!==null){var c={payload:r,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(d){c.listeners.push(d)}};U.T!==null?a(!0):c.isTransition=!1,u(c),a=t.pending,a===null?(c.next=t.pending=c,Ed(t,c)):(c.next=a.next,t.pending=a.next=c)}}function Ed(e,t){var a=t.action,u=t.payload,r=e.state;if(t.isTransition){var c=U.T,d={};U.T=d;try{var p=a(r,u),b=U.S;b!==null&&b(d,p),Ad(e,t,p)}catch(O){Ks(e,t,O)}finally{U.T=c}}else try{c=a(r,u),Ad(e,t,c)}catch(O){Ks(e,t,O)}}function Ad(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(u){wd(e,t,u)},function(u){return Ks(e,t,u)}):wd(e,t,a)}function wd(e,t,a){t.status="fulfilled",t.value=a,zd(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Ed(e,a)))}function Ks(e,t,a){var u=e.pending;if(e.pending=null,u!==null){u=u.next;do t.status="rejected",t.reason=a,zd(t),t=t.next;while(t!==u)}e.action=null}function zd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Td(e,t){return t}function Od(e,t){if(Ne){var a=Be.formState;if(a!==null){e:{var u=ye;if(Ne){if(Je){t:{for(var r=Je,c=sn;r.nodeType!==8;){if(!c){r=null;break t}if(r=en(r.nextSibling),r===null){r=null;break t}}c=r.data,r=c==="F!"||c==="F"?r:null}if(r){Je=en(r.nextSibling),u=r.data==="F!";break e}}wa(u)}u=!1}u&&(t=a[0])}}return a=Ot(),a.memoizedState=a.baseState=t,u={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Td,lastRenderedState:t},a.queue=u,a=Xd.bind(null,ye,u),u.dispatch=a,u=Xs(!1),c=Is.bind(null,ye,!1,u.queue),u=Ot(),r={state:t,dispatch:null,action:e,pending:null},u.queue=r,a=lv.bind(null,ye,r,c,a),r.dispatch=a,u.memoizedState=e,[t,a,!1]}function Nd(e){var t=nt();return Rd(t,Ce,e)}function Rd(e,t,a){if(t=Qs(e,t,Td)[0],e=Oi(wn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var u=ru(t)}catch(d){throw d===tu?_i:d}else u=t;t=nt();var r=t.queue,c=r.dispatch;return a!==t.memoizedState&&(ye.flags|=2048,ml(9,Ni(),uv.bind(null,r,a),null)),[u,c,e]}function uv(e,t){e.action=t}function Dd(e){var t=nt(),a=Ce;if(a!==null)return Rd(t,a,e);nt(),t=t.memoizedState,a=nt();var u=a.queue.dispatch;return a.memoizedState=e,[t,u,!1]}function ml(e,t,a,u){return e={tag:e,create:a,deps:u,inst:t,next:null},t=ye.updateQueue,t===null&&(t=Ys(),ye.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(u=a.next,a.next=e,e.next=u,t.lastEffect=e),e}function Ni(){return{destroy:void 0,resource:void 0}}function jd(){return nt().memoizedState}function Ri(e,t,a,u){var r=Ot();u=u===void 0?null:u,ye.flags|=e,r.memoizedState=ml(1|t,Ni(),a,u)}function su(e,t,a,u){var r=nt();u=u===void 0?null:u;var c=r.memoizedState.inst;Ce!==null&&u!==null&&qs(u,Ce.memoizedState.deps)?r.memoizedState=ml(t,c,a,u):(ye.flags|=e,r.memoizedState=ml(1|t,c,a,u))}function Md(e,t){Ri(8390656,8,e,t)}function Cd(e,t){su(2048,8,e,t)}function Ud(e,t){return su(4,2,e,t)}function Zd(e,t){return su(4,4,e,t)}function Hd(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function qd(e,t,a){a=a!=null?a.concat([e]):null,su(4,4,Hd.bind(null,t,e),a)}function Fs(){}function Ld(e,t){var a=nt();t=t===void 0?null:t;var u=a.memoizedState;return t!==null&&qs(t,u[1])?u[0]:(a.memoizedState=[e,t],e)}function kd(e,t){var a=nt();t=t===void 0?null:t;var u=a.memoizedState;if(t!==null&&qs(t,u[1]))return u[0];if(u=e(),Na){_e(!0);try{e()}finally{_e(!1)}}return a.memoizedState=[u,t],u}function Js(e,t,a){return a===void 0||(Xn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Yh(),ye.lanes|=e,ea|=e,a)}function Bd(e,t,a,u){return Ct(a,t)?a:fl.current!==null?(e=Js(e,a,u),Ct(e,t)||(ct=!0),e):(Xn&42)===0?(ct=!0,e.memoizedState=a):(e=Yh(),ye.lanes|=e,ea|=e,t)}function Vd(e,t,a,u,r){var c=K.p;K.p=c!==0&&8>c?c:8;var d=U.T,p={};U.T=p,Is(e,!1,t,a);try{var b=r(),O=U.S;if(O!==null&&O(p,b),b!==null&&typeof b=="object"&&typeof b.then=="function"){var H=tv(b,u);cu(e,t,H,Lt(e))}else cu(e,t,u,Lt(e))}catch(V){cu(e,t,{then:function(){},status:"rejected",reason:V},Lt())}finally{K.p=c,U.T=d}}function iv(){}function Ps(e,t,a,u){if(e.tag!==5)throw Error(s(476));var r=Yd(e).queue;Vd(e,r,t,ue,a===null?iv:function(){return $d(e),a(u)})}function Yd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ue,baseState:ue,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:wn,lastRenderedState:ue},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:wn,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function $d(e){var t=Yd(e).next.queue;cu(e,t,{},Lt())}function Ws(){return bt(Tu)}function Qd(){return nt().memoizedState}function Gd(){return nt().memoizedState}function rv(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Lt();e=Qn(a);var u=Gn(t,e,a);u!==null&&(kt(u,t,a),au(u,t,a)),t={cache:Os()},e.payload=t;return}t=t.return}}function sv(e,t,a){var u=Lt();a={lane:u,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Di(e)?Kd(t,a):(a=gs(e,t,a,u),a!==null&&(kt(a,e,u),Fd(a,t,u)))}function Xd(e,t,a){var u=Lt();cu(e,t,a,u)}function cu(e,t,a,u){var r={lane:u,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Di(e))Kd(t,r);else{var c=e.alternate;if(e.lanes===0&&(c===null||c.lanes===0)&&(c=t.lastRenderedReducer,c!==null))try{var d=t.lastRenderedState,p=c(d,a);if(r.hasEagerState=!0,r.eagerState=p,Ct(p,d))return mi(e,t,r,0),Be===null&&hi(),!1}catch{}finally{}if(a=gs(e,t,r,u),a!==null)return kt(a,e,u),Fd(a,t,u),!0}return!1}function Is(e,t,a,u){if(u={lane:2,revertLane:Dc(),action:u,hasEagerState:!1,eagerState:null,next:null},Di(e)){if(t)throw Error(s(479))}else t=gs(e,a,u,2),t!==null&&kt(t,e,2)}function Di(e){var t=e.alternate;return e===ye||t!==null&&t===ye}function Kd(e,t){dl=wi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Fd(e,t,a){if((a&4194048)!==0){var u=t.lanes;u&=e.pendingLanes,a|=u,t.lanes=a,nf(e,a)}}var ji={readContext:bt,use:Ti,useCallback:We,useContext:We,useEffect:We,useImperativeHandle:We,useLayoutEffect:We,useInsertionEffect:We,useMemo:We,useReducer:We,useRef:We,useState:We,useDebugValue:We,useDeferredValue:We,useTransition:We,useSyncExternalStore:We,useId:We,useHostTransitionStatus:We,useFormState:We,useActionState:We,useOptimistic:We,useMemoCache:We,useCacheRefresh:We},Jd={readContext:bt,use:Ti,useCallback:function(e,t){return Ot().memoizedState=[e,t===void 0?null:t],e},useContext:bt,useEffect:Md,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ri(4194308,4,Hd.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ri(4194308,4,e,t)},useInsertionEffect:function(e,t){Ri(4,2,e,t)},useMemo:function(e,t){var a=Ot();t=t===void 0?null:t;var u=e();if(Na){_e(!0);try{e()}finally{_e(!1)}}return a.memoizedState=[u,t],u},useReducer:function(e,t,a){var u=Ot();if(a!==void 0){var r=a(t);if(Na){_e(!0);try{a(t)}finally{_e(!1)}}}else r=t;return u.memoizedState=u.baseState=r,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},u.queue=e,e=e.dispatch=sv.bind(null,ye,e),[u.memoizedState,e]},useRef:function(e){var t=Ot();return e={current:e},t.memoizedState=e},useState:function(e){e=Xs(e);var t=e.queue,a=Xd.bind(null,ye,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Fs,useDeferredValue:function(e,t){var a=Ot();return Js(a,e,t)},useTransition:function(){var e=Xs(!1);return e=Vd.bind(null,ye,e.queue,!0,!1),Ot().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var u=ye,r=Ot();if(Ne){if(a===void 0)throw Error(s(407));a=a()}else{if(a=t(),Be===null)throw Error(s(349));(we&124)!==0||vd(u,t,a)}r.memoizedState=a;var c={value:a,getSnapshot:t};return r.queue=c,Md(bd.bind(null,u,c,e),[e]),u.flags|=2048,ml(9,Ni(),gd.bind(null,u,c,a,t),null),a},useId:function(){var e=Ot(),t=Be.identifierPrefix;if(Ne){var a=_n,u=Sn;a=(u&~(1<<32-ke(u)-1)).toString(32)+a,t="«"+t+"R"+a,a=zi++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=nv++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ws,useFormState:Od,useActionState:Od,useOptimistic:function(e){var t=Ot();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Is.bind(null,ye,!0,a),a.dispatch=t,[e,t]},useMemoCache:$s,useCacheRefresh:function(){return Ot().memoizedState=rv.bind(null,ye)}},Pd={readContext:bt,use:Ti,useCallback:Ld,useContext:bt,useEffect:Cd,useImperativeHandle:qd,useInsertionEffect:Ud,useLayoutEffect:Zd,useMemo:kd,useReducer:Oi,useRef:jd,useState:function(){return Oi(wn)},useDebugValue:Fs,useDeferredValue:function(e,t){var a=nt();return Bd(a,Ce.memoizedState,e,t)},useTransition:function(){var e=Oi(wn)[0],t=nt().memoizedState;return[typeof e=="boolean"?e:ru(e),t]},useSyncExternalStore:pd,useId:Qd,useHostTransitionStatus:Ws,useFormState:Nd,useActionState:Nd,useOptimistic:function(e,t){var a=nt();return _d(a,Ce,e,t)},useMemoCache:$s,useCacheRefresh:Gd},cv={readContext:bt,use:Ti,useCallback:Ld,useContext:bt,useEffect:Cd,useImperativeHandle:qd,useInsertionEffect:Ud,useLayoutEffect:Zd,useMemo:kd,useReducer:Gs,useRef:jd,useState:function(){return Gs(wn)},useDebugValue:Fs,useDeferredValue:function(e,t){var a=nt();return Ce===null?Js(a,e,t):Bd(a,Ce.memoizedState,e,t)},useTransition:function(){var e=Gs(wn)[0],t=nt().memoizedState;return[typeof e=="boolean"?e:ru(e),t]},useSyncExternalStore:pd,useId:Qd,useHostTransitionStatus:Ws,useFormState:Dd,useActionState:Dd,useOptimistic:function(e,t){var a=nt();return Ce!==null?_d(a,Ce,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:$s,useCacheRefresh:Gd},yl=null,ou=0;function Mi(e){var t=ou;return ou+=1,yl===null&&(yl=[]),sd(yl,e,t)}function fu(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ci(e,t){throw t.$$typeof===w?Error(s(525)):(e=Object.prototype.toString.call(t),Error(s(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Wd(e){var t=e._init;return t(e._payload)}function Id(e){function t(z,E){if(e){var T=z.deletions;T===null?(z.deletions=[E],z.flags|=16):T.push(E)}}function a(z,E){if(!e)return null;for(;E!==null;)t(z,E),E=E.sibling;return null}function u(z){for(var E=new Map;z!==null;)z.key!==null?E.set(z.key,z):E.set(z.index,z),z=z.sibling;return E}function r(z,E){return z=xn(z,E),z.index=0,z.sibling=null,z}function c(z,E,T){return z.index=T,e?(T=z.alternate,T!==null?(T=T.index,T<E?(z.flags|=67108866,E):T):(z.flags|=67108866,E)):(z.flags|=1048576,E)}function d(z){return e&&z.alternate===null&&(z.flags|=67108866),z}function p(z,E,T,k){return E===null||E.tag!==6?(E=xs(T,z.mode,k),E.return=z,E):(E=r(E,T),E.return=z,E)}function b(z,E,T,k){var te=T.type;return te===C?H(z,E,T.props.children,k,T.key):E!==null&&(E.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===Me&&Wd(te)===E.type)?(E=r(E,T.props),fu(E,T),E.return=z,E):(E=pi(T.type,T.key,T.props,null,z.mode,k),fu(E,T),E.return=z,E)}function O(z,E,T,k){return E===null||E.tag!==4||E.stateNode.containerInfo!==T.containerInfo||E.stateNode.implementation!==T.implementation?(E=Ss(T,z.mode,k),E.return=z,E):(E=r(E,T.children||[]),E.return=z,E)}function H(z,E,T,k,te){return E===null||E.tag!==7?(E=Sa(T,z.mode,k,te),E.return=z,E):(E=r(E,T),E.return=z,E)}function V(z,E,T){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=xs(""+E,z.mode,T),E.return=z,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case A:return T=pi(E.type,E.key,E.props,null,z.mode,T),fu(T,E),T.return=z,T;case L:return E=Ss(E,z.mode,T),E.return=z,E;case Me:var k=E._init;return E=k(E._payload),V(z,E,T)}if(Te(E)||ce(E))return E=Sa(E,z.mode,T,null),E.return=z,E;if(typeof E.then=="function")return V(z,Mi(E),T);if(E.$$typeof===Q)return V(z,xi(z,E),T);Ci(z,E)}return null}function D(z,E,T,k){var te=E!==null?E.key:null;if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return te!==null?null:p(z,E,""+T,k);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case A:return T.key===te?b(z,E,T,k):null;case L:return T.key===te?O(z,E,T,k):null;case Me:return te=T._init,T=te(T._payload),D(z,E,T,k)}if(Te(T)||ce(T))return te!==null?null:H(z,E,T,k,null);if(typeof T.then=="function")return D(z,E,Mi(T),k);if(T.$$typeof===Q)return D(z,E,xi(z,T),k);Ci(z,T)}return null}function j(z,E,T,k,te){if(typeof k=="string"&&k!==""||typeof k=="number"||typeof k=="bigint")return z=z.get(T)||null,p(E,z,""+k,te);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case A:return z=z.get(k.key===null?T:k.key)||null,b(E,z,k,te);case L:return z=z.get(k.key===null?T:k.key)||null,O(E,z,k,te);case Me:var pe=k._init;return k=pe(k._payload),j(z,E,T,k,te)}if(Te(k)||ce(k))return z=z.get(T)||null,H(E,z,k,te,null);if(typeof k.then=="function")return j(z,E,T,Mi(k),te);if(k.$$typeof===Q)return j(z,E,T,xi(E,k),te);Ci(E,k)}return null}function oe(z,E,T,k){for(var te=null,pe=null,le=E,se=E=0,ft=null;le!==null&&se<T.length;se++){le.index>se?(ft=le,le=null):ft=le.sibling;var Oe=D(z,le,T[se],k);if(Oe===null){le===null&&(le=ft);break}e&&le&&Oe.alternate===null&&t(z,le),E=c(Oe,E,se),pe===null?te=Oe:pe.sibling=Oe,pe=Oe,le=ft}if(se===T.length)return a(z,le),Ne&&Ea(z,se),te;if(le===null){for(;se<T.length;se++)le=V(z,T[se],k),le!==null&&(E=c(le,E,se),pe===null?te=le:pe.sibling=le,pe=le);return Ne&&Ea(z,se),te}for(le=u(le);se<T.length;se++)ft=j(le,z,se,T[se],k),ft!==null&&(e&&ft.alternate!==null&&le.delete(ft.key===null?se:ft.key),E=c(ft,E,se),pe===null?te=ft:pe.sibling=ft,pe=ft);return e&&le.forEach(function(ca){return t(z,ca)}),Ne&&Ea(z,se),te}function re(z,E,T,k){if(T==null)throw Error(s(151));for(var te=null,pe=null,le=E,se=E=0,ft=null,Oe=T.next();le!==null&&!Oe.done;se++,Oe=T.next()){le.index>se?(ft=le,le=null):ft=le.sibling;var ca=D(z,le,Oe.value,k);if(ca===null){le===null&&(le=ft);break}e&&le&&ca.alternate===null&&t(z,le),E=c(ca,E,se),pe===null?te=ca:pe.sibling=ca,pe=ca,le=ft}if(Oe.done)return a(z,le),Ne&&Ea(z,se),te;if(le===null){for(;!Oe.done;se++,Oe=T.next())Oe=V(z,Oe.value,k),Oe!==null&&(E=c(Oe,E,se),pe===null?te=Oe:pe.sibling=Oe,pe=Oe);return Ne&&Ea(z,se),te}for(le=u(le);!Oe.done;se++,Oe=T.next())Oe=j(le,z,se,Oe.value,k),Oe!==null&&(e&&Oe.alternate!==null&&le.delete(Oe.key===null?se:Oe.key),E=c(Oe,E,se),pe===null?te=Oe:pe.sibling=Oe,pe=Oe);return e&&le.forEach(function(og){return t(z,og)}),Ne&&Ea(z,se),te}function Ze(z,E,T,k){if(typeof T=="object"&&T!==null&&T.type===C&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case A:e:{for(var te=T.key;E!==null;){if(E.key===te){if(te=T.type,te===C){if(E.tag===7){a(z,E.sibling),k=r(E,T.props.children),k.return=z,z=k;break e}}else if(E.elementType===te||typeof te=="object"&&te!==null&&te.$$typeof===Me&&Wd(te)===E.type){a(z,E.sibling),k=r(E,T.props),fu(k,T),k.return=z,z=k;break e}a(z,E);break}else t(z,E);E=E.sibling}T.type===C?(k=Sa(T.props.children,z.mode,k,T.key),k.return=z,z=k):(k=pi(T.type,T.key,T.props,null,z.mode,k),fu(k,T),k.return=z,z=k)}return d(z);case L:e:{for(te=T.key;E!==null;){if(E.key===te)if(E.tag===4&&E.stateNode.containerInfo===T.containerInfo&&E.stateNode.implementation===T.implementation){a(z,E.sibling),k=r(E,T.children||[]),k.return=z,z=k;break e}else{a(z,E);break}else t(z,E);E=E.sibling}k=Ss(T,z.mode,k),k.return=z,z=k}return d(z);case Me:return te=T._init,T=te(T._payload),Ze(z,E,T,k)}if(Te(T))return oe(z,E,T,k);if(ce(T)){if(te=ce(T),typeof te!="function")throw Error(s(150));return T=te.call(T),re(z,E,T,k)}if(typeof T.then=="function")return Ze(z,E,Mi(T),k);if(T.$$typeof===Q)return Ze(z,E,xi(z,T),k);Ci(z,T)}return typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint"?(T=""+T,E!==null&&E.tag===6?(a(z,E.sibling),k=r(E,T),k.return=z,z=k):(a(z,E),k=xs(T,z.mode,k),k.return=z,z=k),d(z)):a(z,E)}return function(z,E,T,k){try{ou=0;var te=Ze(z,E,T,k);return yl=null,te}catch(le){if(le===tu||le===_i)throw le;var pe=Ut(29,le,null,z.mode);return pe.lanes=k,pe.return=z,pe}finally{}}}var pl=Id(!0),eh=Id(!1),Ft=Y(null),cn=null;function Kn(e){var t=e.alternate;P(rt,rt.current&1),P(Ft,e),cn===null&&(t===null||fl.current!==null||t.memoizedState!==null)&&(cn=e)}function th(e){if(e.tag===22){if(P(rt,rt.current),P(Ft,e),cn===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(cn=e)}}else Fn()}function Fn(){P(rt,rt.current),P(Ft,Ft.current)}function zn(e){I(Ft),cn===e&&(cn=null),I(rt)}var rt=Y(0);function Ui(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Yc(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function ec(e,t,a,u){t=e.memoizedState,a=a(u,t),a=a==null?t:x({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var tc={enqueueSetState:function(e,t,a){e=e._reactInternals;var u=Lt(),r=Qn(u);r.payload=t,a!=null&&(r.callback=a),t=Gn(e,r,u),t!==null&&(kt(t,e,u),au(t,e,u))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var u=Lt(),r=Qn(u);r.tag=1,r.payload=t,a!=null&&(r.callback=a),t=Gn(e,r,u),t!==null&&(kt(t,e,u),au(t,e,u))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Lt(),u=Qn(a);u.tag=2,t!=null&&(u.callback=t),t=Gn(e,u,a),t!==null&&(kt(t,e,a),au(t,e,a))}};function nh(e,t,a,u,r,c,d){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(u,c,d):t.prototype&&t.prototype.isPureReactComponent?!Xl(a,u)||!Xl(r,c):!0}function ah(e,t,a,u){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,u),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,u),t.state!==e&&tc.enqueueReplaceState(t,t.state,null)}function Ra(e,t){var a=t;if("ref"in t){a={};for(var u in t)u!=="ref"&&(a[u]=t[u])}if(e=e.defaultProps){a===t&&(a=x({},a));for(var r in e)a[r]===void 0&&(a[r]=e[r])}return a}var Zi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function lh(e){Zi(e)}function uh(e){console.error(e)}function ih(e){Zi(e)}function Hi(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(u){setTimeout(function(){throw u})}}function rh(e,t,a){try{var u=e.onCaughtError;u(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function nc(e,t,a){return a=Qn(a),a.tag=3,a.payload={element:null},a.callback=function(){Hi(e,t)},a}function sh(e){return e=Qn(e),e.tag=3,e}function ch(e,t,a,u){var r=a.type.getDerivedStateFromError;if(typeof r=="function"){var c=u.value;e.payload=function(){return r(c)},e.callback=function(){rh(t,a,u)}}var d=a.stateNode;d!==null&&typeof d.componentDidCatch=="function"&&(e.callback=function(){rh(t,a,u),typeof r!="function"&&(ta===null?ta=new Set([this]):ta.add(this));var p=u.stack;this.componentDidCatch(u.value,{componentStack:p!==null?p:""})})}function ov(e,t,a,u,r){if(a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){if(t=a.alternate,t!==null&&Wl(t,a,r,!0),a=Ft.current,a!==null){switch(a.tag){case 13:return cn===null?zc():a.alternate===null&&Pe===0&&(Pe=3),a.flags&=-257,a.flags|=65536,a.lanes=r,u===Ds?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([u]):t.add(u),Oc(e,u,r)),!1;case 22:return a.flags|=65536,u===Ds?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([u])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([u]):a.add(u)),Oc(e,u,r)),!1}throw Error(s(435,a.tag))}return Oc(e,u,r),zc(),!1}if(Ne)return t=Ft.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=r,u!==As&&(e=Error(s(422),{cause:u}),Pl(Qt(e,a)))):(u!==As&&(t=Error(s(423),{cause:u}),Pl(Qt(t,a))),e=e.current.alternate,e.flags|=65536,r&=-r,e.lanes|=r,u=Qt(u,a),r=nc(e.stateNode,u,r),Cs(e,r),Pe!==4&&(Pe=2)),!1;var c=Error(s(520),{cause:u});if(c=Qt(c,a),gu===null?gu=[c]:gu.push(c),Pe!==4&&(Pe=2),t===null)return!0;u=Qt(u,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=r&-r,a.lanes|=e,e=nc(a.stateNode,u,e),Cs(a,e),!1;case 1:if(t=a.type,c=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||c!==null&&typeof c.componentDidCatch=="function"&&(ta===null||!ta.has(c))))return a.flags|=65536,r&=-r,a.lanes|=r,r=sh(r),ch(r,e,a,u),Cs(a,r),!1}a=a.return}while(a!==null);return!1}var oh=Error(s(461)),ct=!1;function yt(e,t,a,u){t.child=e===null?eh(t,null,a,u):pl(t,e.child,a,u)}function fh(e,t,a,u,r){a=a.render;var c=t.ref;if("ref"in u){var d={};for(var p in u)p!=="ref"&&(d[p]=u[p])}else d=u;return Ta(t),u=Ls(e,t,a,d,c,r),p=ks(),e!==null&&!ct?(Bs(e,t,r),Tn(e,t,r)):(Ne&&p&&_s(t),t.flags|=1,yt(e,t,u,r),t.child)}function dh(e,t,a,u,r){if(e===null){var c=a.type;return typeof c=="function"&&!bs(c)&&c.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=c,hh(e,t,c,u,r)):(e=pi(a.type,null,u,t,t.mode,r),e.ref=t.ref,e.return=t,t.child=e)}if(c=e.child,!oc(e,r)){var d=c.memoizedProps;if(a=a.compare,a=a!==null?a:Xl,a(d,u)&&e.ref===t.ref)return Tn(e,t,r)}return t.flags|=1,e=xn(c,u),e.ref=t.ref,e.return=t,t.child=e}function hh(e,t,a,u,r){if(e!==null){var c=e.memoizedProps;if(Xl(c,u)&&e.ref===t.ref)if(ct=!1,t.pendingProps=u=c,oc(e,r))(e.flags&131072)!==0&&(ct=!0);else return t.lanes=e.lanes,Tn(e,t,r)}return ac(e,t,a,u,r)}function mh(e,t,a){var u=t.pendingProps,r=u.children,c=e!==null?e.memoizedState:null;if(u.mode==="hidden"){if((t.flags&128)!==0){if(u=c!==null?c.baseLanes|a:a,e!==null){for(r=t.child=e.child,c=0;r!==null;)c=c|r.lanes|r.childLanes,r=r.sibling;t.childLanes=c&~u}else t.childLanes=0,t.child=null;return yh(e,t,u,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&Si(t,c!==null?c.cachePool:null),c!==null?hd(t,c):Zs(),th(t);else return t.lanes=t.childLanes=536870912,yh(e,t,c!==null?c.baseLanes|a:a,a)}else c!==null?(Si(t,c.cachePool),hd(t,c),Fn(),t.memoizedState=null):(e!==null&&Si(t,null),Zs(),Fn());return yt(e,t,r,a),t.child}function yh(e,t,a,u){var r=Rs();return r=r===null?null:{parent:it._currentValue,pool:r},t.memoizedState={baseLanes:a,cachePool:r},e!==null&&Si(t,null),Zs(),th(t),e!==null&&Wl(e,t,u,!0),null}function qi(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(s(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function ac(e,t,a,u,r){return Ta(t),a=Ls(e,t,a,u,void 0,r),u=ks(),e!==null&&!ct?(Bs(e,t,r),Tn(e,t,r)):(Ne&&u&&_s(t),t.flags|=1,yt(e,t,a,r),t.child)}function ph(e,t,a,u,r,c){return Ta(t),t.updateQueue=null,a=yd(t,u,a,r),md(e),u=ks(),e!==null&&!ct?(Bs(e,t,c),Tn(e,t,c)):(Ne&&u&&_s(t),t.flags|=1,yt(e,t,a,c),t.child)}function vh(e,t,a,u,r){if(Ta(t),t.stateNode===null){var c=il,d=a.contextType;typeof d=="object"&&d!==null&&(c=bt(d)),c=new a(u,c),t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,c.updater=tc,t.stateNode=c,c._reactInternals=t,c=t.stateNode,c.props=u,c.state=t.memoizedState,c.refs={},js(t),d=a.contextType,c.context=typeof d=="object"&&d!==null?bt(d):il,c.state=t.memoizedState,d=a.getDerivedStateFromProps,typeof d=="function"&&(ec(t,a,d,u),c.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(d=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),d!==c.state&&tc.enqueueReplaceState(c,c.state,null),uu(t,u,c,r),lu(),c.state=t.memoizedState),typeof c.componentDidMount=="function"&&(t.flags|=4194308),u=!0}else if(e===null){c=t.stateNode;var p=t.memoizedProps,b=Ra(a,p);c.props=b;var O=c.context,H=a.contextType;d=il,typeof H=="object"&&H!==null&&(d=bt(H));var V=a.getDerivedStateFromProps;H=typeof V=="function"||typeof c.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,H||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(p||O!==d)&&ah(t,c,u,d),$n=!1;var D=t.memoizedState;c.state=D,uu(t,u,c,r),lu(),O=t.memoizedState,p||D!==O||$n?(typeof V=="function"&&(ec(t,a,V,u),O=t.memoizedState),(b=$n||nh(t,a,b,u,D,O,d))?(H||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount()),typeof c.componentDidMount=="function"&&(t.flags|=4194308)):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=u,t.memoizedState=O),c.props=u,c.state=O,c.context=d,u=b):(typeof c.componentDidMount=="function"&&(t.flags|=4194308),u=!1)}else{c=t.stateNode,Ms(e,t),d=t.memoizedProps,H=Ra(a,d),c.props=H,V=t.pendingProps,D=c.context,O=a.contextType,b=il,typeof O=="object"&&O!==null&&(b=bt(O)),p=a.getDerivedStateFromProps,(O=typeof p=="function"||typeof c.getSnapshotBeforeUpdate=="function")||typeof c.UNSAFE_componentWillReceiveProps!="function"&&typeof c.componentWillReceiveProps!="function"||(d!==V||D!==b)&&ah(t,c,u,b),$n=!1,D=t.memoizedState,c.state=D,uu(t,u,c,r),lu();var j=t.memoizedState;d!==V||D!==j||$n||e!==null&&e.dependencies!==null&&bi(e.dependencies)?(typeof p=="function"&&(ec(t,a,p,u),j=t.memoizedState),(H=$n||nh(t,a,H,u,D,j,b)||e!==null&&e.dependencies!==null&&bi(e.dependencies))?(O||typeof c.UNSAFE_componentWillUpdate!="function"&&typeof c.componentWillUpdate!="function"||(typeof c.componentWillUpdate=="function"&&c.componentWillUpdate(u,j,b),typeof c.UNSAFE_componentWillUpdate=="function"&&c.UNSAFE_componentWillUpdate(u,j,b)),typeof c.componentDidUpdate=="function"&&(t.flags|=4),typeof c.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),t.memoizedProps=u,t.memoizedState=j),c.props=u,c.state=j,c.context=b,u=H):(typeof c.componentDidUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=4),typeof c.getSnapshotBeforeUpdate!="function"||d===e.memoizedProps&&D===e.memoizedState||(t.flags|=1024),u=!1)}return c=u,qi(e,t),u=(t.flags&128)!==0,c||u?(c=t.stateNode,a=u&&typeof a.getDerivedStateFromError!="function"?null:c.render(),t.flags|=1,e!==null&&u?(t.child=pl(t,e.child,null,r),t.child=pl(t,null,a,r)):yt(e,t,a,r),t.memoizedState=c.state,e=t.child):e=Tn(e,t,r),e}function gh(e,t,a,u){return Jl(),t.flags|=256,yt(e,t,a,u),t.child}var lc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function uc(e){return{baseLanes:e,cachePool:ud()}}function ic(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=Jt),e}function bh(e,t,a){var u=t.pendingProps,r=!1,c=(t.flags&128)!==0,d;if((d=c)||(d=e!==null&&e.memoizedState===null?!1:(rt.current&2)!==0),d&&(r=!0,t.flags&=-129),d=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ne){if(r?Kn(t):Fn(),Ne){var p=Je,b;if(b=p){e:{for(b=p,p=sn;b.nodeType!==8;){if(!p){p=null;break e}if(b=en(b.nextSibling),b===null){p=null;break e}}p=b}p!==null?(t.memoizedState={dehydrated:p,treeContext:_a!==null?{id:Sn,overflow:_n}:null,retryLane:536870912,hydrationErrors:null},b=Ut(18,null,null,0),b.stateNode=p,b.return=t,t.child=b,St=t,Je=null,b=!0):b=!1}b||wa(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return Yc(p)?t.lanes=32:t.lanes=536870912,null;zn(t)}return p=u.children,u=u.fallback,r?(Fn(),r=t.mode,p=Li({mode:"hidden",children:p},r),u=Sa(u,r,a,null),p.return=t,u.return=t,p.sibling=u,t.child=p,r=t.child,r.memoizedState=uc(a),r.childLanes=ic(e,d,a),t.memoizedState=lc,u):(Kn(t),rc(t,p))}if(b=e.memoizedState,b!==null&&(p=b.dehydrated,p!==null)){if(c)t.flags&256?(Kn(t),t.flags&=-257,t=sc(e,t,a)):t.memoizedState!==null?(Fn(),t.child=e.child,t.flags|=128,t=null):(Fn(),r=u.fallback,p=t.mode,u=Li({mode:"visible",children:u.children},p),r=Sa(r,p,a,null),r.flags|=2,u.return=t,r.return=t,u.sibling=r,t.child=u,pl(t,e.child,null,a),u=t.child,u.memoizedState=uc(a),u.childLanes=ic(e,d,a),t.memoizedState=lc,t=r);else if(Kn(t),Yc(p)){if(d=p.nextSibling&&p.nextSibling.dataset,d)var O=d.dgst;d=O,u=Error(s(419)),u.stack="",u.digest=d,Pl({value:u,source:null,stack:null}),t=sc(e,t,a)}else if(ct||Wl(e,t,a,!1),d=(a&e.childLanes)!==0,ct||d){if(d=Be,d!==null&&(u=a&-a,u=(u&42)!==0?1:$r(u),u=(u&(d.suspendedLanes|a))!==0?0:u,u!==0&&u!==b.retryLane))throw b.retryLane=u,ul(e,u),kt(d,e,u),oh;p.data==="$?"||zc(),t=sc(e,t,a)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=b.treeContext,Je=en(p.nextSibling),St=t,Ne=!0,Aa=null,sn=!1,e!==null&&(Xt[Kt++]=Sn,Xt[Kt++]=_n,Xt[Kt++]=_a,Sn=e.id,_n=e.overflow,_a=t),t=rc(t,u.children),t.flags|=4096);return t}return r?(Fn(),r=u.fallback,p=t.mode,b=e.child,O=b.sibling,u=xn(b,{mode:"hidden",children:u.children}),u.subtreeFlags=b.subtreeFlags&65011712,O!==null?r=xn(O,r):(r=Sa(r,p,a,null),r.flags|=2),r.return=t,u.return=t,u.sibling=r,t.child=u,u=r,r=t.child,p=e.child.memoizedState,p===null?p=uc(a):(b=p.cachePool,b!==null?(O=it._currentValue,b=b.parent!==O?{parent:O,pool:O}:b):b=ud(),p={baseLanes:p.baseLanes|a,cachePool:b}),r.memoizedState=p,r.childLanes=ic(e,d,a),t.memoizedState=lc,u):(Kn(t),a=e.child,e=a.sibling,a=xn(a,{mode:"visible",children:u.children}),a.return=t,a.sibling=null,e!==null&&(d=t.deletions,d===null?(t.deletions=[e],t.flags|=16):d.push(e)),t.child=a,t.memoizedState=null,a)}function rc(e,t){return t=Li({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Li(e,t){return e=Ut(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function sc(e,t,a){return pl(t,e.child,null,a),e=rc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function xh(e,t,a){e.lanes|=t;var u=e.alternate;u!==null&&(u.lanes|=t),zs(e.return,t,a)}function cc(e,t,a,u,r){var c=e.memoizedState;c===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:u,tail:a,tailMode:r}:(c.isBackwards=t,c.rendering=null,c.renderingStartTime=0,c.last=u,c.tail=a,c.tailMode=r)}function Sh(e,t,a){var u=t.pendingProps,r=u.revealOrder,c=u.tail;if(yt(e,t,u.children,a),u=rt.current,(u&2)!==0)u=u&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xh(e,a,t);else if(e.tag===19)xh(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}u&=1}switch(P(rt,u),r){case"forwards":for(a=t.child,r=null;a!==null;)e=a.alternate,e!==null&&Ui(e)===null&&(r=a),a=a.sibling;a=r,a===null?(r=t.child,t.child=null):(r=a.sibling,a.sibling=null),cc(t,!1,r,a,c);break;case"backwards":for(a=null,r=t.child,t.child=null;r!==null;){if(e=r.alternate,e!==null&&Ui(e)===null){t.child=r;break}e=r.sibling,r.sibling=a,a=r,r=e}cc(t,!0,a,null,c);break;case"together":cc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Tn(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),ea|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Wl(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(s(153));if(t.child!==null){for(e=t.child,a=xn(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=xn(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function oc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&bi(e)))}function fv(e,t,a){switch(t.tag){case 3:He(t,t.stateNode.containerInfo),Yn(t,it,e.memoizedState.cache),Jl();break;case 27:case 5:ma(t);break;case 4:He(t,t.stateNode.containerInfo);break;case 10:Yn(t,t.type,t.memoizedProps.value);break;case 13:var u=t.memoizedState;if(u!==null)return u.dehydrated!==null?(Kn(t),t.flags|=128,null):(a&t.child.childLanes)!==0?bh(e,t,a):(Kn(t),e=Tn(e,t,a),e!==null?e.sibling:null);Kn(t);break;case 19:var r=(e.flags&128)!==0;if(u=(a&t.childLanes)!==0,u||(Wl(e,t,a,!1),u=(a&t.childLanes)!==0),r){if(u)return Sh(e,t,a);t.flags|=128}if(r=t.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),P(rt,rt.current),u)break;return null;case 22:case 23:return t.lanes=0,mh(e,t,a);case 24:Yn(t,it,e.memoizedState.cache)}return Tn(e,t,a)}function _h(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)ct=!0;else{if(!oc(e,a)&&(t.flags&128)===0)return ct=!1,fv(e,t,a);ct=(e.flags&131072)!==0}else ct=!1,Ne&&(t.flags&1048576)!==0&&Wf(t,gi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var u=t.elementType,r=u._init;if(u=r(u._payload),t.type=u,typeof u=="function")bs(u)?(e=Ra(u,e),t.tag=1,t=vh(null,t,u,e,a)):(t.tag=0,t=ac(null,t,u,e,a));else{if(u!=null){if(r=u.$$typeof,r===ee){t.tag=11,t=fh(null,t,u,e,a);break e}else if(r===Ee){t.tag=14,t=dh(null,t,u,e,a);break e}}throw t=Re(u)||u,Error(s(306,t,""))}}return t;case 0:return ac(e,t,t.type,t.pendingProps,a);case 1:return u=t.type,r=Ra(u,t.pendingProps),vh(e,t,u,r,a);case 3:e:{if(He(t,t.stateNode.containerInfo),e===null)throw Error(s(387));u=t.pendingProps;var c=t.memoizedState;r=c.element,Ms(e,t),uu(t,u,null,a);var d=t.memoizedState;if(u=d.cache,Yn(t,it,u),u!==c.cache&&Ts(t,[it],a,!0),lu(),u=d.element,c.isDehydrated)if(c={element:u,isDehydrated:!1,cache:d.cache},t.updateQueue.baseState=c,t.memoizedState=c,t.flags&256){t=gh(e,t,u,a);break e}else if(u!==r){r=Qt(Error(s(424)),t),Pl(r),t=gh(e,t,u,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Je=en(e.firstChild),St=t,Ne=!0,Aa=null,sn=!0,a=eh(t,null,u,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Jl(),u===r){t=Tn(e,t,a);break e}yt(e,t,u,a)}t=t.child}return t;case 26:return qi(e,t),e===null?(a=zm(t.type,null,t.pendingProps,null))?t.memoizedState=a:Ne||(a=t.type,e=t.pendingProps,u=Ii(fe.current).createElement(a),u[gt]=t,u[zt]=e,vt(u,a,e),st(u),t.stateNode=u):t.memoizedState=zm(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ma(t),e===null&&Ne&&(u=t.stateNode=Em(t.type,t.pendingProps,fe.current),St=t,sn=!0,r=Je,la(t.type)?($c=r,Je=en(u.firstChild)):Je=r),yt(e,t,t.pendingProps.children,a),qi(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ne&&((r=u=Je)&&(u=Lv(u,t.type,t.pendingProps,sn),u!==null?(t.stateNode=u,St=t,Je=en(u.firstChild),sn=!1,r=!0):r=!1),r||wa(t)),ma(t),r=t.type,c=t.pendingProps,d=e!==null?e.memoizedProps:null,u=c.children,kc(r,c)?u=null:d!==null&&kc(r,d)&&(t.flags|=32),t.memoizedState!==null&&(r=Ls(e,t,av,null,null,a),Tu._currentValue=r),qi(e,t),yt(e,t,u,a),t.child;case 6:return e===null&&Ne&&((e=a=Je)&&(a=kv(a,t.pendingProps,sn),a!==null?(t.stateNode=a,St=t,Je=null,e=!0):e=!1),e||wa(t)),null;case 13:return bh(e,t,a);case 4:return He(t,t.stateNode.containerInfo),u=t.pendingProps,e===null?t.child=pl(t,null,u,a):yt(e,t,u,a),t.child;case 11:return fh(e,t,t.type,t.pendingProps,a);case 7:return yt(e,t,t.pendingProps,a),t.child;case 8:return yt(e,t,t.pendingProps.children,a),t.child;case 12:return yt(e,t,t.pendingProps.children,a),t.child;case 10:return u=t.pendingProps,Yn(t,t.type,u.value),yt(e,t,u.children,a),t.child;case 9:return r=t.type._context,u=t.pendingProps.children,Ta(t),r=bt(r),u=u(r),t.flags|=1,yt(e,t,u,a),t.child;case 14:return dh(e,t,t.type,t.pendingProps,a);case 15:return hh(e,t,t.type,t.pendingProps,a);case 19:return Sh(e,t,a);case 31:return u=t.pendingProps,a=t.mode,u={mode:u.mode,children:u.children},e===null?(a=Li(u,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=xn(e.child,u),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return mh(e,t,a);case 24:return Ta(t),u=bt(it),e===null?(r=Rs(),r===null&&(r=Be,c=Os(),r.pooledCache=c,c.refCount++,c!==null&&(r.pooledCacheLanes|=a),r=c),t.memoizedState={parent:u,cache:r},js(t),Yn(t,it,r)):((e.lanes&a)!==0&&(Ms(e,t),uu(t,null,null,a),lu()),r=e.memoizedState,c=t.memoizedState,r.parent!==u?(r={parent:u,cache:u},t.memoizedState=r,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=r),Yn(t,it,u)):(u=c.cache,Yn(t,it,u),u!==r.cache&&Ts(t,[it],a,!0))),yt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function On(e){e.flags|=4}function Eh(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Dm(t)){if(t=Ft.current,t!==null&&((we&4194048)===we?cn!==null:(we&62914560)!==we&&(we&536870912)===0||t!==cn))throw nu=Ds,id;e.flags|=8192}}function ki(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ef():536870912,e.lanes|=t,xl|=t)}function du(e,t){if(!Ne)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var u=null;a!==null;)a.alternate!==null&&(u=a),a=a.sibling;u===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:u.sibling=null}}function Ke(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,u=0;if(t)for(var r=e.child;r!==null;)a|=r.lanes|r.childLanes,u|=r.subtreeFlags&65011712,u|=r.flags&65011712,r.return=e,r=r.sibling;else for(r=e.child;r!==null;)a|=r.lanes|r.childLanes,u|=r.subtreeFlags,u|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=u,e.childLanes=a,t}function dv(e,t,a){var u=t.pendingProps;switch(Es(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ke(t),null;case 1:return Ke(t),null;case 3:return a=t.stateNode,u=null,e!==null&&(u=e.memoizedState.cache),t.memoizedState.cache!==u&&(t.flags|=2048),An(it),Bt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Fl(t)?On(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,td())),Ke(t),null;case 26:return a=t.memoizedState,e===null?(On(t),a!==null?(Ke(t),Eh(t,a)):(Ke(t),t.flags&=-16777217)):a?a!==e.memoizedState?(On(t),Ke(t),Eh(t,a)):(Ke(t),t.flags&=-16777217):(e.memoizedProps!==u&&On(t),Ke(t),t.flags&=-16777217),null;case 27:Ba(t),a=fe.current;var r=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==u&&On(t);else{if(!u){if(t.stateNode===null)throw Error(s(166));return Ke(t),null}e=ie.current,Fl(t)?If(t):(e=Em(r,u,a),t.stateNode=e,On(t))}return Ke(t),null;case 5:if(Ba(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==u&&On(t);else{if(!u){if(t.stateNode===null)throw Error(s(166));return Ke(t),null}if(e=ie.current,Fl(t))If(t);else{switch(r=Ii(fe.current),e){case 1:e=r.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=r.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=r.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=r.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=r.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof u.is=="string"?r.createElement("select",{is:u.is}):r.createElement("select"),u.multiple?e.multiple=!0:u.size&&(e.size=u.size);break;default:e=typeof u.is=="string"?r.createElement(a,{is:u.is}):r.createElement(a)}}e[gt]=t,e[zt]=u;e:for(r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.tag!==27&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break e;for(;r.sibling===null;){if(r.return===null||r.return===t)break e;r=r.return}r.sibling.return=r.return,r=r.sibling}t.stateNode=e;e:switch(vt(e,a,u),a){case"button":case"input":case"select":case"textarea":e=!!u.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&On(t)}}return Ke(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==u&&On(t);else{if(typeof u!="string"&&t.stateNode===null)throw Error(s(166));if(e=fe.current,Fl(t)){if(e=t.stateNode,a=t.memoizedProps,u=null,r=St,r!==null)switch(r.tag){case 27:case 5:u=r.memoizedProps}e[gt]=t,e=!!(e.nodeValue===a||u!==null&&u.suppressHydrationWarning===!0||pm(e.nodeValue,a)),e||wa(t)}else e=Ii(e).createTextNode(u),e[gt]=t,t.stateNode=e}return Ke(t),null;case 13:if(u=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(r=Fl(t),u!==null&&u.dehydrated!==null){if(e===null){if(!r)throw Error(s(318));if(r=t.memoizedState,r=r!==null?r.dehydrated:null,!r)throw Error(s(317));r[gt]=t}else Jl(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ke(t),r=!1}else r=td(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=r),r=!0;if(!r)return t.flags&256?(zn(t),t):(zn(t),null)}if(zn(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=u!==null,e=e!==null&&e.memoizedState!==null,a){u=t.child,r=null,u.alternate!==null&&u.alternate.memoizedState!==null&&u.alternate.memoizedState.cachePool!==null&&(r=u.alternate.memoizedState.cachePool.pool);var c=null;u.memoizedState!==null&&u.memoizedState.cachePool!==null&&(c=u.memoizedState.cachePool.pool),c!==r&&(u.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),ki(t,t.updateQueue),Ke(t),null;case 4:return Bt(),e===null&&Uc(t.stateNode.containerInfo),Ke(t),null;case 10:return An(t.type),Ke(t),null;case 19:if(I(rt),r=t.memoizedState,r===null)return Ke(t),null;if(u=(t.flags&128)!==0,c=r.rendering,c===null)if(u)du(r,!1);else{if(Pe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(c=Ui(e),c!==null){for(t.flags|=128,du(r,!1),e=c.updateQueue,t.updateQueue=e,ki(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Pf(a,e),a=a.sibling;return P(rt,rt.current&1|2),t.child}e=e.sibling}r.tail!==null&&Vt()>Yi&&(t.flags|=128,u=!0,du(r,!1),t.lanes=4194304)}else{if(!u)if(e=Ui(c),e!==null){if(t.flags|=128,u=!0,e=e.updateQueue,t.updateQueue=e,ki(t,e),du(r,!0),r.tail===null&&r.tailMode==="hidden"&&!c.alternate&&!Ne)return Ke(t),null}else 2*Vt()-r.renderingStartTime>Yi&&a!==536870912&&(t.flags|=128,u=!0,du(r,!1),t.lanes=4194304);r.isBackwards?(c.sibling=t.child,t.child=c):(e=r.last,e!==null?e.sibling=c:t.child=c,r.last=c)}return r.tail!==null?(t=r.tail,r.rendering=t,r.tail=t.sibling,r.renderingStartTime=Vt(),t.sibling=null,e=rt.current,P(rt,u?e&1|2:e&1),t):(Ke(t),null);case 22:case 23:return zn(t),Hs(),u=t.memoizedState!==null,e!==null?e.memoizedState!==null!==u&&(t.flags|=8192):u&&(t.flags|=8192),u?(a&536870912)!==0&&(t.flags&128)===0&&(Ke(t),t.subtreeFlags&6&&(t.flags|=8192)):Ke(t),a=t.updateQueue,a!==null&&ki(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),u=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(u=t.memoizedState.cachePool.pool),u!==a&&(t.flags|=2048),e!==null&&I(Oa),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),An(it),Ke(t),null;case 25:return null;case 30:return null}throw Error(s(156,t.tag))}function hv(e,t){switch(Es(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return An(it),Bt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ba(t),null;case 13:if(zn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(s(340));Jl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return I(rt),null;case 4:return Bt(),null;case 10:return An(t.type),null;case 22:case 23:return zn(t),Hs(),e!==null&&I(Oa),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return An(it),null;case 25:return null;default:return null}}function Ah(e,t){switch(Es(t),t.tag){case 3:An(it),Bt();break;case 26:case 27:case 5:Ba(t);break;case 4:Bt();break;case 13:zn(t);break;case 19:I(rt);break;case 10:An(t.type);break;case 22:case 23:zn(t),Hs(),e!==null&&I(Oa);break;case 24:An(it)}}function hu(e,t){try{var a=t.updateQueue,u=a!==null?a.lastEffect:null;if(u!==null){var r=u.next;a=r;do{if((a.tag&e)===e){u=void 0;var c=a.create,d=a.inst;u=c(),d.destroy=u}a=a.next}while(a!==r)}}catch(p){qe(t,t.return,p)}}function Jn(e,t,a){try{var u=t.updateQueue,r=u!==null?u.lastEffect:null;if(r!==null){var c=r.next;u=c;do{if((u.tag&e)===e){var d=u.inst,p=d.destroy;if(p!==void 0){d.destroy=void 0,r=t;var b=a,O=p;try{O()}catch(H){qe(r,b,H)}}}u=u.next}while(u!==c)}}catch(H){qe(t,t.return,H)}}function wh(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{dd(t,a)}catch(u){qe(e,e.return,u)}}}function zh(e,t,a){a.props=Ra(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(u){qe(e,t,u)}}function mu(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var u=e.stateNode;break;case 30:u=e.stateNode;break;default:u=e.stateNode}typeof a=="function"?e.refCleanup=a(u):a.current=u}}catch(r){qe(e,t,r)}}function on(e,t){var a=e.ref,u=e.refCleanup;if(a!==null)if(typeof u=="function")try{u()}catch(r){qe(e,t,r)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(r){qe(e,t,r)}else a.current=null}function Th(e){var t=e.type,a=e.memoizedProps,u=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&u.focus();break e;case"img":a.src?u.src=a.src:a.srcSet&&(u.srcset=a.srcSet)}}catch(r){qe(e,e.return,r)}}function fc(e,t,a){try{var u=e.stateNode;Cv(u,e.type,a,t),u[zt]=t}catch(r){qe(e,e.return,r)}}function Oh(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&la(e.type)||e.tag===4}function dc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Oh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&la(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hc(e,t,a){var u=e.tag;if(u===5||u===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Wi));else if(u!==4&&(u===27&&la(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(hc(e,t,a),e=e.sibling;e!==null;)hc(e,t,a),e=e.sibling}function Bi(e,t,a){var u=e.tag;if(u===5||u===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(u!==4&&(u===27&&la(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Bi(e,t,a),e=e.sibling;e!==null;)Bi(e,t,a),e=e.sibling}function Nh(e){var t=e.stateNode,a=e.memoizedProps;try{for(var u=e.type,r=t.attributes;r.length;)t.removeAttributeNode(r[0]);vt(t,u,a),t[gt]=e,t[zt]=a}catch(c){qe(e,e.return,c)}}var Nn=!1,Ie=!1,mc=!1,Rh=typeof WeakSet=="function"?WeakSet:Set,ot=null;function mv(e,t){if(e=e.containerInfo,qc=ur,e=Bf(e),ds(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var u=a.getSelection&&a.getSelection();if(u&&u.rangeCount!==0){a=u.anchorNode;var r=u.anchorOffset,c=u.focusNode;u=u.focusOffset;try{a.nodeType,c.nodeType}catch{a=null;break e}var d=0,p=-1,b=-1,O=0,H=0,V=e,D=null;t:for(;;){for(var j;V!==a||r!==0&&V.nodeType!==3||(p=d+r),V!==c||u!==0&&V.nodeType!==3||(b=d+u),V.nodeType===3&&(d+=V.nodeValue.length),(j=V.firstChild)!==null;)D=V,V=j;for(;;){if(V===e)break t;if(D===a&&++O===r&&(p=d),D===c&&++H===u&&(b=d),(j=V.nextSibling)!==null)break;V=D,D=V.parentNode}V=j}a=p===-1||b===-1?null:{start:p,end:b}}else a=null}a=a||{start:0,end:0}}else a=null;for(Lc={focusedElem:e,selectionRange:a},ur=!1,ot=t;ot!==null;)if(t=ot,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,ot=e;else for(;ot!==null;){switch(t=ot,c=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&c!==null){e=void 0,a=t,r=c.memoizedProps,c=c.memoizedState,u=a.stateNode;try{var oe=Ra(a.type,r,a.elementType===a.type);e=u.getSnapshotBeforeUpdate(oe,c),u.__reactInternalSnapshotBeforeUpdate=e}catch(re){qe(a,a.return,re)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Vc(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Vc(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(s(163))}if(e=t.sibling,e!==null){e.return=t.return,ot=e;break}ot=t.return}}function Dh(e,t,a){var u=a.flags;switch(a.tag){case 0:case 11:case 15:Pn(e,a),u&4&&hu(5,a);break;case 1:if(Pn(e,a),u&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(d){qe(a,a.return,d)}else{var r=Ra(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(r,t,e.__reactInternalSnapshotBeforeUpdate)}catch(d){qe(a,a.return,d)}}u&64&&wh(a),u&512&&mu(a,a.return);break;case 3:if(Pn(e,a),u&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{dd(e,t)}catch(d){qe(a,a.return,d)}}break;case 27:t===null&&u&4&&Nh(a);case 26:case 5:Pn(e,a),t===null&&u&4&&Th(a),u&512&&mu(a,a.return);break;case 12:Pn(e,a);break;case 13:Pn(e,a),u&4&&Ch(e,a),u&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Ev.bind(null,a),Bv(e,a))));break;case 22:if(u=a.memoizedState!==null||Nn,!u){t=t!==null&&t.memoizedState!==null||Ie,r=Nn;var c=Ie;Nn=u,(Ie=t)&&!c?Wn(e,a,(a.subtreeFlags&8772)!==0):Pn(e,a),Nn=r,Ie=c}break;case 30:break;default:Pn(e,a)}}function jh(e){var t=e.alternate;t!==null&&(e.alternate=null,jh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Xr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ye=null,Nt=!1;function Rn(e,t,a){for(a=a.child;a!==null;)Mh(e,t,a),a=a.sibling}function Mh(e,t,a){if(he&&typeof he.onCommitFiberUnmount=="function")try{he.onCommitFiberUnmount(ae,a)}catch{}switch(a.tag){case 26:Ie||on(a,t),Rn(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ie||on(a,t);var u=Ye,r=Nt;la(a.type)&&(Ye=a.stateNode,Nt=!1),Rn(e,t,a),Eu(a.stateNode),Ye=u,Nt=r;break;case 5:Ie||on(a,t);case 6:if(u=Ye,r=Nt,Ye=null,Rn(e,t,a),Ye=u,Nt=r,Ye!==null)if(Nt)try{(Ye.nodeType===9?Ye.body:Ye.nodeName==="HTML"?Ye.ownerDocument.body:Ye).removeChild(a.stateNode)}catch(c){qe(a,t,c)}else try{Ye.removeChild(a.stateNode)}catch(c){qe(a,t,c)}break;case 18:Ye!==null&&(Nt?(e=Ye,Sm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Du(e)):Sm(Ye,a.stateNode));break;case 4:u=Ye,r=Nt,Ye=a.stateNode.containerInfo,Nt=!0,Rn(e,t,a),Ye=u,Nt=r;break;case 0:case 11:case 14:case 15:Ie||Jn(2,a,t),Ie||Jn(4,a,t),Rn(e,t,a);break;case 1:Ie||(on(a,t),u=a.stateNode,typeof u.componentWillUnmount=="function"&&zh(a,t,u)),Rn(e,t,a);break;case 21:Rn(e,t,a);break;case 22:Ie=(u=Ie)||a.memoizedState!==null,Rn(e,t,a),Ie=u;break;default:Rn(e,t,a)}}function Ch(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Du(e)}catch(a){qe(t,t.return,a)}}function yv(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Rh),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Rh),t;default:throw Error(s(435,e.tag))}}function yc(e,t){var a=yv(e);t.forEach(function(u){var r=Av.bind(null,e,u);a.has(u)||(a.add(u),u.then(r,r))})}function Zt(e,t){var a=t.deletions;if(a!==null)for(var u=0;u<a.length;u++){var r=a[u],c=e,d=t,p=d;e:for(;p!==null;){switch(p.tag){case 27:if(la(p.type)){Ye=p.stateNode,Nt=!1;break e}break;case 5:Ye=p.stateNode,Nt=!1;break e;case 3:case 4:Ye=p.stateNode.containerInfo,Nt=!0;break e}p=p.return}if(Ye===null)throw Error(s(160));Mh(c,d,r),Ye=null,Nt=!1,c=r.alternate,c!==null&&(c.return=null),r.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Uh(t,e),t=t.sibling}var It=null;function Uh(e,t){var a=e.alternate,u=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Zt(t,e),Ht(e),u&4&&(Jn(3,e,e.return),hu(3,e),Jn(5,e,e.return));break;case 1:Zt(t,e),Ht(e),u&512&&(Ie||a===null||on(a,a.return)),u&64&&Nn&&(e=e.updateQueue,e!==null&&(u=e.callbacks,u!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?u:a.concat(u))));break;case 26:var r=It;if(Zt(t,e),Ht(e),u&512&&(Ie||a===null||on(a,a.return)),u&4){var c=a!==null?a.memoizedState:null;if(u=e.memoizedState,a===null)if(u===null)if(e.stateNode===null){e:{u=e.type,a=e.memoizedProps,r=r.ownerDocument||r;t:switch(u){case"title":c=r.getElementsByTagName("title")[0],(!c||c[ql]||c[gt]||c.namespaceURI==="http://www.w3.org/2000/svg"||c.hasAttribute("itemprop"))&&(c=r.createElement(u),r.head.insertBefore(c,r.querySelector("head > title"))),vt(c,u,a),c[gt]=e,st(c),u=c;break e;case"link":var d=Nm("link","href",r).get(u+(a.href||""));if(d){for(var p=0;p<d.length;p++)if(c=d[p],c.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&c.getAttribute("rel")===(a.rel==null?null:a.rel)&&c.getAttribute("title")===(a.title==null?null:a.title)&&c.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){d.splice(p,1);break t}}c=r.createElement(u),vt(c,u,a),r.head.appendChild(c);break;case"meta":if(d=Nm("meta","content",r).get(u+(a.content||""))){for(p=0;p<d.length;p++)if(c=d[p],c.getAttribute("content")===(a.content==null?null:""+a.content)&&c.getAttribute("name")===(a.name==null?null:a.name)&&c.getAttribute("property")===(a.property==null?null:a.property)&&c.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&c.getAttribute("charset")===(a.charSet==null?null:a.charSet)){d.splice(p,1);break t}}c=r.createElement(u),vt(c,u,a),r.head.appendChild(c);break;default:throw Error(s(468,u))}c[gt]=e,st(c),u=c}e.stateNode=u}else Rm(r,e.type,e.stateNode);else e.stateNode=Om(r,u,e.memoizedProps);else c!==u?(c===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):c.count--,u===null?Rm(r,e.type,e.stateNode):Om(r,u,e.memoizedProps)):u===null&&e.stateNode!==null&&fc(e,e.memoizedProps,a.memoizedProps)}break;case 27:Zt(t,e),Ht(e),u&512&&(Ie||a===null||on(a,a.return)),a!==null&&u&4&&fc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Zt(t,e),Ht(e),u&512&&(Ie||a===null||on(a,a.return)),e.flags&32){r=e.stateNode;try{Wa(r,"")}catch(j){qe(e,e.return,j)}}u&4&&e.stateNode!=null&&(r=e.memoizedProps,fc(e,r,a!==null?a.memoizedProps:r)),u&1024&&(mc=!0);break;case 6:if(Zt(t,e),Ht(e),u&4){if(e.stateNode===null)throw Error(s(162));u=e.memoizedProps,a=e.stateNode;try{a.nodeValue=u}catch(j){qe(e,e.return,j)}}break;case 3:if(nr=null,r=It,It=er(t.containerInfo),Zt(t,e),It=r,Ht(e),u&4&&a!==null&&a.memoizedState.isDehydrated)try{Du(t.containerInfo)}catch(j){qe(e,e.return,j)}mc&&(mc=!1,Zh(e));break;case 4:u=It,It=er(e.stateNode.containerInfo),Zt(t,e),Ht(e),It=u;break;case 12:Zt(t,e),Ht(e);break;case 13:Zt(t,e),Ht(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Sc=Vt()),u&4&&(u=e.updateQueue,u!==null&&(e.updateQueue=null,yc(e,u)));break;case 22:r=e.memoizedState!==null;var b=a!==null&&a.memoizedState!==null,O=Nn,H=Ie;if(Nn=O||r,Ie=H||b,Zt(t,e),Ie=H,Nn=O,Ht(e),u&8192)e:for(t=e.stateNode,t._visibility=r?t._visibility&-2:t._visibility|1,r&&(a===null||b||Nn||Ie||Da(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){b=a=t;try{if(c=b.stateNode,r)d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none";else{p=b.stateNode;var V=b.memoizedProps.style,D=V!=null&&V.hasOwnProperty("display")?V.display:null;p.style.display=D==null||typeof D=="boolean"?"":(""+D).trim()}}catch(j){qe(b,b.return,j)}}}else if(t.tag===6){if(a===null){b=t;try{b.stateNode.nodeValue=r?"":b.memoizedProps}catch(j){qe(b,b.return,j)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}u&4&&(u=e.updateQueue,u!==null&&(a=u.retryQueue,a!==null&&(u.retryQueue=null,yc(e,a))));break;case 19:Zt(t,e),Ht(e),u&4&&(u=e.updateQueue,u!==null&&(e.updateQueue=null,yc(e,u)));break;case 30:break;case 21:break;default:Zt(t,e),Ht(e)}}function Ht(e){var t=e.flags;if(t&2){try{for(var a,u=e.return;u!==null;){if(Oh(u)){a=u;break}u=u.return}if(a==null)throw Error(s(160));switch(a.tag){case 27:var r=a.stateNode,c=dc(e);Bi(e,c,r);break;case 5:var d=a.stateNode;a.flags&32&&(Wa(d,""),a.flags&=-33);var p=dc(e);Bi(e,p,d);break;case 3:case 4:var b=a.stateNode.containerInfo,O=dc(e);hc(e,O,b);break;default:throw Error(s(161))}}catch(H){qe(e,e.return,H)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zh(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Zh(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Pn(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Dh(e,t.alternate,t),t=t.sibling}function Da(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Jn(4,t,t.return),Da(t);break;case 1:on(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&zh(t,t.return,a),Da(t);break;case 27:Eu(t.stateNode);case 26:case 5:on(t,t.return),Da(t);break;case 22:t.memoizedState===null&&Da(t);break;case 30:Da(t);break;default:Da(t)}e=e.sibling}}function Wn(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var u=t.alternate,r=e,c=t,d=c.flags;switch(c.tag){case 0:case 11:case 15:Wn(r,c,a),hu(4,c);break;case 1:if(Wn(r,c,a),u=c,r=u.stateNode,typeof r.componentDidMount=="function")try{r.componentDidMount()}catch(O){qe(u,u.return,O)}if(u=c,r=u.updateQueue,r!==null){var p=u.stateNode;try{var b=r.shared.hiddenCallbacks;if(b!==null)for(r.shared.hiddenCallbacks=null,r=0;r<b.length;r++)fd(b[r],p)}catch(O){qe(u,u.return,O)}}a&&d&64&&wh(c),mu(c,c.return);break;case 27:Nh(c);case 26:case 5:Wn(r,c,a),a&&u===null&&d&4&&Th(c),mu(c,c.return);break;case 12:Wn(r,c,a);break;case 13:Wn(r,c,a),a&&d&4&&Ch(r,c);break;case 22:c.memoizedState===null&&Wn(r,c,a),mu(c,c.return);break;case 30:break;default:Wn(r,c,a)}t=t.sibling}}function pc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Il(a))}function vc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Il(e))}function fn(e,t,a,u){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Hh(e,t,a,u),t=t.sibling}function Hh(e,t,a,u){var r=t.flags;switch(t.tag){case 0:case 11:case 15:fn(e,t,a,u),r&2048&&hu(9,t);break;case 1:fn(e,t,a,u);break;case 3:fn(e,t,a,u),r&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Il(e)));break;case 12:if(r&2048){fn(e,t,a,u),e=t.stateNode;try{var c=t.memoizedProps,d=c.id,p=c.onPostCommit;typeof p=="function"&&p(d,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(b){qe(t,t.return,b)}}else fn(e,t,a,u);break;case 13:fn(e,t,a,u);break;case 23:break;case 22:c=t.stateNode,d=t.alternate,t.memoizedState!==null?c._visibility&2?fn(e,t,a,u):yu(e,t):c._visibility&2?fn(e,t,a,u):(c._visibility|=2,vl(e,t,a,u,(t.subtreeFlags&10256)!==0)),r&2048&&pc(d,t);break;case 24:fn(e,t,a,u),r&2048&&vc(t.alternate,t);break;default:fn(e,t,a,u)}}function vl(e,t,a,u,r){for(r=r&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var c=e,d=t,p=a,b=u,O=d.flags;switch(d.tag){case 0:case 11:case 15:vl(c,d,p,b,r),hu(8,d);break;case 23:break;case 22:var H=d.stateNode;d.memoizedState!==null?H._visibility&2?vl(c,d,p,b,r):yu(c,d):(H._visibility|=2,vl(c,d,p,b,r)),r&&O&2048&&pc(d.alternate,d);break;case 24:vl(c,d,p,b,r),r&&O&2048&&vc(d.alternate,d);break;default:vl(c,d,p,b,r)}t=t.sibling}}function yu(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,u=t,r=u.flags;switch(u.tag){case 22:yu(a,u),r&2048&&pc(u.alternate,u);break;case 24:yu(a,u),r&2048&&vc(u.alternate,u);break;default:yu(a,u)}t=t.sibling}}var pu=8192;function gl(e){if(e.subtreeFlags&pu)for(e=e.child;e!==null;)qh(e),e=e.sibling}function qh(e){switch(e.tag){case 26:gl(e),e.flags&pu&&e.memoizedState!==null&&eg(It,e.memoizedState,e.memoizedProps);break;case 5:gl(e);break;case 3:case 4:var t=It;It=er(e.stateNode.containerInfo),gl(e),It=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=pu,pu=16777216,gl(e),pu=t):gl(e));break;default:gl(e)}}function Lh(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function vu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var u=t[a];ot=u,Bh(u,e)}Lh(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)kh(e),e=e.sibling}function kh(e){switch(e.tag){case 0:case 11:case 15:vu(e),e.flags&2048&&Jn(9,e,e.return);break;case 3:vu(e);break;case 12:vu(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Vi(e)):vu(e);break;default:vu(e)}}function Vi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var u=t[a];ot=u,Bh(u,e)}Lh(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Jn(8,t,t.return),Vi(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Vi(t));break;default:Vi(t)}e=e.sibling}}function Bh(e,t){for(;ot!==null;){var a=ot;switch(a.tag){case 0:case 11:case 15:Jn(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var u=a.memoizedState.cachePool.pool;u!=null&&u.refCount++}break;case 24:Il(a.memoizedState.cache)}if(u=a.child,u!==null)u.return=a,ot=u;else e:for(a=e;ot!==null;){u=ot;var r=u.sibling,c=u.return;if(jh(u),u===a){ot=null;break e}if(r!==null){r.return=c,ot=r;break e}ot=c}}}var pv={getCacheForType:function(e){var t=bt(it),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},vv=typeof WeakMap=="function"?WeakMap:Map,De=0,Be=null,ve=null,we=0,je=0,qt=null,In=!1,bl=!1,gc=!1,Dn=0,Pe=0,ea=0,ja=0,bc=0,Jt=0,xl=0,gu=null,Rt=null,xc=!1,Sc=0,Yi=1/0,$i=null,ta=null,pt=0,na=null,Sl=null,_l=0,_c=0,Ec=null,Vh=null,bu=0,Ac=null;function Lt(){if((De&2)!==0&&we!==0)return we&-we;if(U.T!==null){var e=cl;return e!==0?e:Dc()}return af()}function Yh(){Jt===0&&(Jt=(we&536870912)===0||Ne?Io():536870912);var e=Ft.current;return e!==null&&(e.flags|=32),Jt}function kt(e,t,a){(e===Be&&(je===2||je===9)||e.cancelPendingCommit!==null)&&(El(e,0),aa(e,we,Jt,!1)),Hl(e,a),((De&2)===0||e!==Be)&&(e===Be&&((De&2)===0&&(ja|=a),Pe===4&&aa(e,we,Jt,!1)),dn(e))}function $h(e,t,a){if((De&6)!==0)throw Error(s(327));var u=!a&&(t&124)===0&&(t&e.expiredLanes)===0||pa(e,t),r=u?xv(e,t):Tc(e,t,!0),c=u;do{if(r===0){bl&&!u&&aa(e,t,0,!1);break}else{if(a=e.current.alternate,c&&!gv(a)){r=Tc(e,t,!1),c=!1;continue}if(r===2){if(c=t,e.errorRecoveryDisabledLanes&c)var d=0;else d=e.pendingLanes&-536870913,d=d!==0?d:d&536870912?536870912:0;if(d!==0){t=d;e:{var p=e;r=gu;var b=p.current.memoizedState.isDehydrated;if(b&&(El(p,d).flags|=256),d=Tc(p,d,!1),d!==2){if(gc&&!b){p.errorRecoveryDisabledLanes|=c,ja|=c,r=4;break e}c=Rt,Rt=r,c!==null&&(Rt===null?Rt=c:Rt.push.apply(Rt,c))}r=d}if(c=!1,r!==2)continue}}if(r===1){El(e,0),aa(e,t,0,!0);break}e:{switch(u=e,c=r,c){case 0:case 1:throw Error(s(345));case 4:if((t&4194048)!==t)break;case 6:aa(u,t,Jt,!In);break e;case 2:Rt=null;break;case 3:case 5:break;default:throw Error(s(329))}if((t&62914560)===t&&(r=Sc+300-Vt(),10<r)){if(aa(u,t,Jt,!In),ya(u,0,!0)!==0)break e;u.timeoutHandle=bm(Qh.bind(null,u,a,Rt,$i,xc,t,Jt,ja,xl,In,c,2,-0,0),r);break e}Qh(u,a,Rt,$i,xc,t,Jt,ja,xl,In,c,0,-0,0)}}break}while(!0);dn(e)}function Qh(e,t,a,u,r,c,d,p,b,O,H,V,D,j){if(e.timeoutHandle=-1,V=t.subtreeFlags,(V&8192||(V&16785408)===16785408)&&(zu={stylesheets:null,count:0,unsuspend:Iv},qh(t),V=tg(),V!==null)){e.cancelPendingCommit=V(Wh.bind(null,e,t,c,a,u,r,d,p,b,H,1,D,j)),aa(e,c,d,!O);return}Wh(e,t,c,a,u,r,d,p,b)}function gv(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var u=0;u<a.length;u++){var r=a[u],c=r.getSnapshot;r=r.value;try{if(!Ct(c(),r))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function aa(e,t,a,u){t&=~bc,t&=~ja,e.suspendedLanes|=t,e.pingedLanes&=~t,u&&(e.warmLanes|=t),u=e.expirationTimes;for(var r=t;0<r;){var c=31-ke(r),d=1<<c;u[c]=-1,r&=~d}a!==0&&tf(e,a,t)}function Qi(){return(De&6)===0?(xu(0),!1):!0}function wc(){if(ve!==null){if(je===0)var e=ve.return;else e=ve,En=za=null,Vs(e),yl=null,ou=0,e=ve;for(;e!==null;)Ah(e.alternate,e),e=e.return;ve=null}}function El(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,Zv(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),wc(),Be=e,ve=a=xn(e.current,null),we=t,je=0,qt=null,In=!1,bl=pa(e,t),gc=!1,xl=Jt=bc=ja=ea=Pe=0,Rt=gu=null,xc=!1,(t&8)!==0&&(t|=t&32);var u=e.entangledLanes;if(u!==0)for(e=e.entanglements,u&=t;0<u;){var r=31-ke(u),c=1<<r;t|=e[r],u&=~c}return Dn=t,hi(),a}function Gh(e,t){ye=null,U.H=ji,t===tu||t===_i?(t=cd(),je=3):t===id?(t=cd(),je=4):je=t===oh?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,qt=t,ve===null&&(Pe=1,Hi(e,Qt(t,e.current)))}function Xh(){var e=U.H;return U.H=ji,e===null?ji:e}function Kh(){var e=U.A;return U.A=pv,e}function zc(){Pe=4,In||(we&4194048)!==we&&Ft.current!==null||(bl=!0),(ea&134217727)===0&&(ja&134217727)===0||Be===null||aa(Be,we,Jt,!1)}function Tc(e,t,a){var u=De;De|=2;var r=Xh(),c=Kh();(Be!==e||we!==t)&&($i=null,El(e,t)),t=!1;var d=Pe;e:do try{if(je!==0&&ve!==null){var p=ve,b=qt;switch(je){case 8:wc(),d=6;break e;case 3:case 2:case 9:case 6:Ft.current===null&&(t=!0);var O=je;if(je=0,qt=null,Al(e,p,b,O),a&&bl){d=0;break e}break;default:O=je,je=0,qt=null,Al(e,p,b,O)}}bv(),d=Pe;break}catch(H){Gh(e,H)}while(!0);return t&&e.shellSuspendCounter++,En=za=null,De=u,U.H=r,U.A=c,ve===null&&(Be=null,we=0,hi()),d}function bv(){for(;ve!==null;)Fh(ve)}function xv(e,t){var a=De;De|=2;var u=Xh(),r=Kh();Be!==e||we!==t?($i=null,Yi=Vt()+500,El(e,t)):bl=pa(e,t);e:do try{if(je!==0&&ve!==null){t=ve;var c=qt;t:switch(je){case 1:je=0,qt=null,Al(e,t,c,1);break;case 2:case 9:if(rd(c)){je=0,qt=null,Jh(t);break}t=function(){je!==2&&je!==9||Be!==e||(je=7),dn(e)},c.then(t,t);break e;case 3:je=7;break e;case 4:je=5;break e;case 7:rd(c)?(je=0,qt=null,Jh(t)):(je=0,qt=null,Al(e,t,c,7));break;case 5:var d=null;switch(ve.tag){case 26:d=ve.memoizedState;case 5:case 27:var p=ve;if(!d||Dm(d)){je=0,qt=null;var b=p.sibling;if(b!==null)ve=b;else{var O=p.return;O!==null?(ve=O,Gi(O)):ve=null}break t}}je=0,qt=null,Al(e,t,c,5);break;case 6:je=0,qt=null,Al(e,t,c,6);break;case 8:wc(),Pe=6;break e;default:throw Error(s(462))}}Sv();break}catch(H){Gh(e,H)}while(!0);return En=za=null,U.H=u,U.A=r,De=a,ve!==null?0:(Be=null,we=0,hi(),Pe)}function Sv(){for(;ve!==null&&!ei();)Fh(ve)}function Fh(e){var t=_h(e.alternate,e,Dn);e.memoizedProps=e.pendingProps,t===null?Gi(e):ve=t}function Jh(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=ph(a,t,t.pendingProps,t.type,void 0,we);break;case 11:t=ph(a,t,t.pendingProps,t.type.render,t.ref,we);break;case 5:Vs(t);default:Ah(a,t),t=ve=Pf(t,Dn),t=_h(a,t,Dn)}e.memoizedProps=e.pendingProps,t===null?Gi(e):ve=t}function Al(e,t,a,u){En=za=null,Vs(t),yl=null,ou=0;var r=t.return;try{if(ov(e,r,t,a,we)){Pe=1,Hi(e,Qt(a,e.current)),ve=null;return}}catch(c){if(r!==null)throw ve=r,c;Pe=1,Hi(e,Qt(a,e.current)),ve=null;return}t.flags&32768?(Ne||u===1?e=!0:bl||(we&536870912)!==0?e=!1:(In=e=!0,(u===2||u===9||u===3||u===6)&&(u=Ft.current,u!==null&&u.tag===13&&(u.flags|=16384))),Ph(t,e)):Gi(t)}function Gi(e){var t=e;do{if((t.flags&32768)!==0){Ph(t,In);return}e=t.return;var a=dv(t.alternate,t,Dn);if(a!==null){ve=a;return}if(t=t.sibling,t!==null){ve=t;return}ve=t=e}while(t!==null);Pe===0&&(Pe=5)}function Ph(e,t){do{var a=hv(e.alternate,e);if(a!==null){a.flags&=32767,ve=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){ve=e;return}ve=e=a}while(e!==null);Pe=6,ve=null}function Wh(e,t,a,u,r,c,d,p,b){e.cancelPendingCommit=null;do Xi();while(pt!==0);if((De&6)!==0)throw Error(s(327));if(t!==null){if(t===e.current)throw Error(s(177));if(c=t.lanes|t.childLanes,c|=vs,Ip(e,a,c,d,p,b),e===Be&&(ve=Be=null,we=0),Sl=t,na=e,_l=a,_c=c,Ec=r,Vh=u,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,wv(N,function(){return am(),null})):(e.callbackNode=null,e.callbackPriority=0),u=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||u){u=U.T,U.T=null,r=K.p,K.p=2,d=De,De|=4;try{mv(e,t,a)}finally{De=d,K.p=r,U.T=u}}pt=1,Ih(),em(),tm()}}function Ih(){if(pt===1){pt=0;var e=na,t=Sl,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=U.T,U.T=null;var u=K.p;K.p=2;var r=De;De|=4;try{Uh(t,e);var c=Lc,d=Bf(e.containerInfo),p=c.focusedElem,b=c.selectionRange;if(d!==p&&p&&p.ownerDocument&&kf(p.ownerDocument.documentElement,p)){if(b!==null&&ds(p)){var O=b.start,H=b.end;if(H===void 0&&(H=O),"selectionStart"in p)p.selectionStart=O,p.selectionEnd=Math.min(H,p.value.length);else{var V=p.ownerDocument||document,D=V&&V.defaultView||window;if(D.getSelection){var j=D.getSelection(),oe=p.textContent.length,re=Math.min(b.start,oe),Ze=b.end===void 0?re:Math.min(b.end,oe);!j.extend&&re>Ze&&(d=Ze,Ze=re,re=d);var z=Lf(p,re),E=Lf(p,Ze);if(z&&E&&(j.rangeCount!==1||j.anchorNode!==z.node||j.anchorOffset!==z.offset||j.focusNode!==E.node||j.focusOffset!==E.offset)){var T=V.createRange();T.setStart(z.node,z.offset),j.removeAllRanges(),re>Ze?(j.addRange(T),j.extend(E.node,E.offset)):(T.setEnd(E.node,E.offset),j.addRange(T))}}}}for(V=[],j=p;j=j.parentNode;)j.nodeType===1&&V.push({element:j,left:j.scrollLeft,top:j.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<V.length;p++){var k=V[p];k.element.scrollLeft=k.left,k.element.scrollTop=k.top}}ur=!!qc,Lc=qc=null}finally{De=r,K.p=u,U.T=a}}e.current=t,pt=2}}function em(){if(pt===2){pt=0;var e=na,t=Sl,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=U.T,U.T=null;var u=K.p;K.p=2;var r=De;De|=4;try{Dh(e,t.alternate,t)}finally{De=r,K.p=u,U.T=a}}pt=3}}function tm(){if(pt===4||pt===3){pt=0,Br();var e=na,t=Sl,a=_l,u=Vh;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?pt=5:(pt=0,Sl=na=null,nm(e,e.pendingLanes));var r=e.pendingLanes;if(r===0&&(ta=null),Qr(a),t=t.stateNode,he&&typeof he.onCommitFiberRoot=="function")try{he.onCommitFiberRoot(ae,t,void 0,(t.current.flags&128)===128)}catch{}if(u!==null){t=U.T,r=K.p,K.p=2,U.T=null;try{for(var c=e.onRecoverableError,d=0;d<u.length;d++){var p=u[d];c(p.value,{componentStack:p.stack})}}finally{U.T=t,K.p=r}}(_l&3)!==0&&Xi(),dn(e),r=e.pendingLanes,(a&4194090)!==0&&(r&42)!==0?e===Ac?bu++:(bu=0,Ac=e):bu=0,xu(0)}}function nm(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Il(t)))}function Xi(e){return Ih(),em(),tm(),am()}function am(){if(pt!==5)return!1;var e=na,t=_c;_c=0;var a=Qr(_l),u=U.T,r=K.p;try{K.p=32>a?32:a,U.T=null,a=Ec,Ec=null;var c=na,d=_l;if(pt=0,Sl=na=null,_l=0,(De&6)!==0)throw Error(s(331));var p=De;if(De|=4,kh(c.current),Hh(c,c.current,d,a),De=p,xu(0,!1),he&&typeof he.onPostCommitFiberRoot=="function")try{he.onPostCommitFiberRoot(ae,c)}catch{}return!0}finally{K.p=r,U.T=u,nm(e,t)}}function lm(e,t,a){t=Qt(a,t),t=nc(e.stateNode,t,2),e=Gn(e,t,2),e!==null&&(Hl(e,2),dn(e))}function qe(e,t,a){if(e.tag===3)lm(e,e,a);else for(;t!==null;){if(t.tag===3){lm(t,e,a);break}else if(t.tag===1){var u=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(ta===null||!ta.has(u))){e=Qt(a,e),a=sh(2),u=Gn(t,a,2),u!==null&&(ch(a,u,t,e),Hl(u,2),dn(u));break}}t=t.return}}function Oc(e,t,a){var u=e.pingCache;if(u===null){u=e.pingCache=new vv;var r=new Set;u.set(t,r)}else r=u.get(t),r===void 0&&(r=new Set,u.set(t,r));r.has(a)||(gc=!0,r.add(a),e=_v.bind(null,e,t,a),t.then(e,e))}function _v(e,t,a){var u=e.pingCache;u!==null&&u.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Be===e&&(we&a)===a&&(Pe===4||Pe===3&&(we&62914560)===we&&300>Vt()-Sc?(De&2)===0&&El(e,0):bc|=a,xl===we&&(xl=0)),dn(e)}function um(e,t){t===0&&(t=ef()),e=ul(e,t),e!==null&&(Hl(e,t),dn(e))}function Ev(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),um(e,a)}function Av(e,t){var a=0;switch(e.tag){case 13:var u=e.stateNode,r=e.memoizedState;r!==null&&(a=r.retryLane);break;case 19:u=e.stateNode;break;case 22:u=e.stateNode._retryCache;break;default:throw Error(s(314))}u!==null&&u.delete(t),um(e,a)}function wv(e,t){return Ul(e,t)}var Ki=null,wl=null,Nc=!1,Fi=!1,Rc=!1,Ma=0;function dn(e){e!==wl&&e.next===null&&(wl===null?Ki=wl=e:wl=wl.next=e),Fi=!0,Nc||(Nc=!0,Tv())}function xu(e,t){if(!Rc&&Fi){Rc=!0;do for(var a=!1,u=Ki;u!==null;){if(e!==0){var r=u.pendingLanes;if(r===0)var c=0;else{var d=u.suspendedLanes,p=u.pingedLanes;c=(1<<31-ke(42|e)+1)-1,c&=r&~(d&~p),c=c&201326741?c&201326741|1:c?c|2:0}c!==0&&(a=!0,cm(u,c))}else c=we,c=ya(u,u===Be?c:0,u.cancelPendingCommit!==null||u.timeoutHandle!==-1),(c&3)===0||pa(u,c)||(a=!0,cm(u,c));u=u.next}while(a);Rc=!1}}function zv(){im()}function im(){Fi=Nc=!1;var e=0;Ma!==0&&(Uv()&&(e=Ma),Ma=0);for(var t=Vt(),a=null,u=Ki;u!==null;){var r=u.next,c=rm(u,t);c===0?(u.next=null,a===null?Ki=r:a.next=r,r===null&&(wl=a)):(a=u,(e!==0||(c&3)!==0)&&(Fi=!0)),u=r}xu(e)}function rm(e,t){for(var a=e.suspendedLanes,u=e.pingedLanes,r=e.expirationTimes,c=e.pendingLanes&-62914561;0<c;){var d=31-ke(c),p=1<<d,b=r[d];b===-1?((p&a)===0||(p&u)!==0)&&(r[d]=ti(p,t)):b<=t&&(e.expiredLanes|=p),c&=~p}if(t=Be,a=we,a=ya(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),u=e.callbackNode,a===0||e===t&&(je===2||je===9)||e.cancelPendingCommit!==null)return u!==null&&u!==null&&Ya(u),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||pa(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(u!==null&&Ya(u),Qr(a)){case 2:case 8:a=S;break;case 32:a=N;break;case 268435456:a=W;break;default:a=N}return u=sm.bind(null,e),a=Ul(a,u),e.callbackPriority=t,e.callbackNode=a,t}return u!==null&&u!==null&&Ya(u),e.callbackPriority=2,e.callbackNode=null,2}function sm(e,t){if(pt!==0&&pt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(Xi()&&e.callbackNode!==a)return null;var u=we;return u=ya(e,e===Be?u:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),u===0?null:($h(e,u,t),rm(e,Vt()),e.callbackNode!=null&&e.callbackNode===a?sm.bind(null,e):null)}function cm(e,t){if(Xi())return null;$h(e,t,!0)}function Tv(){Hv(function(){(De&6)!==0?Ul(Zl,zv):im()})}function Dc(){return Ma===0&&(Ma=Io()),Ma}function om(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:ii(""+e)}function fm(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ov(e,t,a,u,r){if(t==="submit"&&a&&a.stateNode===r){var c=om((r[zt]||null).action),d=u.submitter;d&&(t=(t=d[zt]||null)?om(t.formAction):d.getAttribute("formAction"),t!==null&&(c=t,d=null));var p=new oi("action","action",null,u,r);e.push({event:p,listeners:[{instance:null,listener:function(){if(u.defaultPrevented){if(Ma!==0){var b=d?fm(r,d):new FormData(r);Ps(a,{pending:!0,data:b,method:r.method,action:c},null,b)}}else typeof c=="function"&&(p.preventDefault(),b=d?fm(r,d):new FormData(r),Ps(a,{pending:!0,data:b,method:r.method,action:c},c,b))},currentTarget:r}]})}}for(var jc=0;jc<ps.length;jc++){var Mc=ps[jc],Nv=Mc.toLowerCase(),Rv=Mc[0].toUpperCase()+Mc.slice(1);Wt(Nv,"on"+Rv)}Wt($f,"onAnimationEnd"),Wt(Qf,"onAnimationIteration"),Wt(Gf,"onAnimationStart"),Wt("dblclick","onDoubleClick"),Wt("focusin","onFocus"),Wt("focusout","onBlur"),Wt(X0,"onTransitionRun"),Wt(K0,"onTransitionStart"),Wt(F0,"onTransitionCancel"),Wt(Xf,"onTransitionEnd"),Fa("onMouseEnter",["mouseout","mouseover"]),Fa("onMouseLeave",["mouseout","mouseover"]),Fa("onPointerEnter",["pointerout","pointerover"]),Fa("onPointerLeave",["pointerout","pointerover"]),va("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),va("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),va("onBeforeInput",["compositionend","keypress","textInput","paste"]),va("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),va("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),va("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Su="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Su));function dm(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var u=e[a],r=u.event;u=u.listeners;e:{var c=void 0;if(t)for(var d=u.length-1;0<=d;d--){var p=u[d],b=p.instance,O=p.currentTarget;if(p=p.listener,b!==c&&r.isPropagationStopped())break e;c=p,r.currentTarget=O;try{c(r)}catch(H){Zi(H)}r.currentTarget=null,c=b}else for(d=0;d<u.length;d++){if(p=u[d],b=p.instance,O=p.currentTarget,p=p.listener,b!==c&&r.isPropagationStopped())break e;c=p,r.currentTarget=O;try{c(r)}catch(H){Zi(H)}r.currentTarget=null,c=b}}}}function ge(e,t){var a=t[Gr];a===void 0&&(a=t[Gr]=new Set);var u=e+"__bubble";a.has(u)||(hm(t,e,2,!1),a.add(u))}function Cc(e,t,a){var u=0;t&&(u|=4),hm(a,e,u,t)}var Ji="_reactListening"+Math.random().toString(36).slice(2);function Uc(e){if(!e[Ji]){e[Ji]=!0,uf.forEach(function(a){a!=="selectionchange"&&(Dv.has(a)||Cc(a,!1,e),Cc(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ji]||(t[Ji]=!0,Cc("selectionchange",!1,t))}}function hm(e,t,a,u){switch(Hm(t)){case 2:var r=lg;break;case 8:r=ug;break;default:r=Fc}a=r.bind(null,t,a,e),r=void 0,!as||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(r=!0),u?r!==void 0?e.addEventListener(t,a,{capture:!0,passive:r}):e.addEventListener(t,a,!0):r!==void 0?e.addEventListener(t,a,{passive:r}):e.addEventListener(t,a,!1)}function Zc(e,t,a,u,r){var c=u;if((t&1)===0&&(t&2)===0&&u!==null)e:for(;;){if(u===null)return;var d=u.tag;if(d===3||d===4){var p=u.stateNode.containerInfo;if(p===r)break;if(d===4)for(d=u.return;d!==null;){var b=d.tag;if((b===3||b===4)&&d.stateNode.containerInfo===r)return;d=d.return}for(;p!==null;){if(d=Ga(p),d===null)return;if(b=d.tag,b===5||b===6||b===26||b===27){u=c=d;continue e}p=p.parentNode}}u=u.return}xf(function(){var O=c,H=ts(a),V=[];e:{var D=Kf.get(e);if(D!==void 0){var j=oi,oe=e;switch(e){case"keypress":if(si(a)===0)break e;case"keydown":case"keyup":j=w0;break;case"focusin":oe="focus",j=rs;break;case"focusout":oe="blur",j=rs;break;case"beforeblur":case"afterblur":j=rs;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=Ef;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=h0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=O0;break;case $f:case Qf:case Gf:j=p0;break;case Xf:j=R0;break;case"scroll":case"scrollend":j=f0;break;case"wheel":j=j0;break;case"copy":case"cut":case"paste":j=g0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=wf;break;case"toggle":case"beforetoggle":j=C0}var re=(t&4)!==0,Ze=!re&&(e==="scroll"||e==="scrollend"),z=re?D!==null?D+"Capture":null:D;re=[];for(var E=O,T;E!==null;){var k=E;if(T=k.stateNode,k=k.tag,k!==5&&k!==26&&k!==27||T===null||z===null||(k=kl(E,z),k!=null&&re.push(_u(E,k,T))),Ze)break;E=E.return}0<re.length&&(D=new j(D,oe,null,a,H),V.push({event:D,listeners:re}))}}if((t&7)===0){e:{if(D=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",D&&a!==es&&(oe=a.relatedTarget||a.fromElement)&&(Ga(oe)||oe[Qa]))break e;if((j||D)&&(D=H.window===H?H:(D=H.ownerDocument)?D.defaultView||D.parentWindow:window,j?(oe=a.relatedTarget||a.toElement,j=O,oe=oe?Ga(oe):null,oe!==null&&(Ze=f(oe),re=oe.tag,oe!==Ze||re!==5&&re!==27&&re!==6)&&(oe=null)):(j=null,oe=O),j!==oe)){if(re=Ef,k="onMouseLeave",z="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(re=wf,k="onPointerLeave",z="onPointerEnter",E="pointer"),Ze=j==null?D:Ll(j),T=oe==null?D:Ll(oe),D=new re(k,E+"leave",j,a,H),D.target=Ze,D.relatedTarget=T,k=null,Ga(H)===O&&(re=new re(z,E+"enter",oe,a,H),re.target=T,re.relatedTarget=Ze,k=re),Ze=k,j&&oe)t:{for(re=j,z=oe,E=0,T=re;T;T=zl(T))E++;for(T=0,k=z;k;k=zl(k))T++;for(;0<E-T;)re=zl(re),E--;for(;0<T-E;)z=zl(z),T--;for(;E--;){if(re===z||z!==null&&re===z.alternate)break t;re=zl(re),z=zl(z)}re=null}else re=null;j!==null&&mm(V,D,j,re,!1),oe!==null&&Ze!==null&&mm(V,Ze,oe,re,!0)}}e:{if(D=O?Ll(O):window,j=D.nodeName&&D.nodeName.toLowerCase(),j==="select"||j==="input"&&D.type==="file")var te=Mf;else if(Df(D))if(Cf)te=$0;else{te=V0;var pe=B0}else j=D.nodeName,!j||j.toLowerCase()!=="input"||D.type!=="checkbox"&&D.type!=="radio"?O&&Ir(O.elementType)&&(te=Mf):te=Y0;if(te&&(te=te(e,O))){jf(V,te,a,H);break e}pe&&pe(e,D,O),e==="focusout"&&O&&D.type==="number"&&O.memoizedProps.value!=null&&Wr(D,"number",D.value)}switch(pe=O?Ll(O):window,e){case"focusin":(Df(pe)||pe.contentEditable==="true")&&(nl=pe,hs=O,Kl=null);break;case"focusout":Kl=hs=nl=null;break;case"mousedown":ms=!0;break;case"contextmenu":case"mouseup":case"dragend":ms=!1,Vf(V,a,H);break;case"selectionchange":if(G0)break;case"keydown":case"keyup":Vf(V,a,H)}var le;if(cs)e:{switch(e){case"compositionstart":var se="onCompositionStart";break e;case"compositionend":se="onCompositionEnd";break e;case"compositionupdate":se="onCompositionUpdate";break e}se=void 0}else tl?Nf(e,a)&&(se="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(se="onCompositionStart");se&&(zf&&a.locale!=="ko"&&(tl||se!=="onCompositionStart"?se==="onCompositionEnd"&&tl&&(le=Sf()):(Vn=H,ls="value"in Vn?Vn.value:Vn.textContent,tl=!0)),pe=Pi(O,se),0<pe.length&&(se=new Af(se,e,null,a,H),V.push({event:se,listeners:pe}),le?se.data=le:(le=Rf(a),le!==null&&(se.data=le)))),(le=Z0?H0(e,a):q0(e,a))&&(se=Pi(O,"onBeforeInput"),0<se.length&&(pe=new Af("onBeforeInput","beforeinput",null,a,H),V.push({event:pe,listeners:se}),pe.data=le)),Ov(V,e,O,a,H)}dm(V,t)})}function _u(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Pi(e,t){for(var a=t+"Capture",u=[];e!==null;){var r=e,c=r.stateNode;if(r=r.tag,r!==5&&r!==26&&r!==27||c===null||(r=kl(e,a),r!=null&&u.unshift(_u(e,r,c)),r=kl(e,t),r!=null&&u.push(_u(e,r,c))),e.tag===3)return u;e=e.return}return[]}function zl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function mm(e,t,a,u,r){for(var c=t._reactName,d=[];a!==null&&a!==u;){var p=a,b=p.alternate,O=p.stateNode;if(p=p.tag,b!==null&&b===u)break;p!==5&&p!==26&&p!==27||O===null||(b=O,r?(O=kl(a,c),O!=null&&d.unshift(_u(a,O,b))):r||(O=kl(a,c),O!=null&&d.push(_u(a,O,b)))),a=a.return}d.length!==0&&e.push({event:t,listeners:d})}var jv=/\r\n?/g,Mv=/\u0000|\uFFFD/g;function ym(e){return(typeof e=="string"?e:""+e).replace(jv,`
`).replace(Mv,"")}function pm(e,t){return t=ym(t),ym(e)===t}function Wi(){}function Ue(e,t,a,u,r,c){switch(a){case"children":typeof u=="string"?t==="body"||t==="textarea"&&u===""||Wa(e,u):(typeof u=="number"||typeof u=="bigint")&&t!=="body"&&Wa(e,""+u);break;case"className":ai(e,"class",u);break;case"tabIndex":ai(e,"tabindex",u);break;case"dir":case"role":case"viewBox":case"width":case"height":ai(e,a,u);break;case"style":gf(e,u,c);break;case"data":if(t!=="object"){ai(e,"data",u);break}case"src":case"href":if(u===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(u==null||typeof u=="function"||typeof u=="symbol"||typeof u=="boolean"){e.removeAttribute(a);break}u=ii(""+u),e.setAttribute(a,u);break;case"action":case"formAction":if(typeof u=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof c=="function"&&(a==="formAction"?(t!=="input"&&Ue(e,t,"name",r.name,r,null),Ue(e,t,"formEncType",r.formEncType,r,null),Ue(e,t,"formMethod",r.formMethod,r,null),Ue(e,t,"formTarget",r.formTarget,r,null)):(Ue(e,t,"encType",r.encType,r,null),Ue(e,t,"method",r.method,r,null),Ue(e,t,"target",r.target,r,null)));if(u==null||typeof u=="symbol"||typeof u=="boolean"){e.removeAttribute(a);break}u=ii(""+u),e.setAttribute(a,u);break;case"onClick":u!=null&&(e.onclick=Wi);break;case"onScroll":u!=null&&ge("scroll",e);break;case"onScrollEnd":u!=null&&ge("scrollend",e);break;case"dangerouslySetInnerHTML":if(u!=null){if(typeof u!="object"||!("__html"in u))throw Error(s(61));if(a=u.__html,a!=null){if(r.children!=null)throw Error(s(60));e.innerHTML=a}}break;case"multiple":e.multiple=u&&typeof u!="function"&&typeof u!="symbol";break;case"muted":e.muted=u&&typeof u!="function"&&typeof u!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(u==null||typeof u=="function"||typeof u=="boolean"||typeof u=="symbol"){e.removeAttribute("xlink:href");break}a=ii(""+u),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":u!=null&&typeof u!="function"&&typeof u!="symbol"?e.setAttribute(a,""+u):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":u&&typeof u!="function"&&typeof u!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":u===!0?e.setAttribute(a,""):u!==!1&&u!=null&&typeof u!="function"&&typeof u!="symbol"?e.setAttribute(a,u):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":u!=null&&typeof u!="function"&&typeof u!="symbol"&&!isNaN(u)&&1<=u?e.setAttribute(a,u):e.removeAttribute(a);break;case"rowSpan":case"start":u==null||typeof u=="function"||typeof u=="symbol"||isNaN(u)?e.removeAttribute(a):e.setAttribute(a,u);break;case"popover":ge("beforetoggle",e),ge("toggle",e),ni(e,"popover",u);break;case"xlinkActuate":gn(e,"http://www.w3.org/1999/xlink","xlink:actuate",u);break;case"xlinkArcrole":gn(e,"http://www.w3.org/1999/xlink","xlink:arcrole",u);break;case"xlinkRole":gn(e,"http://www.w3.org/1999/xlink","xlink:role",u);break;case"xlinkShow":gn(e,"http://www.w3.org/1999/xlink","xlink:show",u);break;case"xlinkTitle":gn(e,"http://www.w3.org/1999/xlink","xlink:title",u);break;case"xlinkType":gn(e,"http://www.w3.org/1999/xlink","xlink:type",u);break;case"xmlBase":gn(e,"http://www.w3.org/XML/1998/namespace","xml:base",u);break;case"xmlLang":gn(e,"http://www.w3.org/XML/1998/namespace","xml:lang",u);break;case"xmlSpace":gn(e,"http://www.w3.org/XML/1998/namespace","xml:space",u);break;case"is":ni(e,"is",u);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=c0.get(a)||a,ni(e,a,u))}}function Hc(e,t,a,u,r,c){switch(a){case"style":gf(e,u,c);break;case"dangerouslySetInnerHTML":if(u!=null){if(typeof u!="object"||!("__html"in u))throw Error(s(61));if(a=u.__html,a!=null){if(r.children!=null)throw Error(s(60));e.innerHTML=a}}break;case"children":typeof u=="string"?Wa(e,u):(typeof u=="number"||typeof u=="bigint")&&Wa(e,""+u);break;case"onScroll":u!=null&&ge("scroll",e);break;case"onScrollEnd":u!=null&&ge("scrollend",e);break;case"onClick":u!=null&&(e.onclick=Wi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!rf.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(r=a.endsWith("Capture"),t=a.slice(2,r?a.length-7:void 0),c=e[zt]||null,c=c!=null?c[a]:null,typeof c=="function"&&e.removeEventListener(t,c,r),typeof u=="function")){typeof c!="function"&&c!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,u,r);break e}a in e?e[a]=u:u===!0?e.setAttribute(a,""):ni(e,a,u)}}}function vt(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ge("error",e),ge("load",e);var u=!1,r=!1,c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(d!=null)switch(c){case"src":u=!0;break;case"srcSet":r=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Ue(e,t,c,d,a,null)}}r&&Ue(e,t,"srcSet",a.srcSet,a,null),u&&Ue(e,t,"src",a.src,a,null);return;case"input":ge("invalid",e);var p=c=d=r=null,b=null,O=null;for(u in a)if(a.hasOwnProperty(u)){var H=a[u];if(H!=null)switch(u){case"name":r=H;break;case"type":d=H;break;case"checked":b=H;break;case"defaultChecked":O=H;break;case"value":c=H;break;case"defaultValue":p=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(s(137,t));break;default:Ue(e,t,u,H,a,null)}}mf(e,c,p,b,O,d,r,!1),li(e);return;case"select":ge("invalid",e),u=d=c=null;for(r in a)if(a.hasOwnProperty(r)&&(p=a[r],p!=null))switch(r){case"value":c=p;break;case"defaultValue":d=p;break;case"multiple":u=p;default:Ue(e,t,r,p,a,null)}t=c,a=d,e.multiple=!!u,t!=null?Pa(e,!!u,t,!1):a!=null&&Pa(e,!!u,a,!0);return;case"textarea":ge("invalid",e),c=r=u=null;for(d in a)if(a.hasOwnProperty(d)&&(p=a[d],p!=null))switch(d){case"value":u=p;break;case"defaultValue":r=p;break;case"children":c=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(s(91));break;default:Ue(e,t,d,p,a,null)}pf(e,u,r,c),li(e);return;case"option":for(b in a)if(a.hasOwnProperty(b)&&(u=a[b],u!=null))switch(b){case"selected":e.selected=u&&typeof u!="function"&&typeof u!="symbol";break;default:Ue(e,t,b,u,a,null)}return;case"dialog":ge("beforetoggle",e),ge("toggle",e),ge("cancel",e),ge("close",e);break;case"iframe":case"object":ge("load",e);break;case"video":case"audio":for(u=0;u<Su.length;u++)ge(Su[u],e);break;case"image":ge("error",e),ge("load",e);break;case"details":ge("toggle",e);break;case"embed":case"source":case"link":ge("error",e),ge("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(O in a)if(a.hasOwnProperty(O)&&(u=a[O],u!=null))switch(O){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Ue(e,t,O,u,a,null)}return;default:if(Ir(t)){for(H in a)a.hasOwnProperty(H)&&(u=a[H],u!==void 0&&Hc(e,t,H,u,a,void 0));return}}for(p in a)a.hasOwnProperty(p)&&(u=a[p],u!=null&&Ue(e,t,p,u,a,null))}function Cv(e,t,a,u){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,c=null,d=null,p=null,b=null,O=null,H=null;for(j in a){var V=a[j];if(a.hasOwnProperty(j)&&V!=null)switch(j){case"checked":break;case"value":break;case"defaultValue":b=V;default:u.hasOwnProperty(j)||Ue(e,t,j,null,u,V)}}for(var D in u){var j=u[D];if(V=a[D],u.hasOwnProperty(D)&&(j!=null||V!=null))switch(D){case"type":c=j;break;case"name":r=j;break;case"checked":O=j;break;case"defaultChecked":H=j;break;case"value":d=j;break;case"defaultValue":p=j;break;case"children":case"dangerouslySetInnerHTML":if(j!=null)throw Error(s(137,t));break;default:j!==V&&Ue(e,t,D,j,u,V)}}Pr(e,d,p,b,O,H,c,r);return;case"select":j=d=p=D=null;for(c in a)if(b=a[c],a.hasOwnProperty(c)&&b!=null)switch(c){case"value":break;case"multiple":j=b;default:u.hasOwnProperty(c)||Ue(e,t,c,null,u,b)}for(r in u)if(c=u[r],b=a[r],u.hasOwnProperty(r)&&(c!=null||b!=null))switch(r){case"value":D=c;break;case"defaultValue":p=c;break;case"multiple":d=c;default:c!==b&&Ue(e,t,r,c,u,b)}t=p,a=d,u=j,D!=null?Pa(e,!!a,D,!1):!!u!=!!a&&(t!=null?Pa(e,!!a,t,!0):Pa(e,!!a,a?[]:"",!1));return;case"textarea":j=D=null;for(p in a)if(r=a[p],a.hasOwnProperty(p)&&r!=null&&!u.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Ue(e,t,p,null,u,r)}for(d in u)if(r=u[d],c=a[d],u.hasOwnProperty(d)&&(r!=null||c!=null))switch(d){case"value":D=r;break;case"defaultValue":j=r;break;case"children":break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(s(91));break;default:r!==c&&Ue(e,t,d,r,u,c)}yf(e,D,j);return;case"option":for(var oe in a)if(D=a[oe],a.hasOwnProperty(oe)&&D!=null&&!u.hasOwnProperty(oe))switch(oe){case"selected":e.selected=!1;break;default:Ue(e,t,oe,null,u,D)}for(b in u)if(D=u[b],j=a[b],u.hasOwnProperty(b)&&D!==j&&(D!=null||j!=null))switch(b){case"selected":e.selected=D&&typeof D!="function"&&typeof D!="symbol";break;default:Ue(e,t,b,D,u,j)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var re in a)D=a[re],a.hasOwnProperty(re)&&D!=null&&!u.hasOwnProperty(re)&&Ue(e,t,re,null,u,D);for(O in u)if(D=u[O],j=a[O],u.hasOwnProperty(O)&&D!==j&&(D!=null||j!=null))switch(O){case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(s(137,t));break;default:Ue(e,t,O,D,u,j)}return;default:if(Ir(t)){for(var Ze in a)D=a[Ze],a.hasOwnProperty(Ze)&&D!==void 0&&!u.hasOwnProperty(Ze)&&Hc(e,t,Ze,void 0,u,D);for(H in u)D=u[H],j=a[H],!u.hasOwnProperty(H)||D===j||D===void 0&&j===void 0||Hc(e,t,H,D,u,j);return}}for(var z in a)D=a[z],a.hasOwnProperty(z)&&D!=null&&!u.hasOwnProperty(z)&&Ue(e,t,z,null,u,D);for(V in u)D=u[V],j=a[V],!u.hasOwnProperty(V)||D===j||D==null&&j==null||Ue(e,t,V,D,u,j)}var qc=null,Lc=null;function Ii(e){return e.nodeType===9?e:e.ownerDocument}function vm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function gm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function kc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Bc=null;function Uv(){var e=window.event;return e&&e.type==="popstate"?e===Bc?!1:(Bc=e,!0):(Bc=null,!1)}var bm=typeof setTimeout=="function"?setTimeout:void 0,Zv=typeof clearTimeout=="function"?clearTimeout:void 0,xm=typeof Promise=="function"?Promise:void 0,Hv=typeof queueMicrotask=="function"?queueMicrotask:typeof xm<"u"?function(e){return xm.resolve(null).then(e).catch(qv)}:bm;function qv(e){setTimeout(function(){throw e})}function la(e){return e==="head"}function Sm(e,t){var a=t,u=0,r=0;do{var c=a.nextSibling;if(e.removeChild(a),c&&c.nodeType===8)if(a=c.data,a==="/$"){if(0<u&&8>u){a=u;var d=e.ownerDocument;if(a&1&&Eu(d.documentElement),a&2&&Eu(d.body),a&4)for(a=d.head,Eu(a),d=a.firstChild;d;){var p=d.nextSibling,b=d.nodeName;d[ql]||b==="SCRIPT"||b==="STYLE"||b==="LINK"&&d.rel.toLowerCase()==="stylesheet"||a.removeChild(d),d=p}}if(r===0){e.removeChild(c),Du(t);return}r--}else a==="$"||a==="$?"||a==="$!"?r++:u=a.charCodeAt(0)-48;else u=0;a=c}while(a);Du(t)}function Vc(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Vc(a),Xr(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function Lv(e,t,a,u){for(;e.nodeType===1;){var r=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!u&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(u){if(!e[ql])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(c=e.getAttribute("rel"),c==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(c!==r.rel||e.getAttribute("href")!==(r.href==null||r.href===""?null:r.href)||e.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin)||e.getAttribute("title")!==(r.title==null?null:r.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(c=e.getAttribute("src"),(c!==(r.src==null?null:r.src)||e.getAttribute("type")!==(r.type==null?null:r.type)||e.getAttribute("crossorigin")!==(r.crossOrigin==null?null:r.crossOrigin))&&c&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var c=r.name==null?null:""+r.name;if(r.type==="hidden"&&e.getAttribute("name")===c)return e}else return e;if(e=en(e.nextSibling),e===null)break}return null}function kv(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=en(e.nextSibling),e===null))return null;return e}function Yc(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function Bv(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var u=function(){t(),a.removeEventListener("DOMContentLoaded",u)};a.addEventListener("DOMContentLoaded",u),e._reactRetry=u}}function en(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var $c=null;function _m(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function Em(e,t,a){switch(t=Ii(a),e){case"html":if(e=t.documentElement,!e)throw Error(s(452));return e;case"head":if(e=t.head,!e)throw Error(s(453));return e;case"body":if(e=t.body,!e)throw Error(s(454));return e;default:throw Error(s(451))}}function Eu(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Xr(e)}var Pt=new Map,Am=new Set;function er(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var jn=K.d;K.d={f:Vv,r:Yv,D:$v,C:Qv,L:Gv,m:Xv,X:Fv,S:Kv,M:Jv};function Vv(){var e=jn.f(),t=Qi();return e||t}function Yv(e){var t=Xa(e);t!==null&&t.tag===5&&t.type==="form"?$d(t):jn.r(e)}var Tl=typeof document>"u"?null:document;function wm(e,t,a){var u=Tl;if(u&&typeof t=="string"&&t){var r=$t(t);r='link[rel="'+e+'"][href="'+r+'"]',typeof a=="string"&&(r+='[crossorigin="'+a+'"]'),Am.has(r)||(Am.add(r),e={rel:e,crossOrigin:a,href:t},u.querySelector(r)===null&&(t=u.createElement("link"),vt(t,"link",e),st(t),u.head.appendChild(t)))}}function $v(e){jn.D(e),wm("dns-prefetch",e,null)}function Qv(e,t){jn.C(e,t),wm("preconnect",e,t)}function Gv(e,t,a){jn.L(e,t,a);var u=Tl;if(u&&e&&t){var r='link[rel="preload"][as="'+$t(t)+'"]';t==="image"&&a&&a.imageSrcSet?(r+='[imagesrcset="'+$t(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(r+='[imagesizes="'+$t(a.imageSizes)+'"]')):r+='[href="'+$t(e)+'"]';var c=r;switch(t){case"style":c=Ol(e);break;case"script":c=Nl(e)}Pt.has(c)||(e=x({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),Pt.set(c,e),u.querySelector(r)!==null||t==="style"&&u.querySelector(Au(c))||t==="script"&&u.querySelector(wu(c))||(t=u.createElement("link"),vt(t,"link",e),st(t),u.head.appendChild(t)))}}function Xv(e,t){jn.m(e,t);var a=Tl;if(a&&e){var u=t&&typeof t.as=="string"?t.as:"script",r='link[rel="modulepreload"][as="'+$t(u)+'"][href="'+$t(e)+'"]',c=r;switch(u){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":c=Nl(e)}if(!Pt.has(c)&&(e=x({rel:"modulepreload",href:e},t),Pt.set(c,e),a.querySelector(r)===null)){switch(u){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(wu(c)))return}u=a.createElement("link"),vt(u,"link",e),st(u),a.head.appendChild(u)}}}function Kv(e,t,a){jn.S(e,t,a);var u=Tl;if(u&&e){var r=Ka(u).hoistableStyles,c=Ol(e);t=t||"default";var d=r.get(c);if(!d){var p={loading:0,preload:null};if(d=u.querySelector(Au(c)))p.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},a),(a=Pt.get(c))&&Qc(e,a);var b=d=u.createElement("link");st(b),vt(b,"link",e),b._p=new Promise(function(O,H){b.onload=O,b.onerror=H}),b.addEventListener("load",function(){p.loading|=1}),b.addEventListener("error",function(){p.loading|=2}),p.loading|=4,tr(d,t,u)}d={type:"stylesheet",instance:d,count:1,state:p},r.set(c,d)}}}function Fv(e,t){jn.X(e,t);var a=Tl;if(a&&e){var u=Ka(a).hoistableScripts,r=Nl(e),c=u.get(r);c||(c=a.querySelector(wu(r)),c||(e=x({src:e,async:!0},t),(t=Pt.get(r))&&Gc(e,t),c=a.createElement("script"),st(c),vt(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},u.set(r,c))}}function Jv(e,t){jn.M(e,t);var a=Tl;if(a&&e){var u=Ka(a).hoistableScripts,r=Nl(e),c=u.get(r);c||(c=a.querySelector(wu(r)),c||(e=x({src:e,async:!0,type:"module"},t),(t=Pt.get(r))&&Gc(e,t),c=a.createElement("script"),st(c),vt(c,"link",e),a.head.appendChild(c)),c={type:"script",instance:c,count:1,state:null},u.set(r,c))}}function zm(e,t,a,u){var r=(r=fe.current)?er(r):null;if(!r)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=Ol(a.href),a=Ka(r).hoistableStyles,u=a.get(t),u||(u={type:"style",instance:null,count:0,state:null},a.set(t,u)),u):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=Ol(a.href);var c=Ka(r).hoistableStyles,d=c.get(e);if(d||(r=r.ownerDocument||r,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=r.querySelector(Au(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Pt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Pt.set(e,a),c||Pv(r,e,a,d.state))),t&&u===null)throw Error(s(528,""));return d}if(t&&u!==null)throw Error(s(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Nl(a),a=Ka(r).hoistableScripts,u=a.get(t),u||(u={type:"script",instance:null,count:0,state:null},a.set(t,u)),u):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function Ol(e){return'href="'+$t(e)+'"'}function Au(e){return'link[rel="stylesheet"]['+e+"]"}function Tm(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function Pv(e,t,a,u){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?u.loading=1:(t=e.createElement("link"),u.preload=t,t.addEventListener("load",function(){return u.loading|=1}),t.addEventListener("error",function(){return u.loading|=2}),vt(t,"link",a),st(t),e.head.appendChild(t))}function Nl(e){return'[src="'+$t(e)+'"]'}function wu(e){return"script[async]"+e}function Om(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var u=e.querySelector('style[data-href~="'+$t(a.href)+'"]');if(u)return t.instance=u,st(u),u;var r=x({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return u=(e.ownerDocument||e).createElement("style"),st(u),vt(u,"style",r),tr(u,a.precedence,e),t.instance=u;case"stylesheet":r=Ol(a.href);var c=e.querySelector(Au(r));if(c)return t.state.loading|=4,t.instance=c,st(c),c;u=Tm(a),(r=Pt.get(r))&&Qc(u,r),c=(e.ownerDocument||e).createElement("link"),st(c);var d=c;return d._p=new Promise(function(p,b){d.onload=p,d.onerror=b}),vt(c,"link",u),t.state.loading|=4,tr(c,a.precedence,e),t.instance=c;case"script":return c=Nl(a.src),(r=e.querySelector(wu(c)))?(t.instance=r,st(r),r):(u=a,(r=Pt.get(c))&&(u=x({},a),Gc(u,r)),e=e.ownerDocument||e,r=e.createElement("script"),st(r),vt(r,"link",u),e.head.appendChild(r),t.instance=r);case"void":return null;default:throw Error(s(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(u=t.instance,t.state.loading|=4,tr(u,a.precedence,e));return t.instance}function tr(e,t,a){for(var u=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=u.length?u[u.length-1]:null,c=r,d=0;d<u.length;d++){var p=u[d];if(p.dataset.precedence===t)c=p;else if(c!==r)break}c?c.parentNode.insertBefore(e,c.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Qc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Gc(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var nr=null;function Nm(e,t,a){if(nr===null){var u=new Map,r=nr=new Map;r.set(a,u)}else r=nr,u=r.get(a),u||(u=new Map,r.set(a,u));if(u.has(e))return u;for(u.set(e,null),a=a.getElementsByTagName(e),r=0;r<a.length;r++){var c=a[r];if(!(c[ql]||c[gt]||e==="link"&&c.getAttribute("rel")==="stylesheet")&&c.namespaceURI!=="http://www.w3.org/2000/svg"){var d=c.getAttribute(t)||"";d=e+d;var p=u.get(d);p?p.push(c):u.set(d,[c])}}return u}function Rm(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Wv(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Dm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var zu=null;function Iv(){}function eg(e,t,a){if(zu===null)throw Error(s(475));var u=zu;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var r=Ol(a.href),c=e.querySelector(Au(r));if(c){e=c._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(u.count++,u=ar.bind(u),e.then(u,u)),t.state.loading|=4,t.instance=c,st(c);return}c=e.ownerDocument||e,a=Tm(a),(r=Pt.get(r))&&Qc(a,r),c=c.createElement("link"),st(c);var d=c;d._p=new Promise(function(p,b){d.onload=p,d.onerror=b}),vt(c,"link",a),t.instance=c}u.stylesheets===null&&(u.stylesheets=new Map),u.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(u.count++,t=ar.bind(u),e.addEventListener("load",t),e.addEventListener("error",t))}}function tg(){if(zu===null)throw Error(s(475));var e=zu;return e.stylesheets&&e.count===0&&Xc(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Xc(e,e.stylesheets),e.unsuspend){var u=e.unsuspend;e.unsuspend=null,u()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function ar(){if(this.count--,this.count===0){if(this.stylesheets)Xc(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var lr=null;function Xc(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,lr=new Map,t.forEach(ng,e),lr=null,ar.call(e))}function ng(e,t){if(!(t.state.loading&4)){var a=lr.get(e);if(a)var u=a.get(null);else{a=new Map,lr.set(e,a);for(var r=e.querySelectorAll("link[data-precedence],style[data-precedence]"),c=0;c<r.length;c++){var d=r[c];(d.nodeName==="LINK"||d.getAttribute("media")!=="not all")&&(a.set(d.dataset.precedence,d),u=d)}u&&a.set(null,u)}r=t.instance,d=r.getAttribute("data-precedence"),c=a.get(d)||u,c===u&&a.set(null,r),a.set(d,r),this.count++,u=ar.bind(this),r.addEventListener("load",u),r.addEventListener("error",u),c?c.parentNode.insertBefore(r,c.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(r,e.firstChild)),t.state.loading|=4}}var Tu={$$typeof:Q,Provider:null,Consumer:null,_currentValue:ue,_currentValue2:ue,_threadCount:0};function ag(e,t,a,u,r,c,d,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Yr(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yr(0),this.hiddenUpdates=Yr(null),this.identifierPrefix=u,this.onUncaughtError=r,this.onCaughtError=c,this.onRecoverableError=d,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function jm(e,t,a,u,r,c,d,p,b,O,H,V){return e=new ag(e,t,a,d,p,b,O,V),t=1,c===!0&&(t|=24),c=Ut(3,null,null,t),e.current=c,c.stateNode=e,t=Os(),t.refCount++,e.pooledCache=t,t.refCount++,c.memoizedState={element:u,isDehydrated:a,cache:t},js(c),e}function Mm(e){return e?(e=il,e):il}function Cm(e,t,a,u,r,c){r=Mm(r),u.context===null?u.context=r:u.pendingContext=r,u=Qn(t),u.payload={element:a},c=c===void 0?null:c,c!==null&&(u.callback=c),a=Gn(e,u,t),a!==null&&(kt(a,e,t),au(a,e,t))}function Um(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Kc(e,t){Um(e,t),(e=e.alternate)&&Um(e,t)}function Zm(e){if(e.tag===13){var t=ul(e,67108864);t!==null&&kt(t,e,67108864),Kc(e,67108864)}}var ur=!0;function lg(e,t,a,u){var r=U.T;U.T=null;var c=K.p;try{K.p=2,Fc(e,t,a,u)}finally{K.p=c,U.T=r}}function ug(e,t,a,u){var r=U.T;U.T=null;var c=K.p;try{K.p=8,Fc(e,t,a,u)}finally{K.p=c,U.T=r}}function Fc(e,t,a,u){if(ur){var r=Jc(u);if(r===null)Zc(e,t,u,ir,a),qm(e,u);else if(rg(r,e,t,a,u))u.stopPropagation();else if(qm(e,u),t&4&&-1<ig.indexOf(e)){for(;r!==null;){var c=Xa(r);if(c!==null)switch(c.tag){case 3:if(c=c.stateNode,c.current.memoizedState.isDehydrated){var d=vn(c.pendingLanes);if(d!==0){var p=c;for(p.pendingLanes|=2,p.entangledLanes|=2;d;){var b=1<<31-ke(d);p.entanglements[1]|=b,d&=~b}dn(c),(De&6)===0&&(Yi=Vt()+500,xu(0))}}break;case 13:p=ul(c,2),p!==null&&kt(p,c,2),Qi(),Kc(c,2)}if(c=Jc(u),c===null&&Zc(e,t,u,ir,a),c===r)break;r=c}r!==null&&u.stopPropagation()}else Zc(e,t,u,null,a)}}function Jc(e){return e=ts(e),Pc(e)}var ir=null;function Pc(e){if(ir=null,e=Ga(e),e!==null){var t=f(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=h(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ir=e,null}function Hm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Wo()){case Zl:return 2;case S:return 8;case N:case q:return 32;case W:return 268435456;default:return 32}default:return 32}}var Wc=!1,ua=null,ia=null,ra=null,Ou=new Map,Nu=new Map,sa=[],ig="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function qm(e,t){switch(e){case"focusin":case"focusout":ua=null;break;case"dragenter":case"dragleave":ia=null;break;case"mouseover":case"mouseout":ra=null;break;case"pointerover":case"pointerout":Ou.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Nu.delete(t.pointerId)}}function Ru(e,t,a,u,r,c){return e===null||e.nativeEvent!==c?(e={blockedOn:t,domEventName:a,eventSystemFlags:u,nativeEvent:c,targetContainers:[r]},t!==null&&(t=Xa(t),t!==null&&Zm(t)),e):(e.eventSystemFlags|=u,t=e.targetContainers,r!==null&&t.indexOf(r)===-1&&t.push(r),e)}function rg(e,t,a,u,r){switch(t){case"focusin":return ua=Ru(ua,e,t,a,u,r),!0;case"dragenter":return ia=Ru(ia,e,t,a,u,r),!0;case"mouseover":return ra=Ru(ra,e,t,a,u,r),!0;case"pointerover":var c=r.pointerId;return Ou.set(c,Ru(Ou.get(c)||null,e,t,a,u,r)),!0;case"gotpointercapture":return c=r.pointerId,Nu.set(c,Ru(Nu.get(c)||null,e,t,a,u,r)),!0}return!1}function Lm(e){var t=Ga(e.target);if(t!==null){var a=f(t);if(a!==null){if(t=a.tag,t===13){if(t=h(a),t!==null){e.blockedOn=t,e0(e.priority,function(){if(a.tag===13){var u=Lt();u=$r(u);var r=ul(a,u);r!==null&&kt(r,a,u),Kc(a,u)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function rr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Jc(e.nativeEvent);if(a===null){a=e.nativeEvent;var u=new a.constructor(a.type,a);es=u,a.target.dispatchEvent(u),es=null}else return t=Xa(a),t!==null&&Zm(t),e.blockedOn=a,!1;t.shift()}return!0}function km(e,t,a){rr(e)&&a.delete(t)}function sg(){Wc=!1,ua!==null&&rr(ua)&&(ua=null),ia!==null&&rr(ia)&&(ia=null),ra!==null&&rr(ra)&&(ra=null),Ou.forEach(km),Nu.forEach(km)}function sr(e,t){e.blockedOn===t&&(e.blockedOn=null,Wc||(Wc=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,sg)))}var cr=null;function Bm(e){cr!==e&&(cr=e,n.unstable_scheduleCallback(n.unstable_NormalPriority,function(){cr===e&&(cr=null);for(var t=0;t<e.length;t+=3){var a=e[t],u=e[t+1],r=e[t+2];if(typeof u!="function"){if(Pc(u||a)===null)continue;break}var c=Xa(a);c!==null&&(e.splice(t,3),t-=3,Ps(c,{pending:!0,data:r,method:a.method,action:u},u,r))}}))}function Du(e){function t(b){return sr(b,e)}ua!==null&&sr(ua,e),ia!==null&&sr(ia,e),ra!==null&&sr(ra,e),Ou.forEach(t),Nu.forEach(t);for(var a=0;a<sa.length;a++){var u=sa[a];u.blockedOn===e&&(u.blockedOn=null)}for(;0<sa.length&&(a=sa[0],a.blockedOn===null);)Lm(a),a.blockedOn===null&&sa.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(u=0;u<a.length;u+=3){var r=a[u],c=a[u+1],d=r[zt]||null;if(typeof c=="function")d||Bm(a);else if(d){var p=null;if(c&&c.hasAttribute("formAction")){if(r=c,d=c[zt]||null)p=d.formAction;else if(Pc(r)!==null)continue}else p=d.action;typeof p=="function"?a[u+1]=p:(a.splice(u,3),u-=3),Bm(a)}}}function Ic(e){this._internalRoot=e}or.prototype.render=Ic.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(s(409));var a=t.current,u=Lt();Cm(a,u,e,t,null,null)},or.prototype.unmount=Ic.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Cm(e.current,2,null,e,null,null),Qi(),t[Qa]=null}};function or(e){this._internalRoot=e}or.prototype.unstable_scheduleHydration=function(e){if(e){var t=af();e={blockedOn:null,target:e,priority:t};for(var a=0;a<sa.length&&t!==0&&t<sa[a].priority;a++);sa.splice(a,0,e),a===0&&Lm(e)}};var Vm=l.version;if(Vm!=="19.1.0")throw Error(s(527,Vm,"19.1.0"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(s(188)):(e=Object.keys(e).join(","),Error(s(268,e)));return e=v(t),e=e!==null?m(e):null,e=e===null?null:e.stateNode,e};var cg={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:U,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var fr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!fr.isDisabled&&fr.supportsFiber)try{ae=fr.inject(cg),he=fr}catch{}}return Mu.createRoot=function(e,t){if(!o(e))throw Error(s(299));var a=!1,u="",r=lh,c=uh,d=ih,p=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(u=t.identifierPrefix),t.onUncaughtError!==void 0&&(r=t.onUncaughtError),t.onCaughtError!==void 0&&(c=t.onCaughtError),t.onRecoverableError!==void 0&&(d=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=jm(e,1,!1,null,null,a,u,r,c,d,p,null),e[Qa]=t.current,Uc(e),new Ic(t)},Mu.hydrateRoot=function(e,t,a){if(!o(e))throw Error(s(299));var u=!1,r="",c=lh,d=uh,p=ih,b=null,O=null;return a!=null&&(a.unstable_strictMode===!0&&(u=!0),a.identifierPrefix!==void 0&&(r=a.identifierPrefix),a.onUncaughtError!==void 0&&(c=a.onUncaughtError),a.onCaughtError!==void 0&&(d=a.onCaughtError),a.onRecoverableError!==void 0&&(p=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(b=a.unstable_transitionCallbacks),a.formState!==void 0&&(O=a.formState)),t=jm(e,1,!0,t,a??null,u,r,c,d,p,b,O),t.context=Mm(null),a=t.current,u=Lt(),u=$r(u),r=Qn(u),r.callback=null,Gn(a,r,u),a=u,t.current.lanes=a,Hl(t,a),dn(t),e[Qa]=t.current,Uc(e),new or(t)},Mu.version="19.1.0",Mu}var Wm;function bg(){if(Wm)return no.exports;Wm=1;function n(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(n)}catch(l){console.error(l)}}return n(),no.exports=gg(),no.exports}var xg=bg();const Sg=Py(xg);var Cu={},Im;function _g(){if(Im)return Cu;Im=1,Object.defineProperty(Cu,"__esModule",{value:!0}),Cu.parse=h,Cu.serialize=m;const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,l=/^[\u0021-\u003A\u003C-\u007E]*$/,i=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,s=/^[\u0020-\u003A\u003D-\u007E]*$/,o=Object.prototype.toString,f=(()=>{const A=function(){};return A.prototype=Object.create(null),A})();function h(A,L){const C=new f,M=A.length;if(M<2)return C;const G=L?.decode||x;let Z=0;do{const B=A.indexOf("=",Z);if(B===-1)break;const Q=A.indexOf(";",Z),ee=Q===-1?M:Q;if(B>ee){Z=A.lastIndexOf(";",B-1)+1;continue}const F=y(A,Z,B),ze=v(A,B,F),Ee=A.slice(F,ze);if(C[Ee]===void 0){let Me=y(A,B+1,ee),be=v(A,ee,Me);const Ge=G(A.slice(Me,be));C[Ee]=Ge}Z=ee+1}while(Z<M);return C}function y(A,L,C){do{const M=A.charCodeAt(L);if(M!==32&&M!==9)return L}while(++L<C);return C}function v(A,L,C){for(;L>C;){const M=A.charCodeAt(--L);if(M!==32&&M!==9)return L+1}return C}function m(A,L,C){const M=C?.encode||encodeURIComponent;if(!n.test(A))throw new TypeError(`argument name is invalid: ${A}`);const G=M(L);if(!l.test(G))throw new TypeError(`argument val is invalid: ${L}`);let Z=A+"="+G;if(!C)return Z;if(C.maxAge!==void 0){if(!Number.isInteger(C.maxAge))throw new TypeError(`option maxAge is invalid: ${C.maxAge}`);Z+="; Max-Age="+C.maxAge}if(C.domain){if(!i.test(C.domain))throw new TypeError(`option domain is invalid: ${C.domain}`);Z+="; Domain="+C.domain}if(C.path){if(!s.test(C.path))throw new TypeError(`option path is invalid: ${C.path}`);Z+="; Path="+C.path}if(C.expires){if(!w(C.expires)||!Number.isFinite(C.expires.valueOf()))throw new TypeError(`option expires is invalid: ${C.expires}`);Z+="; Expires="+C.expires.toUTCString()}if(C.httpOnly&&(Z+="; HttpOnly"),C.secure&&(Z+="; Secure"),C.partitioned&&(Z+="; Partitioned"),C.priority)switch(typeof C.priority=="string"?C.priority.toLowerCase():void 0){case"low":Z+="; Priority=Low";break;case"medium":Z+="; Priority=Medium";break;case"high":Z+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${C.priority}`)}if(C.sameSite)switch(typeof C.sameSite=="string"?C.sameSite.toLowerCase():C.sameSite){case!0:case"strict":Z+="; SameSite=Strict";break;case"lax":Z+="; SameSite=Lax";break;case"none":Z+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${C.sameSite}`)}return Z}function x(A){if(A.indexOf("%")===-1)return A;try{return decodeURIComponent(A)}catch{return A}}function w(A){return o.call(A)==="[object Date]"}return Cu}_g();var ey="popstate";function Eg(n={}){function l(s,o){let{pathname:f,search:h,hash:y}=s.location;return fo("",{pathname:f,search:h,hash:y},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function i(s,o){return typeof o=="string"?o:Yu(o)}return wg(l,i,null,n)}function $e(n,l){if(n===!1||n===null||typeof n>"u")throw new Error(l)}function ln(n,l){if(!n){typeof console<"u"&&console.warn(l);try{throw new Error(l)}catch{}}}function Ag(){return Math.random().toString(36).substring(2,10)}function ty(n,l){return{usr:n.state,key:n.key,idx:l}}function fo(n,l,i=null,s){return{pathname:typeof n=="string"?n:n.pathname,search:"",hash:"",...typeof l=="string"?jl(l):l,state:i,key:l&&l.key||s||Ag()}}function Yu({pathname:n="/",search:l="",hash:i=""}){return l&&l!=="?"&&(n+=l.charAt(0)==="?"?l:"?"+l),i&&i!=="#"&&(n+=i.charAt(0)==="#"?i:"#"+i),n}function jl(n){let l={};if(n){let i=n.indexOf("#");i>=0&&(l.hash=n.substring(i),n=n.substring(0,i));let s=n.indexOf("?");s>=0&&(l.search=n.substring(s),n=n.substring(0,s)),n&&(l.pathname=n)}return l}function wg(n,l,i,s={}){let{window:o=document.defaultView,v5Compat:f=!1}=s,h=o.history,y="POP",v=null,m=x();m==null&&(m=0,h.replaceState({...h.state,idx:m},""));function x(){return(h.state||{idx:null}).idx}function w(){y="POP";let G=x(),Z=G==null?null:G-m;m=G,v&&v({action:y,location:M.location,delta:Z})}function A(G,Z){y="PUSH";let B=fo(M.location,G,Z);m=x()+1;let Q=ty(B,m),ee=M.createHref(B);try{h.pushState(Q,"",ee)}catch(F){if(F instanceof DOMException&&F.name==="DataCloneError")throw F;o.location.assign(ee)}f&&v&&v({action:y,location:M.location,delta:1})}function L(G,Z){y="REPLACE";let B=fo(M.location,G,Z);m=x();let Q=ty(B,m),ee=M.createHref(B);h.replaceState(Q,"",ee),f&&v&&v({action:y,location:M.location,delta:0})}function C(G){return zg(G)}let M={get action(){return y},get location(){return n(o,h)},listen(G){if(v)throw new Error("A history only accepts one active listener");return o.addEventListener(ey,w),v=G,()=>{o.removeEventListener(ey,w),v=null}},createHref(G){return l(o,G)},createURL:C,encodeLocation(G){let Z=C(G);return{pathname:Z.pathname,search:Z.search,hash:Z.hash}},push:A,replace:L,go(G){return h.go(G)}};return M}function zg(n,l=!1){let i="http://localhost";typeof window<"u"&&(i=window.location.origin!=="null"?window.location.origin:window.location.href),$e(i,"No window.location.(origin|href) available to create URL");let s=typeof n=="string"?n:Yu(n);return s=s.replace(/ $/,"%20"),!l&&s.startsWith("//")&&(s=i+s),new URL(s,i)}function Wy(n,l,i="/"){return Tg(n,l,i,!1)}function Tg(n,l,i,s){let o=typeof l=="string"?jl(l):l,f=Zn(o.pathname||"/",i);if(f==null)return null;let h=Iy(n);Og(h);let y=null;for(let v=0;y==null&&v<h.length;++v){let m=Lg(f);y=Hg(h[v],m,s)}return y}function Iy(n,l=[],i=[],s=""){let o=(f,h,y)=>{let v={relativePath:y===void 0?f.path||"":y,caseSensitive:f.caseSensitive===!0,childrenIndex:h,route:f};v.relativePath.startsWith("/")&&($e(v.relativePath.startsWith(s),`Absolute route path "${v.relativePath}" nested under path "${s}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),v.relativePath=v.relativePath.slice(s.length));let m=Un([s,v.relativePath]),x=i.concat(v);f.children&&f.children.length>0&&($e(f.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),Iy(f.children,l,x,m)),!(f.path==null&&!f.index)&&l.push({path:m,score:Ug(m,f.index),routesMeta:x})};return n.forEach((f,h)=>{if(f.path===""||!f.path?.includes("?"))o(f,h);else for(let y of ep(f.path))o(f,h,y)}),l}function ep(n){let l=n.split("/");if(l.length===0)return[];let[i,...s]=l,o=i.endsWith("?"),f=i.replace(/\?$/,"");if(s.length===0)return o?[f,""]:[f];let h=ep(s.join("/")),y=[];return y.push(...h.map(v=>v===""?f:[f,v].join("/"))),o&&y.push(...h),y.map(v=>n.startsWith("/")&&v===""?"/":v)}function Og(n){n.sort((l,i)=>l.score!==i.score?i.score-l.score:Zg(l.routesMeta.map(s=>s.childrenIndex),i.routesMeta.map(s=>s.childrenIndex)))}var Ng=/^:[\w-]+$/,Rg=3,Dg=2,jg=1,Mg=10,Cg=-2,ny=n=>n==="*";function Ug(n,l){let i=n.split("/"),s=i.length;return i.some(ny)&&(s+=Cg),l&&(s+=Dg),i.filter(o=>!ny(o)).reduce((o,f)=>o+(Ng.test(f)?Rg:f===""?jg:Mg),s)}function Zg(n,l){return n.length===l.length&&n.slice(0,-1).every((s,o)=>s===l[o])?n[n.length-1]-l[l.length-1]:0}function Hg(n,l,i=!1){let{routesMeta:s}=n,o={},f="/",h=[];for(let y=0;y<s.length;++y){let v=s[y],m=y===s.length-1,x=f==="/"?l:l.slice(f.length)||"/",w=Er({path:v.relativePath,caseSensitive:v.caseSensitive,end:m},x),A=v.route;if(!w&&m&&i&&!s[s.length-1].route.index&&(w=Er({path:v.relativePath,caseSensitive:v.caseSensitive,end:!1},x)),!w)return null;Object.assign(o,w.params),h.push({params:o,pathname:Un([f,w.pathname]),pathnameBase:Yg(Un([f,w.pathnameBase])),route:A}),w.pathnameBase!=="/"&&(f=Un([f,w.pathnameBase]))}return h}function Er(n,l){typeof n=="string"&&(n={path:n,caseSensitive:!1,end:!0});let[i,s]=qg(n.path,n.caseSensitive,n.end),o=l.match(i);if(!o)return null;let f=o[0],h=f.replace(/(.)\/+$/,"$1"),y=o.slice(1);return{params:s.reduce((m,{paramName:x,isOptional:w},A)=>{if(x==="*"){let C=y[A]||"";h=f.slice(0,f.length-C.length).replace(/(.)\/+$/,"$1")}const L=y[A];return w&&!L?m[x]=void 0:m[x]=(L||"").replace(/%2F/g,"/"),m},{}),pathname:f,pathnameBase:h,pattern:n}}function qg(n,l=!1,i=!0){ln(n==="*"||!n.endsWith("*")||n.endsWith("/*"),`Route path "${n}" will be treated as if it were "${n.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${n.replace(/\*$/,"/*")}".`);let s=[],o="^"+n.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(h,y,v)=>(s.push({paramName:y,isOptional:v!=null}),v?"/?([^\\/]+)?":"/([^\\/]+)"));return n.endsWith("*")?(s.push({paramName:"*"}),o+=n==="*"||n==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):i?o+="\\/*$":n!==""&&n!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,l?void 0:"i"),s]}function Lg(n){try{return n.split("/").map(l=>decodeURIComponent(l).replace(/\//g,"%2F")).join("/")}catch(l){return ln(!1,`The URL path "${n}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${l}).`),n}}function Zn(n,l){if(l==="/")return n;if(!n.toLowerCase().startsWith(l.toLowerCase()))return null;let i=l.endsWith("/")?l.length-1:l.length,s=n.charAt(i);return s&&s!=="/"?null:n.slice(i)||"/"}function kg(n,l="/"){let{pathname:i,search:s="",hash:o=""}=typeof n=="string"?jl(n):n;return{pathname:i?i.startsWith("/")?i:Bg(i,l):l,search:$g(s),hash:Qg(o)}}function Bg(n,l){let i=l.replace(/\/+$/,"").split("/");return n.split("/").forEach(o=>{o===".."?i.length>1&&i.pop():o!=="."&&i.push(o)}),i.length>1?i.join("/"):"/"}function io(n,l,i,s){return`Cannot include a '${n}' character in a manually specified \`to.${l}\` field [${JSON.stringify(s)}].  Please separate it out to the \`to.${i}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function Vg(n){return n.filter((l,i)=>i===0||l.route.path&&l.route.path.length>0)}function Ro(n){let l=Vg(n);return l.map((i,s)=>s===l.length-1?i.pathname:i.pathnameBase)}function Do(n,l,i,s=!1){let o;typeof n=="string"?o=jl(n):(o={...n},$e(!o.pathname||!o.pathname.includes("?"),io("?","pathname","search",o)),$e(!o.pathname||!o.pathname.includes("#"),io("#","pathname","hash",o)),$e(!o.search||!o.search.includes("#"),io("#","search","hash",o)));let f=n===""||o.pathname==="",h=f?"/":o.pathname,y;if(h==null)y=i;else{let w=l.length-1;if(!s&&h.startsWith("..")){let A=h.split("/");for(;A[0]==="..";)A.shift(),w-=1;o.pathname=A.join("/")}y=w>=0?l[w]:"/"}let v=kg(o,y),m=h&&h!=="/"&&h.endsWith("/"),x=(f||h===".")&&i.endsWith("/");return!v.pathname.endsWith("/")&&(m||x)&&(v.pathname+="/"),v}var Un=n=>n.join("/").replace(/\/\/+/g,"/"),Yg=n=>n.replace(/\/+$/,"").replace(/^\/*/,"/"),$g=n=>!n||n==="?"?"":n.startsWith("?")?n:"?"+n,Qg=n=>!n||n==="#"?"":n.startsWith("#")?n:"#"+n;function Gg(n){return n!=null&&typeof n.status=="number"&&typeof n.statusText=="string"&&typeof n.internal=="boolean"&&"data"in n}var tp=["POST","PUT","PATCH","DELETE"];new Set(tp);var Xg=["GET",...tp];new Set(Xg);var Ml=R.createContext(null);Ml.displayName="DataRouter";var Dr=R.createContext(null);Dr.displayName="DataRouterState";var np=R.createContext({isTransitioning:!1});np.displayName="ViewTransition";var Kg=R.createContext(new Map);Kg.displayName="Fetchers";var Fg=R.createContext(null);Fg.displayName="Await";var un=R.createContext(null);un.displayName="Navigation";var Ku=R.createContext(null);Ku.displayName="Location";var yn=R.createContext({outlet:null,matches:[],isDataRoute:!1});yn.displayName="Route";var jo=R.createContext(null);jo.displayName="RouteError";function Jg(n,{relative:l}={}){$e(Cl(),"useHref() may be used only in the context of a <Router> component.");let{basename:i,navigator:s}=R.useContext(un),{hash:o,pathname:f,search:h}=Fu(n,{relative:l}),y=f;return i!=="/"&&(y=f==="/"?i:Un([i,f])),s.createHref({pathname:y,search:h,hash:o})}function Cl(){return R.useContext(Ku)!=null}function qn(){return $e(Cl(),"useLocation() may be used only in the context of a <Router> component."),R.useContext(Ku).location}var ap="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function lp(n){R.useContext(un).static||R.useLayoutEffect(n)}function up(){let{isDataRoute:n}=R.useContext(yn);return n?c1():Pg()}function Pg(){$e(Cl(),"useNavigate() may be used only in the context of a <Router> component.");let n=R.useContext(Ml),{basename:l,navigator:i}=R.useContext(un),{matches:s}=R.useContext(yn),{pathname:o}=qn(),f=JSON.stringify(Ro(s)),h=R.useRef(!1);return lp(()=>{h.current=!0}),R.useCallback((v,m={})=>{if(ln(h.current,ap),!h.current)return;if(typeof v=="number"){i.go(v);return}let x=Do(v,JSON.parse(f),o,m.relative==="path");n==null&&l!=="/"&&(x.pathname=x.pathname==="/"?l:Un([l,x.pathname])),(m.replace?i.replace:i.push)(x,m.state,m)},[l,i,f,o,n])}R.createContext(null);function Fu(n,{relative:l}={}){let{matches:i}=R.useContext(yn),{pathname:s}=qn(),o=JSON.stringify(Ro(i));return R.useMemo(()=>Do(n,JSON.parse(o),s,l==="path"),[n,o,s,l])}function Wg(n,l){return ip(n,l)}function ip(n,l,i,s){$e(Cl(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o}=R.useContext(un),{matches:f}=R.useContext(yn),h=f[f.length-1],y=h?h.params:{},v=h?h.pathname:"/",m=h?h.pathnameBase:"/",x=h&&h.route;{let Z=x&&x.path||"";rp(v,!x||Z.endsWith("*")||Z.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${v}" (under <Route path="${Z}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${Z}"> to <Route path="${Z==="/"?"*":`${Z}/*`}">.`)}let w=qn(),A;if(l){let Z=typeof l=="string"?jl(l):l;$e(m==="/"||Z.pathname?.startsWith(m),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${Z.pathname}" was given in the \`location\` prop.`),A=Z}else A=w;let L=A.pathname||"/",C=L;if(m!=="/"){let Z=m.replace(/^\//,"").split("/");C="/"+L.replace(/^\//,"").split("/").slice(Z.length).join("/")}let M=Wy(n,{pathname:C});ln(x||M!=null,`No routes matched location "${A.pathname}${A.search}${A.hash}" `),ln(M==null||M[M.length-1].route.element!==void 0||M[M.length-1].route.Component!==void 0||M[M.length-1].route.lazy!==void 0,`Matched leaf route at location "${A.pathname}${A.search}${A.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let G=a1(M&&M.map(Z=>Object.assign({},Z,{params:Object.assign({},y,Z.params),pathname:Un([m,o.encodeLocation?o.encodeLocation(Z.pathname).pathname:Z.pathname]),pathnameBase:Z.pathnameBase==="/"?m:Un([m,o.encodeLocation?o.encodeLocation(Z.pathnameBase).pathname:Z.pathnameBase])})),f,i,s);return l&&G?R.createElement(Ku.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...A},navigationType:"POP"}},G):G}function Ig(){let n=s1(),l=Gg(n)?`${n.status} ${n.statusText}`:n instanceof Error?n.message:JSON.stringify(n),i=n instanceof Error?n.stack:null,s="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:s},f={padding:"2px 4px",backgroundColor:s},h=null;return console.error("Error handled by React Router default ErrorBoundary:",n),h=R.createElement(R.Fragment,null,R.createElement("p",null,"💿 Hey developer 👋"),R.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",R.createElement("code",{style:f},"ErrorBoundary")," or"," ",R.createElement("code",{style:f},"errorElement")," prop on your route.")),R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},l),i?R.createElement("pre",{style:o},i):null,h)}var e1=R.createElement(Ig,null),t1=class extends R.Component{constructor(n){super(n),this.state={location:n.location,revalidation:n.revalidation,error:n.error}}static getDerivedStateFromError(n){return{error:n}}static getDerivedStateFromProps(n,l){return l.location!==n.location||l.revalidation!=="idle"&&n.revalidation==="idle"?{error:n.error,location:n.location,revalidation:n.revalidation}:{error:n.error!==void 0?n.error:l.error,location:l.location,revalidation:n.revalidation||l.revalidation}}componentDidCatch(n,l){console.error("React Router caught the following error during render",n,l)}render(){return this.state.error!==void 0?R.createElement(yn.Provider,{value:this.props.routeContext},R.createElement(jo.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function n1({routeContext:n,match:l,children:i}){let s=R.useContext(Ml);return s&&s.static&&s.staticContext&&(l.route.errorElement||l.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=l.route.id),R.createElement(yn.Provider,{value:n},i)}function a1(n,l=[],i=null,s=null){if(n==null){if(!i)return null;if(i.errors)n=i.matches;else if(l.length===0&&!i.initialized&&i.matches.length>0)n=i.matches;else return null}let o=n,f=i?.errors;if(f!=null){let v=o.findIndex(m=>m.route.id&&f?.[m.route.id]!==void 0);$e(v>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(f).join(",")}`),o=o.slice(0,Math.min(o.length,v+1))}let h=!1,y=-1;if(i)for(let v=0;v<o.length;v++){let m=o[v];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(y=v),m.route.id){let{loaderData:x,errors:w}=i,A=m.route.loader&&!x.hasOwnProperty(m.route.id)&&(!w||w[m.route.id]===void 0);if(m.route.lazy||A){h=!0,y>=0?o=o.slice(0,y+1):o=[o[0]];break}}}return o.reduceRight((v,m,x)=>{let w,A=!1,L=null,C=null;i&&(w=f&&m.route.id?f[m.route.id]:void 0,L=m.route.errorElement||e1,h&&(y<0&&x===0?(rp("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),A=!0,C=null):y===x&&(A=!0,C=m.route.hydrateFallbackElement||null)));let M=l.concat(o.slice(0,x+1)),G=()=>{let Z;return w?Z=L:A?Z=C:m.route.Component?Z=R.createElement(m.route.Component,null):m.route.element?Z=m.route.element:Z=v,R.createElement(n1,{match:m,routeContext:{outlet:v,matches:M,isDataRoute:i!=null},children:Z})};return i&&(m.route.ErrorBoundary||m.route.errorElement||x===0)?R.createElement(t1,{location:i.location,revalidation:i.revalidation,component:L,error:w,children:G(),routeContext:{outlet:null,matches:M,isDataRoute:!0}}):G()},null)}function Mo(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function l1(n){let l=R.useContext(Ml);return $e(l,Mo(n)),l}function u1(n){let l=R.useContext(Dr);return $e(l,Mo(n)),l}function i1(n){let l=R.useContext(yn);return $e(l,Mo(n)),l}function Co(n){let l=i1(n),i=l.matches[l.matches.length-1];return $e(i.route.id,`${n} can only be used on routes that contain a unique "id"`),i.route.id}function r1(){return Co("useRouteId")}function s1(){let n=R.useContext(jo),l=u1("useRouteError"),i=Co("useRouteError");return n!==void 0?n:l.errors?.[i]}function c1(){let{router:n}=l1("useNavigate"),l=Co("useNavigate"),i=R.useRef(!1);return lp(()=>{i.current=!0}),R.useCallback(async(o,f={})=>{ln(i.current,ap),i.current&&(typeof o=="number"?n.navigate(o):await n.navigate(o,{fromRouteId:l,...f}))},[n,l])}var ay={};function rp(n,l,i){!l&&!ay[n]&&(ay[n]=!0,ln(!1,i))}R.memo(o1);function o1({routes:n,future:l,state:i}){return ip(n,void 0,i,l)}function ro({to:n,replace:l,state:i,relative:s}){$e(Cl(),"<Navigate> may be used only in the context of a <Router> component.");let{static:o}=R.useContext(un);ln(!o,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:f}=R.useContext(yn),{pathname:h}=qn(),y=up(),v=Do(n,Ro(f),h,s==="path"),m=JSON.stringify(v);return R.useEffect(()=>{y(JSON.parse(m),{replace:l,state:i,relative:s})},[y,m,s,l,i]),null}function _t(n){$e(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function f1({basename:n="/",children:l=null,location:i,navigationType:s="POP",navigator:o,static:f=!1}){$e(!Cl(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let h=n.replace(/^\/*/,"/"),y=R.useMemo(()=>({basename:h,navigator:o,static:f,future:{}}),[h,o,f]);typeof i=="string"&&(i=jl(i));let{pathname:v="/",search:m="",hash:x="",state:w=null,key:A="default"}=i,L=R.useMemo(()=>{let C=Zn(v,h);return C==null?null:{location:{pathname:C,search:m,hash:x,state:w,key:A},navigationType:s}},[h,v,m,x,w,A,s]);return ln(L!=null,`<Router basename="${h}"> is not able to match the URL "${v}${m}${x}" because it does not start with the basename, so the <Router> won't render anything.`),L==null?null:R.createElement(un.Provider,{value:y},R.createElement(Ku.Provider,{children:l,value:L}))}function ly({children:n,location:l}){return Wg(ho(n),l)}function ho(n,l=[]){let i=[];return R.Children.forEach(n,(s,o)=>{if(!R.isValidElement(s))return;let f=[...l,o];if(s.type===R.Fragment){i.push.apply(i,ho(s.props.children,f));return}$e(s.type===_t,`[${typeof s.type=="string"?s.type:s.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),$e(!s.props.index||!s.props.children,"An index route cannot have child routes.");let h={id:s.props.id||f.join("-"),caseSensitive:s.props.caseSensitive,element:s.props.element,Component:s.props.Component,index:s.props.index,path:s.props.path,loader:s.props.loader,action:s.props.action,hydrateFallbackElement:s.props.hydrateFallbackElement,HydrateFallback:s.props.HydrateFallback,errorElement:s.props.errorElement,ErrorBoundary:s.props.ErrorBoundary,hasErrorBoundary:s.props.hasErrorBoundary===!0||s.props.ErrorBoundary!=null||s.props.errorElement!=null,shouldRevalidate:s.props.shouldRevalidate,handle:s.props.handle,lazy:s.props.lazy};s.props.children&&(h.children=ho(s.props.children,f)),i.push(h)}),i}var br="get",xr="application/x-www-form-urlencoded";function jr(n){return n!=null&&typeof n.tagName=="string"}function d1(n){return jr(n)&&n.tagName.toLowerCase()==="button"}function h1(n){return jr(n)&&n.tagName.toLowerCase()==="form"}function m1(n){return jr(n)&&n.tagName.toLowerCase()==="input"}function y1(n){return!!(n.metaKey||n.altKey||n.ctrlKey||n.shiftKey)}function p1(n,l){return n.button===0&&(!l||l==="_self")&&!y1(n)}var dr=null;function v1(){if(dr===null)try{new FormData(document.createElement("form"),0),dr=!1}catch{dr=!0}return dr}var g1=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function so(n){return n!=null&&!g1.has(n)?(ln(!1,`"${n}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${xr}"`),null):n}function b1(n,l){let i,s,o,f,h;if(h1(n)){let y=n.getAttribute("action");s=y?Zn(y,l):null,i=n.getAttribute("method")||br,o=so(n.getAttribute("enctype"))||xr,f=new FormData(n)}else if(d1(n)||m1(n)&&(n.type==="submit"||n.type==="image")){let y=n.form;if(y==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let v=n.getAttribute("formaction")||y.getAttribute("action");if(s=v?Zn(v,l):null,i=n.getAttribute("formmethod")||y.getAttribute("method")||br,o=so(n.getAttribute("formenctype"))||so(y.getAttribute("enctype"))||xr,f=new FormData(y,n),!v1()){let{name:m,type:x,value:w}=n;if(x==="image"){let A=m?`${m}.`:"";f.append(`${A}x`,"0"),f.append(`${A}y`,"0")}else m&&f.append(m,w)}}else{if(jr(n))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');i=br,s=null,o=xr,h=n}return f&&o==="text/plain"&&(h=f,f=void 0),{action:s,method:i.toLowerCase(),encType:o,formData:f,body:h}}function Uo(n,l){if(n===!1||n===null||typeof n>"u")throw new Error(l)}async function x1(n,l){if(n.id in l)return l[n.id];try{let i=await import(n.module);return l[n.id]=i,i}catch(i){return console.error(`Error loading route module \`${n.module}\`, reloading page...`),console.error(i),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function S1(n){return n==null?!1:n.href==null?n.rel==="preload"&&typeof n.imageSrcSet=="string"&&typeof n.imageSizes=="string":typeof n.rel=="string"&&typeof n.href=="string"}async function _1(n,l,i){let s=await Promise.all(n.map(async o=>{let f=l.routes[o.route.id];if(f){let h=await x1(f,i);return h.links?h.links():[]}return[]}));return z1(s.flat(1).filter(S1).filter(o=>o.rel==="stylesheet"||o.rel==="preload").map(o=>o.rel==="stylesheet"?{...o,rel:"prefetch",as:"style"}:{...o,rel:"prefetch"}))}function uy(n,l,i,s,o,f){let h=(v,m)=>i[m]?v.route.id!==i[m].route.id:!0,y=(v,m)=>i[m].pathname!==v.pathname||i[m].route.path?.endsWith("*")&&i[m].params["*"]!==v.params["*"];return f==="assets"?l.filter((v,m)=>h(v,m)||y(v,m)):f==="data"?l.filter((v,m)=>{let x=s.routes[v.route.id];if(!x||!x.hasLoader)return!1;if(h(v,m)||y(v,m))return!0;if(v.route.shouldRevalidate){let w=v.route.shouldRevalidate({currentUrl:new URL(o.pathname+o.search+o.hash,window.origin),currentParams:i[0]?.params||{},nextUrl:new URL(n,window.origin),nextParams:v.params,defaultShouldRevalidate:!0});if(typeof w=="boolean")return w}return!0}):[]}function E1(n,l,{includeHydrateFallback:i}={}){return A1(n.map(s=>{let o=l.routes[s.route.id];if(!o)return[];let f=[o.module];return o.clientActionModule&&(f=f.concat(o.clientActionModule)),o.clientLoaderModule&&(f=f.concat(o.clientLoaderModule)),i&&o.hydrateFallbackModule&&(f=f.concat(o.hydrateFallbackModule)),o.imports&&(f=f.concat(o.imports)),f}).flat(1))}function A1(n){return[...new Set(n)]}function w1(n){let l={},i=Object.keys(n).sort();for(let s of i)l[s]=n[s];return l}function z1(n,l){let i=new Set;return new Set(l),n.reduce((s,o)=>{let f=JSON.stringify(w1(o));return i.has(f)||(i.add(f),s.push({key:f,link:o})),s},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var T1=new Set([100,101,204,205]);function O1(n,l){let i=typeof n=="string"?new URL(n,typeof window>"u"?"server://singlefetch/":window.location.origin):n;return i.pathname==="/"?i.pathname="_root.data":l&&Zn(i.pathname,l)==="/"?i.pathname=`${l.replace(/\/$/,"")}/_root.data`:i.pathname=`${i.pathname.replace(/\/$/,"")}.data`,i}function sp(){let n=R.useContext(Ml);return Uo(n,"You must render this element inside a <DataRouterContext.Provider> element"),n}function N1(){let n=R.useContext(Dr);return Uo(n,"You must render this element inside a <DataRouterStateContext.Provider> element"),n}var Zo=R.createContext(void 0);Zo.displayName="FrameworkContext";function cp(){let n=R.useContext(Zo);return Uo(n,"You must render this element inside a <HydratedRouter> element"),n}function R1(n,l){let i=R.useContext(Zo),[s,o]=R.useState(!1),[f,h]=R.useState(!1),{onFocus:y,onBlur:v,onMouseEnter:m,onMouseLeave:x,onTouchStart:w}=l,A=R.useRef(null);R.useEffect(()=>{if(n==="render"&&h(!0),n==="viewport"){let M=Z=>{Z.forEach(B=>{h(B.isIntersecting)})},G=new IntersectionObserver(M,{threshold:.5});return A.current&&G.observe(A.current),()=>{G.disconnect()}}},[n]),R.useEffect(()=>{if(s){let M=setTimeout(()=>{h(!0)},100);return()=>{clearTimeout(M)}}},[s]);let L=()=>{o(!0)},C=()=>{o(!1),h(!1)};return i?n!=="intent"?[f,A,{}]:[f,A,{onFocus:Uu(y,L),onBlur:Uu(v,C),onMouseEnter:Uu(m,L),onMouseLeave:Uu(x,C),onTouchStart:Uu(w,L)}]:[!1,A,{}]}function Uu(n,l){return i=>{n&&n(i),i.defaultPrevented||l(i)}}function D1({page:n,...l}){let{router:i}=sp(),s=R.useMemo(()=>Wy(i.routes,n,i.basename),[i.routes,n,i.basename]);return s?R.createElement(M1,{page:n,matches:s,...l}):null}function j1(n){let{manifest:l,routeModules:i}=cp(),[s,o]=R.useState([]);return R.useEffect(()=>{let f=!1;return _1(n,l,i).then(h=>{f||o(h)}),()=>{f=!0}},[n,l,i]),s}function M1({page:n,matches:l,...i}){let s=qn(),{manifest:o,routeModules:f}=cp(),{basename:h}=sp(),{loaderData:y,matches:v}=N1(),m=R.useMemo(()=>uy(n,l,v,o,s,"data"),[n,l,v,o,s]),x=R.useMemo(()=>uy(n,l,v,o,s,"assets"),[n,l,v,o,s]),w=R.useMemo(()=>{if(n===s.pathname+s.search+s.hash)return[];let C=new Set,M=!1;if(l.forEach(Z=>{let B=o.routes[Z.route.id];!B||!B.hasLoader||(!m.some(Q=>Q.route.id===Z.route.id)&&Z.route.id in y&&f[Z.route.id]?.shouldRevalidate||B.hasClientLoader?M=!0:C.add(Z.route.id))}),C.size===0)return[];let G=O1(n,h);return M&&C.size>0&&G.searchParams.set("_routes",l.filter(Z=>C.has(Z.route.id)).map(Z=>Z.route.id).join(",")),[G.pathname+G.search]},[h,y,s,o,m,l,n,f]),A=R.useMemo(()=>E1(x,o),[x,o]),L=j1(x);return R.createElement(R.Fragment,null,w.map(C=>R.createElement("link",{key:C,rel:"prefetch",as:"fetch",href:C,...i})),A.map(C=>R.createElement("link",{key:C,rel:"modulepreload",href:C,...i})),L.map(({key:C,link:M})=>R.createElement("link",{key:C,...M})))}function C1(...n){return l=>{n.forEach(i=>{typeof i=="function"?i(l):i!=null&&(i.current=l)})}}var op=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{op&&(window.__reactRouterVersion="7.6.3")}catch{}function U1({basename:n,children:l,window:i}){let s=R.useRef();s.current==null&&(s.current=Eg({window:i,v5Compat:!0}));let o=s.current,[f,h]=R.useState({action:o.action,location:o.location}),y=R.useCallback(v=>{R.startTransition(()=>h(v))},[h]);return R.useLayoutEffect(()=>o.listen(y),[o,y]),R.createElement(f1,{basename:n,children:l,location:f.location,navigationType:f.action,navigator:o})}var fp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Dl=R.forwardRef(function({onClick:l,discover:i="render",prefetch:s="none",relative:o,reloadDocument:f,replace:h,state:y,target:v,to:m,preventScrollReset:x,viewTransition:w,...A},L){let{basename:C}=R.useContext(un),M=typeof m=="string"&&fp.test(m),G,Z=!1;if(typeof m=="string"&&M&&(G=m,op))try{let be=new URL(window.location.href),Ge=m.startsWith("//")?new URL(be.protocol+m):new URL(m),Xe=Zn(Ge.pathname,C);Ge.origin===be.origin&&Xe!=null?m=Xe+Ge.search+Ge.hash:Z=!0}catch{ln(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let B=Jg(m,{relative:o}),[Q,ee,F]=R1(s,A),ze=L1(m,{replace:h,state:y,target:v,preventScrollReset:x,relative:o,viewTransition:w});function Ee(be){l&&l(be),be.defaultPrevented||ze(be)}let Me=R.createElement("a",{...A,...F,href:G||B,onClick:Z||f?l:Ee,ref:C1(L,ee),target:v,"data-discover":!M&&i==="render"?"true":void 0});return Q&&!M?R.createElement(R.Fragment,null,Me,R.createElement(D1,{page:B})):Me});Dl.displayName="Link";var Z1=R.forwardRef(function({"aria-current":l="page",caseSensitive:i=!1,className:s="",end:o=!1,style:f,to:h,viewTransition:y,children:v,...m},x){let w=Fu(h,{relative:m.relative}),A=qn(),L=R.useContext(Dr),{navigator:C,basename:M}=R.useContext(un),G=L!=null&&$1(w)&&y===!0,Z=C.encodeLocation?C.encodeLocation(w).pathname:w.pathname,B=A.pathname,Q=L&&L.navigation&&L.navigation.location?L.navigation.location.pathname:null;i||(B=B.toLowerCase(),Q=Q?Q.toLowerCase():null,Z=Z.toLowerCase()),Q&&M&&(Q=Zn(Q,M)||Q);const ee=Z!=="/"&&Z.endsWith("/")?Z.length-1:Z.length;let F=B===Z||!o&&B.startsWith(Z)&&B.charAt(ee)==="/",ze=Q!=null&&(Q===Z||!o&&Q.startsWith(Z)&&Q.charAt(Z.length)==="/"),Ee={isActive:F,isPending:ze,isTransitioning:G},Me=F?l:void 0,be;typeof s=="function"?be=s(Ee):be=[s,F?"active":null,ze?"pending":null,G?"transitioning":null].filter(Boolean).join(" ");let Ge=typeof f=="function"?f(Ee):f;return R.createElement(Dl,{...m,"aria-current":Me,className:be,ref:x,style:Ge,to:h,viewTransition:y},typeof v=="function"?v(Ee):v)});Z1.displayName="NavLink";var H1=R.forwardRef(({discover:n="render",fetcherKey:l,navigate:i,reloadDocument:s,replace:o,state:f,method:h=br,action:y,onSubmit:v,relative:m,preventScrollReset:x,viewTransition:w,...A},L)=>{let C=V1(),M=Y1(y,{relative:m}),G=h.toLowerCase()==="get"?"get":"post",Z=typeof y=="string"&&fp.test(y),B=Q=>{if(v&&v(Q),Q.defaultPrevented)return;Q.preventDefault();let ee=Q.nativeEvent.submitter,F=ee?.getAttribute("formmethod")||h;C(ee||Q.currentTarget,{fetcherKey:l,method:F,navigate:i,replace:o,state:f,relative:m,preventScrollReset:x,viewTransition:w})};return R.createElement("form",{ref:L,method:G,action:M,onSubmit:s?v:B,...A,"data-discover":!Z&&n==="render"?"true":void 0})});H1.displayName="Form";function q1(n){return`${n} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function dp(n){let l=R.useContext(Ml);return $e(l,q1(n)),l}function L1(n,{target:l,replace:i,state:s,preventScrollReset:o,relative:f,viewTransition:h}={}){let y=up(),v=qn(),m=Fu(n,{relative:f});return R.useCallback(x=>{if(p1(x,l)){x.preventDefault();let w=i!==void 0?i:Yu(v)===Yu(m);y(n,{replace:w,state:s,preventScrollReset:o,relative:f,viewTransition:h})}},[v,y,m,i,s,l,n,o,f,h])}var k1=0,B1=()=>`__${String(++k1)}__`;function V1(){let{router:n}=dp("useSubmit"),{basename:l}=R.useContext(un),i=r1();return R.useCallback(async(s,o={})=>{let{action:f,method:h,encType:y,formData:v,body:m}=b1(s,l);if(o.navigate===!1){let x=o.fetcherKey||B1();await n.fetch(x,i,o.action||f,{preventScrollReset:o.preventScrollReset,formData:v,body:m,formMethod:o.method||h,formEncType:o.encType||y,flushSync:o.flushSync})}else await n.navigate(o.action||f,{preventScrollReset:o.preventScrollReset,formData:v,body:m,formMethod:o.method||h,formEncType:o.encType||y,replace:o.replace,state:o.state,fromRouteId:i,flushSync:o.flushSync,viewTransition:o.viewTransition})},[n,l,i])}function Y1(n,{relative:l}={}){let{basename:i}=R.useContext(un),s=R.useContext(yn);$e(s,"useFormAction must be used inside a RouteContext");let[o]=s.matches.slice(-1),f={...Fu(n||".",{relative:l})},h=qn();if(n==null){f.search=h.search;let y=new URLSearchParams(f.search),v=y.getAll("index");if(v.some(x=>x==="")){y.delete("index"),v.filter(w=>w).forEach(w=>y.append("index",w));let x=y.toString();f.search=x?`?${x}`:""}}return(!n||n===".")&&o.route.index&&(f.search=f.search?f.search.replace(/^\?/,"?index&"):"?index"),i!=="/"&&(f.pathname=f.pathname==="/"?i:Un([i,f.pathname])),Yu(f)}function $1(n,l={}){let i=R.useContext(np);$e(i!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:s}=dp("useViewTransitionState"),o=Fu(n,{relative:l.relative});if(!i.isTransitioning)return!1;let f=Zn(i.currentLocation.pathname,s)||i.currentLocation.pathname,h=Zn(i.nextLocation.pathname,s)||i.nextLocation.pathname;return Er(o.pathname,h)!=null||Er(o.pathname,f)!=null}[...T1];var Mr=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(n){return this.listeners.add(n),this.onSubscribe(),()=>{this.listeners.delete(n),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Cr=typeof window>"u"||"Deno"in globalThis;function tn(){}function Q1(n,l){return typeof n=="function"?n(l):n}function G1(n){return typeof n=="number"&&n>=0&&n!==1/0}function X1(n,l){return Math.max(n+(l||0)-Date.now(),0)}function mo(n,l){return typeof n=="function"?n(l):n}function K1(n,l){return typeof n=="function"?n(l):n}function iy(n,l){const{type:i="all",exact:s,fetchStatus:o,predicate:f,queryKey:h,stale:y}=n;if(h){if(s){if(l.queryHash!==Ho(h,l.options))return!1}else if(!Qu(l.queryKey,h))return!1}if(i!=="all"){const v=l.isActive();if(i==="active"&&!v||i==="inactive"&&v)return!1}return!(typeof y=="boolean"&&l.isStale()!==y||o&&o!==l.state.fetchStatus||f&&!f(l))}function ry(n,l){const{exact:i,status:s,predicate:o,mutationKey:f}=n;if(f){if(!l.options.mutationKey)return!1;if(i){if($u(l.options.mutationKey)!==$u(f))return!1}else if(!Qu(l.options.mutationKey,f))return!1}return!(s&&l.state.status!==s||o&&!o(l))}function Ho(n,l){return(l?.queryKeyHashFn||$u)(n)}function $u(n){return JSON.stringify(n,(l,i)=>yo(i)?Object.keys(i).sort().reduce((s,o)=>(s[o]=i[o],s),{}):i)}function Qu(n,l){return n===l?!0:typeof n!=typeof l?!1:n&&l&&typeof n=="object"&&typeof l=="object"?Object.keys(l).every(i=>Qu(n[i],l[i])):!1}function hp(n,l){if(n===l)return n;const i=sy(n)&&sy(l);if(i||yo(n)&&yo(l)){const s=i?n:Object.keys(n),o=s.length,f=i?l:Object.keys(l),h=f.length,y=i?[]:{},v=new Set(s);let m=0;for(let x=0;x<h;x++){const w=i?x:f[x];(!i&&v.has(w)||i)&&n[w]===void 0&&l[w]===void 0?(y[w]=void 0,m++):(y[w]=hp(n[w],l[w]),y[w]===n[w]&&n[w]!==void 0&&m++)}return o===h&&m===o?n:y}return l}function sy(n){return Array.isArray(n)&&n.length===Object.keys(n).length}function yo(n){if(!cy(n))return!1;const l=n.constructor;if(l===void 0)return!0;const i=l.prototype;return!(!cy(i)||!i.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(n)!==Object.prototype)}function cy(n){return Object.prototype.toString.call(n)==="[object Object]"}function F1(n){return new Promise(l=>{setTimeout(l,n)})}function J1(n,l,i){return typeof i.structuralSharing=="function"?i.structuralSharing(n,l):i.structuralSharing!==!1?hp(n,l):l}function P1(n,l,i=0){const s=[...n,l];return i&&s.length>i?s.slice(1):s}function W1(n,l,i=0){const s=[l,...n];return i&&s.length>i?s.slice(0,-1):s}var qo=Symbol();function mp(n,l){return!n.queryFn&&l?.initialPromise?()=>l.initialPromise:!n.queryFn||n.queryFn===qo?()=>Promise.reject(new Error(`Missing queryFn: '${n.queryHash}'`)):n.queryFn}var I1=class extends Mr{#e;#t;#n;constructor(){super(),this.#n=n=>{if(!Cr&&window.addEventListener){const l=()=>n();return window.addEventListener("visibilitychange",l,!1),()=>{window.removeEventListener("visibilitychange",l)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(n){this.#n=n,this.#t?.(),this.#t=n(l=>{typeof l=="boolean"?this.setFocused(l):this.onFocus()})}setFocused(n){this.#e!==n&&(this.#e=n,this.onFocus())}onFocus(){const n=this.isFocused();this.listeners.forEach(l=>{l(n)})}isFocused(){return typeof this.#e=="boolean"?this.#e:globalThis.document?.visibilityState!=="hidden"}},yp=new I1,eb=class extends Mr{#e=!0;#t;#n;constructor(){super(),this.#n=n=>{if(!Cr&&window.addEventListener){const l=()=>n(!0),i=()=>n(!1);return window.addEventListener("online",l,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",l),window.removeEventListener("offline",i)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(n){this.#n=n,this.#t?.(),this.#t=n(this.setOnline.bind(this))}setOnline(n){this.#e!==n&&(this.#e=n,this.listeners.forEach(i=>{i(n)}))}isOnline(){return this.#e}},Ar=new eb;function tb(){let n,l;const i=new Promise((o,f)=>{n=o,l=f});i.status="pending",i.catch(()=>{});function s(o){Object.assign(i,o),delete i.resolve,delete i.reject}return i.resolve=o=>{s({status:"fulfilled",value:o}),n(o)},i.reject=o=>{s({status:"rejected",reason:o}),l(o)},i}function nb(n){return Math.min(1e3*2**n,3e4)}function pp(n){return(n??"online")==="online"?Ar.isOnline():!0}var vp=class extends Error{constructor(n){super("CancelledError"),this.revert=n?.revert,this.silent=n?.silent}};function co(n){return n instanceof vp}function gp(n){let l=!1,i=0,s=!1,o;const f=tb(),h=M=>{s||(A(new vp(M)),n.abort?.())},y=()=>{l=!0},v=()=>{l=!1},m=()=>yp.isFocused()&&(n.networkMode==="always"||Ar.isOnline())&&n.canRun(),x=()=>pp(n.networkMode)&&n.canRun(),w=M=>{s||(s=!0,n.onSuccess?.(M),o?.(),f.resolve(M))},A=M=>{s||(s=!0,n.onError?.(M),o?.(),f.reject(M))},L=()=>new Promise(M=>{o=G=>{(s||m())&&M(G)},n.onPause?.()}).then(()=>{o=void 0,s||n.onContinue?.()}),C=()=>{if(s)return;let M;const G=i===0?n.initialPromise:void 0;try{M=G??n.fn()}catch(Z){M=Promise.reject(Z)}Promise.resolve(M).then(w).catch(Z=>{if(s)return;const B=n.retry??(Cr?0:3),Q=n.retryDelay??nb,ee=typeof Q=="function"?Q(i,Z):Q,F=B===!0||typeof B=="number"&&i<B||typeof B=="function"&&B(i,Z);if(l||!F){A(Z);return}i++,n.onFail?.(i,Z),F1(ee).then(()=>m()?void 0:L()).then(()=>{l?A(Z):C()})})};return{promise:f,cancel:h,continue:()=>(o?.(),f),cancelRetry:y,continueRetry:v,canStart:x,start:()=>(x()?C():L().then(C),f)}}var ab=n=>setTimeout(n,0);function lb(){let n=[],l=0,i=y=>{y()},s=y=>{y()},o=ab;const f=y=>{l?n.push(y):o(()=>{i(y)})},h=()=>{const y=n;n=[],y.length&&o(()=>{s(()=>{y.forEach(v=>{i(v)})})})};return{batch:y=>{let v;l++;try{v=y()}finally{l--,l||h()}return v},batchCalls:y=>(...v)=>{f(()=>{y(...v)})},schedule:f,setNotifyFunction:y=>{i=y},setBatchNotifyFunction:y=>{s=y},setScheduler:y=>{o=y}}}var At=lb(),bp=class{#e;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),G1(this.gcTime)&&(this.#e=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(n){this.gcTime=Math.max(this.gcTime||0,n??(Cr?1/0:5*60*1e3))}clearGcTimeout(){this.#e&&(clearTimeout(this.#e),this.#e=void 0)}},ub=class extends bp{#e;#t;#n;#l;#a;#i;#r;constructor(n){super(),this.#r=!1,this.#i=n.defaultOptions,this.setOptions(n.options),this.observers=[],this.#l=n.client,this.#n=this.#l.getQueryCache(),this.queryKey=n.queryKey,this.queryHash=n.queryHash,this.#e=rb(this.options),this.state=n.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#a?.promise}setOptions(n){this.options={...this.#i,...n},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#n.remove(this)}setData(n,l){const i=J1(this.state.data,n,this.options);return this.#u({data:i,type:"success",dataUpdatedAt:l?.updatedAt,manual:l?.manual}),i}setState(n,l){this.#u({type:"setState",state:n,setStateOptions:l})}cancel(n){const l=this.#a?.promise;return this.#a?.cancel(n),l?l.then(tn).catch(tn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(n=>K1(n.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===qo||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(n=>mo(n.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(n=>n.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(n=0){return this.state.data===void 0?!0:n==="static"?!1:this.state.isInvalidated?!0:!X1(this.state.dataUpdatedAt,n)}onFocus(){this.observers.find(l=>l.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#a?.continue()}onOnline(){this.observers.find(l=>l.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#a?.continue()}addObserver(n){this.observers.includes(n)||(this.observers.push(n),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:n}))}removeObserver(n){this.observers.includes(n)&&(this.observers=this.observers.filter(l=>l!==n),this.observers.length||(this.#a&&(this.#r?this.#a.cancel({revert:!0}):this.#a.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:n}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#u({type:"invalidate"})}fetch(n,l){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&l?.cancelRefetch)this.cancel({silent:!0});else if(this.#a)return this.#a.continueRetry(),this.#a.promise}if(n&&this.setOptions(n),!this.options.queryFn){const v=this.observers.find(m=>m.options.queryFn);v&&this.setOptions(v.options)}const i=new AbortController,s=v=>{Object.defineProperty(v,"signal",{enumerable:!0,get:()=>(this.#r=!0,i.signal)})},o=()=>{const v=mp(this.options,l),x=(()=>{const w={client:this.#l,queryKey:this.queryKey,meta:this.meta};return s(w),w})();return this.#r=!1,this.options.persister?this.options.persister(v,x,this):v(x)},h=(()=>{const v={fetchOptions:l,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:o};return s(v),v})();this.options.behavior?.onFetch(h,this),this.#t=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==h.fetchOptions?.meta)&&this.#u({type:"fetch",meta:h.fetchOptions?.meta});const y=v=>{co(v)&&v.silent||this.#u({type:"error",error:v}),co(v)||(this.#n.config.onError?.(v,this),this.#n.config.onSettled?.(this.state.data,v,this)),this.scheduleGc()};return this.#a=gp({initialPromise:l?.initialPromise,fn:h.fetchFn,abort:i.abort.bind(i),onSuccess:v=>{if(v===void 0){y(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(v)}catch(m){y(m);return}this.#n.config.onSuccess?.(v,this),this.#n.config.onSettled?.(v,this.state.error,this),this.scheduleGc()},onError:y,onFail:(v,m)=>{this.#u({type:"failed",failureCount:v,error:m})},onPause:()=>{this.#u({type:"pause"})},onContinue:()=>{this.#u({type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay,networkMode:h.options.networkMode,canRun:()=>!0}),this.#a.start()}#u(n){const l=i=>{switch(n.type){case"failed":return{...i,fetchFailureCount:n.failureCount,fetchFailureReason:n.error};case"pause":return{...i,fetchStatus:"paused"};case"continue":return{...i,fetchStatus:"fetching"};case"fetch":return{...i,...ib(i.data,this.options),fetchMeta:n.meta??null};case"success":return this.#t=void 0,{...i,data:n.data,dataUpdateCount:i.dataUpdateCount+1,dataUpdatedAt:n.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!n.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=n.error;return co(s)&&s.revert&&this.#t?{...this.#t,fetchStatus:"idle"}:{...i,error:s,errorUpdateCount:i.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:i.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...i,isInvalidated:!0};case"setState":return{...i,...n.state}}};this.state=l(this.state),At.batch(()=>{this.observers.forEach(i=>{i.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:n})})}};function ib(n,l){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:pp(l.networkMode)?"fetching":"paused",...n===void 0&&{error:null,status:"pending"}}}function rb(n){const l=typeof n.initialData=="function"?n.initialData():n.initialData,i=l!==void 0,s=i?typeof n.initialDataUpdatedAt=="function"?n.initialDataUpdatedAt():n.initialDataUpdatedAt:0;return{data:l,dataUpdateCount:0,dataUpdatedAt:i?s??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:i?"success":"pending",fetchStatus:"idle"}}var sb=class extends Mr{constructor(n={}){super(),this.config=n,this.#e=new Map}#e;build(n,l,i){const s=l.queryKey,o=l.queryHash??Ho(s,l);let f=this.get(o);return f||(f=new ub({client:n,queryKey:s,queryHash:o,options:n.defaultQueryOptions(l),state:i,defaultOptions:n.getQueryDefaults(s)}),this.add(f)),f}add(n){this.#e.has(n.queryHash)||(this.#e.set(n.queryHash,n),this.notify({type:"added",query:n}))}remove(n){const l=this.#e.get(n.queryHash);l&&(n.destroy(),l===n&&this.#e.delete(n.queryHash),this.notify({type:"removed",query:n}))}clear(){At.batch(()=>{this.getAll().forEach(n=>{this.remove(n)})})}get(n){return this.#e.get(n)}getAll(){return[...this.#e.values()]}find(n){const l={exact:!0,...n};return this.getAll().find(i=>iy(l,i))}findAll(n={}){const l=this.getAll();return Object.keys(n).length>0?l.filter(i=>iy(n,i)):l}notify(n){At.batch(()=>{this.listeners.forEach(l=>{l(n)})})}onFocus(){At.batch(()=>{this.getAll().forEach(n=>{n.onFocus()})})}onOnline(){At.batch(()=>{this.getAll().forEach(n=>{n.onOnline()})})}},cb=class extends bp{#e;#t;#n;constructor(n){super(),this.mutationId=n.mutationId,this.#t=n.mutationCache,this.#e=[],this.state=n.state||ob(),this.setOptions(n.options),this.scheduleGc()}setOptions(n){this.options=n,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(n){this.#e.includes(n)||(this.#e.push(n),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:n}))}removeObserver(n){this.#e=this.#e.filter(l=>l!==n),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:n})}optionalRemove(){this.#e.length||(this.state.status==="pending"?this.scheduleGc():this.#t.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(n){const l=()=>{this.#l({type:"continue"})};this.#n=gp({fn:()=>this.options.mutationFn?this.options.mutationFn(n):Promise.reject(new Error("No mutationFn found")),onFail:(o,f)=>{this.#l({type:"failed",failureCount:o,error:f})},onPause:()=>{this.#l({type:"pause"})},onContinue:l,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});const i=this.state.status==="pending",s=!this.#n.canStart();try{if(i)l();else{this.#l({type:"pending",variables:n,isPaused:s}),await this.#t.config.onMutate?.(n,this);const f=await this.options.onMutate?.(n);f!==this.state.context&&this.#l({type:"pending",context:f,variables:n,isPaused:s})}const o=await this.#n.start();return await this.#t.config.onSuccess?.(o,n,this.state.context,this),await this.options.onSuccess?.(o,n,this.state.context),await this.#t.config.onSettled?.(o,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(o,null,n,this.state.context),this.#l({type:"success",data:o}),o}catch(o){try{throw await this.#t.config.onError?.(o,n,this.state.context,this),await this.options.onError?.(o,n,this.state.context),await this.#t.config.onSettled?.(void 0,o,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,o,n,this.state.context),o}finally{this.#l({type:"error",error:o})}}finally{this.#t.runNext(this)}}#l(n){const l=i=>{switch(n.type){case"failed":return{...i,failureCount:n.failureCount,failureReason:n.error};case"pause":return{...i,isPaused:!0};case"continue":return{...i,isPaused:!1};case"pending":return{...i,context:n.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:n.isPaused,status:"pending",variables:n.variables,submittedAt:Date.now()};case"success":return{...i,data:n.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...i,data:void 0,error:n.error,failureCount:i.failureCount+1,failureReason:n.error,isPaused:!1,status:"error"}}};this.state=l(this.state),At.batch(()=>{this.#e.forEach(i=>{i.onMutationUpdate(n)}),this.#t.notify({mutation:this,type:"updated",action:n})})}};function ob(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var fb=class extends Mr{constructor(n={}){super(),this.config=n,this.#e=new Set,this.#t=new Map,this.#n=0}#e;#t;#n;build(n,l,i){const s=new cb({mutationCache:this,mutationId:++this.#n,options:n.defaultMutationOptions(l),state:i});return this.add(s),s}add(n){this.#e.add(n);const l=hr(n);if(typeof l=="string"){const i=this.#t.get(l);i?i.push(n):this.#t.set(l,[n])}this.notify({type:"added",mutation:n})}remove(n){if(this.#e.delete(n)){const l=hr(n);if(typeof l=="string"){const i=this.#t.get(l);if(i)if(i.length>1){const s=i.indexOf(n);s!==-1&&i.splice(s,1)}else i[0]===n&&this.#t.delete(l)}}this.notify({type:"removed",mutation:n})}canRun(n){const l=hr(n);if(typeof l=="string"){const s=this.#t.get(l)?.find(o=>o.state.status==="pending");return!s||s===n}else return!0}runNext(n){const l=hr(n);return typeof l=="string"?this.#t.get(l)?.find(s=>s!==n&&s.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){At.batch(()=>{this.#e.forEach(n=>{this.notify({type:"removed",mutation:n})}),this.#e.clear(),this.#t.clear()})}getAll(){return Array.from(this.#e)}find(n){const l={exact:!0,...n};return this.getAll().find(i=>ry(l,i))}findAll(n={}){return this.getAll().filter(l=>ry(n,l))}notify(n){At.batch(()=>{this.listeners.forEach(l=>{l(n)})})}resumePausedMutations(){const n=this.getAll().filter(l=>l.state.isPaused);return At.batch(()=>Promise.all(n.map(l=>l.continue().catch(tn))))}};function hr(n){return n.options.scope?.id}function oy(n){return{onFetch:(l,i)=>{const s=l.options,o=l.fetchOptions?.meta?.fetchMore?.direction,f=l.state.data?.pages||[],h=l.state.data?.pageParams||[];let y={pages:[],pageParams:[]},v=0;const m=async()=>{let x=!1;const w=C=>{Object.defineProperty(C,"signal",{enumerable:!0,get:()=>(l.signal.aborted?x=!0:l.signal.addEventListener("abort",()=>{x=!0}),l.signal)})},A=mp(l.options,l.fetchOptions),L=async(C,M,G)=>{if(x)return Promise.reject();if(M==null&&C.pages.length)return Promise.resolve(C);const B=(()=>{const ze={client:l.client,queryKey:l.queryKey,pageParam:M,direction:G?"backward":"forward",meta:l.options.meta};return w(ze),ze})(),Q=await A(B),{maxPages:ee}=l.options,F=G?W1:P1;return{pages:F(C.pages,Q,ee),pageParams:F(C.pageParams,M,ee)}};if(o&&f.length){const C=o==="backward",M=C?db:fy,G={pages:f,pageParams:h},Z=M(s,G);y=await L(G,Z,C)}else{const C=n??f.length;do{const M=v===0?h[0]??s.initialPageParam:fy(s,y);if(v>0&&M==null)break;y=await L(y,M),v++}while(v<C)}return y};l.options.persister?l.fetchFn=()=>l.options.persister?.(m,{client:l.client,queryKey:l.queryKey,meta:l.options.meta,signal:l.signal},i):l.fetchFn=m}}}function fy(n,{pages:l,pageParams:i}){const s=l.length-1;return l.length>0?n.getNextPageParam(l[s],l,i[s],i):void 0}function db(n,{pages:l,pageParams:i}){return l.length>0?n.getPreviousPageParam?.(l[0],l,i[0],i):void 0}var hb=class{#e;#t;#n;#l;#a;#i;#r;#u;constructor(n={}){this.#e=n.queryCache||new sb,this.#t=n.mutationCache||new fb,this.#n=n.defaultOptions||{},this.#l=new Map,this.#a=new Map,this.#i=0}mount(){this.#i++,this.#i===1&&(this.#r=yp.subscribe(async n=>{n&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#u=Ar.subscribe(async n=>{n&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#i--,this.#i===0&&(this.#r?.(),this.#r=void 0,this.#u?.(),this.#u=void 0)}isFetching(n){return this.#e.findAll({...n,fetchStatus:"fetching"}).length}isMutating(n){return this.#t.findAll({...n,status:"pending"}).length}getQueryData(n){const l=this.defaultQueryOptions({queryKey:n});return this.#e.get(l.queryHash)?.state.data}ensureQueryData(n){const l=this.defaultQueryOptions(n),i=this.#e.build(this,l),s=i.state.data;return s===void 0?this.fetchQuery(n):(n.revalidateIfStale&&i.isStaleByTime(mo(l.staleTime,i))&&this.prefetchQuery(l),Promise.resolve(s))}getQueriesData(n){return this.#e.findAll(n).map(({queryKey:l,state:i})=>{const s=i.data;return[l,s]})}setQueryData(n,l,i){const s=this.defaultQueryOptions({queryKey:n}),f=this.#e.get(s.queryHash)?.state.data,h=Q1(l,f);if(h!==void 0)return this.#e.build(this,s).setData(h,{...i,manual:!0})}setQueriesData(n,l,i){return At.batch(()=>this.#e.findAll(n).map(({queryKey:s})=>[s,this.setQueryData(s,l,i)]))}getQueryState(n){const l=this.defaultQueryOptions({queryKey:n});return this.#e.get(l.queryHash)?.state}removeQueries(n){const l=this.#e;At.batch(()=>{l.findAll(n).forEach(i=>{l.remove(i)})})}resetQueries(n,l){const i=this.#e;return At.batch(()=>(i.findAll(n).forEach(s=>{s.reset()}),this.refetchQueries({type:"active",...n},l)))}cancelQueries(n,l={}){const i={revert:!0,...l},s=At.batch(()=>this.#e.findAll(n).map(o=>o.cancel(i)));return Promise.all(s).then(tn).catch(tn)}invalidateQueries(n,l={}){return At.batch(()=>(this.#e.findAll(n).forEach(i=>{i.invalidate()}),n?.refetchType==="none"?Promise.resolve():this.refetchQueries({...n,type:n?.refetchType??n?.type??"active"},l)))}refetchQueries(n,l={}){const i={...l,cancelRefetch:l.cancelRefetch??!0},s=At.batch(()=>this.#e.findAll(n).filter(o=>!o.isDisabled()&&!o.isStatic()).map(o=>{let f=o.fetch(void 0,i);return i.throwOnError||(f=f.catch(tn)),o.state.fetchStatus==="paused"?Promise.resolve():f}));return Promise.all(s).then(tn)}fetchQuery(n){const l=this.defaultQueryOptions(n);l.retry===void 0&&(l.retry=!1);const i=this.#e.build(this,l);return i.isStaleByTime(mo(l.staleTime,i))?i.fetch(l):Promise.resolve(i.state.data)}prefetchQuery(n){return this.fetchQuery(n).then(tn).catch(tn)}fetchInfiniteQuery(n){return n.behavior=oy(n.pages),this.fetchQuery(n)}prefetchInfiniteQuery(n){return this.fetchInfiniteQuery(n).then(tn).catch(tn)}ensureInfiniteQueryData(n){return n.behavior=oy(n.pages),this.ensureQueryData(n)}resumePausedMutations(){return Ar.isOnline()?this.#t.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#t}getDefaultOptions(){return this.#n}setDefaultOptions(n){this.#n=n}setQueryDefaults(n,l){this.#l.set($u(n),{queryKey:n,defaultOptions:l})}getQueryDefaults(n){const l=[...this.#l.values()],i={};return l.forEach(s=>{Qu(n,s.queryKey)&&Object.assign(i,s.defaultOptions)}),i}setMutationDefaults(n,l){this.#a.set($u(n),{mutationKey:n,defaultOptions:l})}getMutationDefaults(n){const l=[...this.#a.values()],i={};return l.forEach(s=>{Qu(n,s.mutationKey)&&Object.assign(i,s.defaultOptions)}),i}defaultQueryOptions(n){if(n._defaulted)return n;const l={...this.#n.queries,...this.getQueryDefaults(n.queryKey),...n,_defaulted:!0};return l.queryHash||(l.queryHash=Ho(l.queryKey,l)),l.refetchOnReconnect===void 0&&(l.refetchOnReconnect=l.networkMode!=="always"),l.throwOnError===void 0&&(l.throwOnError=!!l.suspense),!l.networkMode&&l.persister&&(l.networkMode="offlineFirst"),l.queryFn===qo&&(l.enabled=!1),l}defaultMutationOptions(n){return n?._defaulted?n:{...this.#n.mutations,...n?.mutationKey&&this.getMutationDefaults(n.mutationKey),...n,_defaulted:!0}}clear(){this.#e.clear(),this.#t.clear()}},mb=R.createContext(void 0),yb=({client:n,children:l})=>(R.useEffect(()=>(n.mount(),()=>{n.unmount()}),[n]),g.jsx(mb.Provider,{value:n,children:l}));let pb={data:""},vb=n=>typeof window=="object"?((n?n.querySelector("#_goober"):window._goober)||Object.assign((n||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:n||pb,gb=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,bb=/\/\*[^]*?\*\/|  +/g,dy=/\n+/g,fa=(n,l)=>{let i="",s="",o="";for(let f in n){let h=n[f];f[0]=="@"?f[1]=="i"?i=f+" "+h+";":s+=f[1]=="f"?fa(h,f):f+"{"+fa(h,f[1]=="k"?"":l)+"}":typeof h=="object"?s+=fa(h,l?l.replace(/([^,])+/g,y=>f.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,v=>/&/.test(v)?v.replace(/&/g,y):y?y+" "+v:v)):f):h!=null&&(f=/^--/.test(f)?f:f.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=fa.p?fa.p(f,h):f+":"+h+";")}return i+(l&&o?l+"{"+o+"}":o)+s},Mn={},xp=n=>{if(typeof n=="object"){let l="";for(let i in n)l+=i+xp(n[i]);return l}return n},xb=(n,l,i,s,o)=>{let f=xp(n),h=Mn[f]||(Mn[f]=(v=>{let m=0,x=11;for(;m<v.length;)x=101*x+v.charCodeAt(m++)>>>0;return"go"+x})(f));if(!Mn[h]){let v=f!==n?n:(m=>{let x,w,A=[{}];for(;x=gb.exec(m.replace(bb,""));)x[4]?A.shift():x[3]?(w=x[3].replace(dy," ").trim(),A.unshift(A[0][w]=A[0][w]||{})):A[0][x[1]]=x[2].replace(dy," ").trim();return A[0]})(n);Mn[h]=fa(o?{["@keyframes "+h]:v}:v,i?"":"."+h)}let y=i&&Mn.g?Mn.g:null;return i&&(Mn.g=Mn[h]),((v,m,x,w)=>{w?m.data=m.data.replace(w,v):m.data.indexOf(v)===-1&&(m.data=x?v+m.data:m.data+v)})(Mn[h],l,s,y),h},Sb=(n,l,i)=>n.reduce((s,o,f)=>{let h=l[f];if(h&&h.call){let y=h(i),v=y&&y.props&&y.props.className||/^go/.test(y)&&y;h=v?"."+v:y&&typeof y=="object"?y.props?"":fa(y,""):y===!1?"":y}return s+o+(h??"")},"");function Ur(n){let l=this||{},i=n.call?n(l.p):n;return xb(i.unshift?i.raw?Sb(i,[].slice.call(arguments,1),l.p):i.reduce((s,o)=>Object.assign(s,o&&o.call?o(l.p):o),{}):i,vb(l.target),l.g,l.o,l.k)}let Sp,po,vo;Ur.bind({g:1});let Hn=Ur.bind({k:1});function _b(n,l,i,s){fa.p=l,Sp=n,po=i,vo=s}function ha(n,l){let i=this||{};return function(){let s=arguments;function o(f,h){let y=Object.assign({},f),v=y.className||o.className;i.p=Object.assign({theme:po&&po()},y),i.o=/ *go\d+/.test(v),y.className=Ur.apply(i,s)+(v?" "+v:"");let m=n;return n[0]&&(m=y.as||n,delete y.as),vo&&m[0]&&vo(y),Sp(m,y)}return o}}var Eb=n=>typeof n=="function",wr=(n,l)=>Eb(n)?n(l):n,Ab=(()=>{let n=0;return()=>(++n).toString()})(),_p=(()=>{let n;return()=>{if(n===void 0&&typeof window<"u"){let l=matchMedia("(prefers-reduced-motion: reduce)");n=!l||l.matches}return n}})(),wb=20,Ep=(n,l)=>{switch(l.type){case 0:return{...n,toasts:[l.toast,...n.toasts].slice(0,wb)};case 1:return{...n,toasts:n.toasts.map(f=>f.id===l.toast.id?{...f,...l.toast}:f)};case 2:let{toast:i}=l;return Ep(n,{type:n.toasts.find(f=>f.id===i.id)?1:0,toast:i});case 3:let{toastId:s}=l;return{...n,toasts:n.toasts.map(f=>f.id===s||s===void 0?{...f,dismissed:!0,visible:!1}:f)};case 4:return l.toastId===void 0?{...n,toasts:[]}:{...n,toasts:n.toasts.filter(f=>f.id!==l.toastId)};case 5:return{...n,pausedAt:l.time};case 6:let o=l.time-(n.pausedAt||0);return{...n,pausedAt:void 0,toasts:n.toasts.map(f=>({...f,pauseDuration:f.pauseDuration+o}))}}},Sr=[],Ca={toasts:[],pausedAt:void 0},La=n=>{Ca=Ep(Ca,n),Sr.forEach(l=>{l(Ca)})},zb={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Tb=(n={})=>{let[l,i]=R.useState(Ca),s=R.useRef(Ca);R.useEffect(()=>(s.current!==Ca&&i(Ca),Sr.push(i),()=>{let f=Sr.indexOf(i);f>-1&&Sr.splice(f,1)}),[]);let o=l.toasts.map(f=>{var h,y,v;return{...n,...n[f.type],...f,removeDelay:f.removeDelay||((h=n[f.type])==null?void 0:h.removeDelay)||n?.removeDelay,duration:f.duration||((y=n[f.type])==null?void 0:y.duration)||n?.duration||zb[f.type],style:{...n.style,...(v=n[f.type])==null?void 0:v.style,...f.style}}});return{...l,toasts:o}},Ob=(n,l="blank",i)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:l,ariaProps:{role:"status","aria-live":"polite"},message:n,pauseDuration:0,...i,id:i?.id||Ab()}),Ju=n=>(l,i)=>{let s=Ob(l,n,i);return La({type:2,toast:s}),s.id},wt=(n,l)=>Ju("blank")(n,l);wt.error=Ju("error");wt.success=Ju("success");wt.loading=Ju("loading");wt.custom=Ju("custom");wt.dismiss=n=>{La({type:3,toastId:n})};wt.remove=n=>La({type:4,toastId:n});wt.promise=(n,l,i)=>{let s=wt.loading(l.loading,{...i,...i?.loading});return typeof n=="function"&&(n=n()),n.then(o=>{let f=l.success?wr(l.success,o):void 0;return f?wt.success(f,{id:s,...i,...i?.success}):wt.dismiss(s),o}).catch(o=>{let f=l.error?wr(l.error,o):void 0;f?wt.error(f,{id:s,...i,...i?.error}):wt.dismiss(s)}),n};var Nb=(n,l)=>{La({type:1,toast:{id:n,height:l}})},Rb=()=>{La({type:5,time:Date.now()})},Lu=new Map,Db=1e3,jb=(n,l=Db)=>{if(Lu.has(n))return;let i=setTimeout(()=>{Lu.delete(n),La({type:4,toastId:n})},l);Lu.set(n,i)},Mb=n=>{let{toasts:l,pausedAt:i}=Tb(n);R.useEffect(()=>{if(i)return;let f=Date.now(),h=l.map(y=>{if(y.duration===1/0)return;let v=(y.duration||0)+y.pauseDuration-(f-y.createdAt);if(v<0){y.visible&&wt.dismiss(y.id);return}return setTimeout(()=>wt.dismiss(y.id),v)});return()=>{h.forEach(y=>y&&clearTimeout(y))}},[l,i]);let s=R.useCallback(()=>{i&&La({type:6,time:Date.now()})},[i]),o=R.useCallback((f,h)=>{let{reverseOrder:y=!1,gutter:v=8,defaultPosition:m}=h||{},x=l.filter(L=>(L.position||m)===(f.position||m)&&L.height),w=x.findIndex(L=>L.id===f.id),A=x.filter((L,C)=>C<w&&L.visible).length;return x.filter(L=>L.visible).slice(...y?[A+1]:[0,A]).reduce((L,C)=>L+(C.height||0)+v,0)},[l]);return R.useEffect(()=>{l.forEach(f=>{if(f.dismissed)jb(f.id,f.removeDelay);else{let h=Lu.get(f.id);h&&(clearTimeout(h),Lu.delete(f.id))}})},[l]),{toasts:l,handlers:{updateHeight:Nb,startPause:Rb,endPause:s,calculateOffset:o}}},Cb=Hn`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Ub=Hn`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Zb=Hn`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Hb=ha("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Cb} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Ub} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${n=>n.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Zb} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,qb=Hn`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Lb=ha("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${n=>n.secondary||"#e0e0e0"};
  border-right-color: ${n=>n.primary||"#616161"};
  animation: ${qb} 1s linear infinite;
`,kb=Hn`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Bb=Hn`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Vb=ha("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${n=>n.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${kb} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Bb} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${n=>n.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Yb=ha("div")`
  position: absolute;
`,$b=ha("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Qb=Hn`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Gb=ha("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Qb} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Xb=({toast:n})=>{let{icon:l,type:i,iconTheme:s}=n;return l!==void 0?typeof l=="string"?R.createElement(Gb,null,l):l:i==="blank"?null:R.createElement($b,null,R.createElement(Lb,{...s}),i!=="loading"&&R.createElement(Yb,null,i==="error"?R.createElement(Hb,{...s}):R.createElement(Vb,{...s})))},Kb=n=>`
0% {transform: translate3d(0,${n*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Fb=n=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${n*-150}%,-1px) scale(.6); opacity:0;}
`,Jb="0%{opacity:0;} 100%{opacity:1;}",Pb="0%{opacity:1;} 100%{opacity:0;}",Wb=ha("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Ib=ha("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ex=(n,l)=>{let i=n.includes("top")?1:-1,[s,o]=_p()?[Jb,Pb]:[Kb(i),Fb(i)];return{animation:l?`${Hn(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Hn(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},tx=R.memo(({toast:n,position:l,style:i,children:s})=>{let o=n.height?ex(n.position||l||"top-center",n.visible):{opacity:0},f=R.createElement(Xb,{toast:n}),h=R.createElement(Ib,{...n.ariaProps},wr(n.message,n));return R.createElement(Wb,{className:n.className,style:{...o,...i,...n.style}},typeof s=="function"?s({icon:f,message:h}):R.createElement(R.Fragment,null,f,h))});_b(R.createElement);var nx=({id:n,className:l,style:i,onHeightUpdate:s,children:o})=>{let f=R.useCallback(h=>{if(h){let y=()=>{let v=h.getBoundingClientRect().height;s(n,v)};y(),new MutationObserver(y).observe(h,{subtree:!0,childList:!0,characterData:!0})}},[n,s]);return R.createElement("div",{ref:f,className:l,style:i},o)},ax=(n,l)=>{let i=n.includes("top"),s=i?{top:0}:{bottom:0},o=n.includes("center")?{justifyContent:"center"}:n.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:_p()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${l*(i?1:-1)}px)`,...s,...o}},lx=Ur`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,mr=16,ux=({reverseOrder:n,position:l="top-center",toastOptions:i,gutter:s,children:o,containerStyle:f,containerClassName:h})=>{let{toasts:y,handlers:v}=Mb(i);return R.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:mr,left:mr,right:mr,bottom:mr,pointerEvents:"none",...f},className:h,onMouseEnter:v.startPause,onMouseLeave:v.endPause},y.map(m=>{let x=m.position||l,w=v.calculateOffset(m,{reverseOrder:n,gutter:s,defaultPosition:l}),A=ax(x,w);return R.createElement(nx,{id:m.id,key:m.id,onHeightUpdate:v.updateHeight,className:m.visible?lx:"",style:A},m.type==="custom"?wr(m.message,m):o?o(m):R.createElement(tx,{toast:m,position:x}))}))},zr=wt;const hy=n=>{let l;const i=new Set,s=(m,x)=>{const w=typeof m=="function"?m(l):m;if(!Object.is(w,l)){const A=l;l=x??(typeof w!="object"||w===null)?w:Object.assign({},l,w),i.forEach(L=>L(l,A))}},o=()=>l,y={setState:s,getState:o,getInitialState:()=>v,subscribe:m=>(i.add(m),()=>i.delete(m))},v=l=n(s,o,y);return y},ix=n=>n?hy(n):hy,rx=n=>n;function sx(n,l=rx){const i=Dt.useSyncExternalStore(n.subscribe,()=>l(n.getState()),()=>l(n.getInitialState()));return Dt.useDebugValue(i),i}const my=n=>{const l=ix(n),i=s=>sx(l,s);return Object.assign(i,l),i},cx=n=>n?my(n):my;function ox(n,l){let i;try{i=n()}catch{return}return{getItem:o=>{var f;const h=v=>v===null?null:JSON.parse(v,void 0),y=(f=i.getItem(o))!=null?f:null;return y instanceof Promise?y.then(h):h(y)},setItem:(o,f)=>i.setItem(o,JSON.stringify(f,void 0)),removeItem:o=>i.removeItem(o)}}const go=n=>l=>{try{const i=n(l);return i instanceof Promise?i:{then(s){return go(s)(i)},catch(s){return this}}}catch(i){return{then(s){return this},catch(s){return go(s)(i)}}}},fx=(n,l)=>(i,s,o)=>{let f={storage:ox(()=>localStorage),partialize:M=>M,version:0,merge:(M,G)=>({...G,...M}),...l},h=!1;const y=new Set,v=new Set;let m=f.storage;if(!m)return n((...M)=>{console.warn(`[zustand persist middleware] Unable to update item '${f.name}', the given storage is currently unavailable.`),i(...M)},s,o);const x=()=>{const M=f.partialize({...s()});return m.setItem(f.name,{state:M,version:f.version})},w=o.setState;o.setState=(M,G)=>{w(M,G),x()};const A=n((...M)=>{i(...M),x()},s,o);o.getInitialState=()=>A;let L;const C=()=>{var M,G;if(!m)return;h=!1,y.forEach(B=>{var Q;return B((Q=s())!=null?Q:A)});const Z=((G=f.onRehydrateStorage)==null?void 0:G.call(f,(M=s())!=null?M:A))||void 0;return go(m.getItem.bind(m))(f.name).then(B=>{if(B)if(typeof B.version=="number"&&B.version!==f.version){if(f.migrate){const Q=f.migrate(B.state,B.version);return Q instanceof Promise?Q.then(ee=>[!0,ee]):[!0,Q]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,B.state];return[!1,void 0]}).then(B=>{var Q;const[ee,F]=B;if(L=f.merge(F,(Q=s())!=null?Q:A),i(L,!0),ee)return x()}).then(()=>{Z?.(L,void 0),L=s(),h=!0,v.forEach(B=>B(L))}).catch(B=>{Z?.(void 0,B)})};return o.persist={setOptions:M=>{f={...f,...M},M.storage&&(m=M.storage)},clearStorage:()=>{m?.removeItem(f.name)},getOptions:()=>f,rehydrate:()=>C(),hasHydrated:()=>h,onHydrate:M=>(y.add(M),()=>{y.delete(M)}),onFinishHydration:M=>(v.add(M),()=>{v.delete(M)})},f.skipHydration||C(),L||A},dx=fx,Pu=cx(dx((n,l)=>({user:null,isLoading:!1,login:async(i,s)=>{try{const o={id:"1",email:i,firstName:"John",lastName:"Doe",role:"admin",phone:"+1234567890",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n({user:o})}catch(o){throw console.error("Login error:",o),o}},register:async i=>{try{const s={id:"1",email:i.email,firstName:i.firstName,lastName:i.lastName,phone:i.phone,role:i.role||"client",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n({user:s})}catch(s){throw console.error("Registration error:",s),s}},logout:async()=>{try{n({user:null})}catch(i){throw console.error("Logout error:",i),i}},updateProfile:async i=>{const{user:s}=l();if(!s)throw new Error("No user logged in");try{const o={...s,...i};n({user:o})}catch(o){throw console.error("Profile update error:",o),o}},checkAuth:async()=>{try{n({user:null,isLoading:!1})}catch(i){console.error("Auth check error:",i),n({user:null,isLoading:!1})}}}),{name:"auth-storage",partialize:n=>({user:n.user})}));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hx=n=>n.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),mx=n=>n.replace(/^([A-Z])|[\s-_]+(\w)/g,(l,i,s)=>s?s.toUpperCase():i.toLowerCase()),yy=n=>{const l=mx(n);return l.charAt(0).toUpperCase()+l.slice(1)},Ap=(...n)=>n.filter((l,i,s)=>!!l&&l.trim()!==""&&s.indexOf(l)===i).join(" ").trim(),yx=n=>{for(const l in n)if(l.startsWith("aria-")||l==="role"||l==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var px={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=R.forwardRef(({color:n="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:o="",children:f,iconNode:h,...y},v)=>R.createElement("svg",{ref:v,...px,width:l,height:l,stroke:n,strokeWidth:s?Number(i)*24/Number(l):i,className:Ap("lucide",o),...!f&&!yx(y)&&{"aria-hidden":"true"},...y},[...h.map(([m,x])=>R.createElement(m,x)),...Array.isArray(f)?f:[f]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mt=(n,l)=>{const i=R.forwardRef(({className:s,...o},f)=>R.createElement(vx,{ref:f,iconNode:l,className:Ap(`lucide-${hx(yy(n))}`,`lucide-${n}`,s),...o}));return i.displayName=yy(n),i};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Za=Mt("calendar",gx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],xx=Mt("chart-column",bx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],bo=Mt("clock",Sx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _x=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],Ex=Mt("credit-card",_x);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],py=Mt("dollar-sign",Ax);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wx=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],xo=Mt("eye-off",wx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zx=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],So=Mt("eye",zx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tx=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ox=Mt("house",Tx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nx=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],vy=Mt("log-out",Nx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],Dx=Mt("menu",Rx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Mx=Mt("settings",jx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],gy=Mt("user",Cx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ux=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],_o=Mt("users",Ux);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zx=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Hx=Mt("x",Zx),qx=({children:n})=>{const[l,i]=R.useState(!1),{user:s,logout:o}=Pu(),f=qn(),y=[{name:"Dashboard",href:"/dashboard",icon:Ox,roles:["admin","staff","client"]},{name:"Appointments",href:"/appointments",icon:Za,roles:["admin","staff"]},{name:"Book Appointment",href:"/book",icon:Za,roles:["client"]},{name:"My Appointments",href:"/my-appointments",icon:bo,roles:["client"]},{name:"Schedule",href:"/schedule",icon:bo,roles:["staff"]},{name:"Services",href:"/services",icon:Ex,roles:["admin"]},{name:"Users",href:"/users",icon:_o,roles:["admin"]},{name:"Reports",href:"/reports",icon:xx,roles:["admin"]},{name:"Settings",href:"/settings",icon:Mx,roles:["admin","staff","client"]}].filter(m=>m.roles.includes(s?.role||"client")),v=async()=>{try{await o()}catch(m){console.error("Logout failed:",m)}};return g.jsxs("div",{className:"min-h-screen bg-gray-50",children:[g.jsxs("div",{className:`fixed inset-0 z-50 lg:hidden ${l?"block":"hidden"}`,children:[g.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>i(!1)}),g.jsxs("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white",children:[g.jsxs("div",{className:"flex h-16 items-center justify-between px-4",children:[g.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"AMS"}),g.jsx("button",{onClick:()=>i(!1),children:g.jsx(Hx,{className:"h-6 w-6"})})]}),g.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:y.map(m=>{const x=f.pathname===m.href;return g.jsxs(Dl,{to:m.href,className:`sidebar-nav-item ${x?"active":""}`,onClick:()=>i(!1),children:[g.jsx(m.icon,{className:"h-5 w-5 mr-3"}),m.name]},m.name)})}),g.jsxs("div",{className:"border-t border-gray-200 p-4",children:[g.jsxs("div",{className:"flex items-center",children:[g.jsx("div",{className:"flex-shrink-0",children:g.jsx("div",{className:"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center",children:g.jsx(gy,{className:"h-5 w-5 text-white"})})}),g.jsxs("div",{className:"ml-3",children:[g.jsxs("p",{className:"text-sm font-medium text-gray-700",children:[s?.firstName," ",s?.lastName]}),g.jsx("p",{className:"text-xs text-gray-500 capitalize",children:s?.role})]})]}),g.jsxs("button",{onClick:v,className:"mt-3 flex w-full items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg",children:[g.jsx(vy,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})]}),g.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:g.jsxs("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[g.jsx("div",{className:"flex h-16 items-center px-4",children:g.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Appointment MS"})}),g.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:y.map(m=>{const x=f.pathname===m.href;return g.jsxs(Dl,{to:m.href,className:`sidebar-nav-item ${x?"active":""}`,children:[g.jsx(m.icon,{className:"h-5 w-5 mr-3"}),m.name]},m.name)})}),g.jsxs("div",{className:"border-t border-gray-200 p-4",children:[g.jsxs("div",{className:"flex items-center",children:[g.jsx("div",{className:"flex-shrink-0",children:g.jsx("div",{className:"h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center",children:g.jsx(gy,{className:"h-5 w-5 text-white"})})}),g.jsxs("div",{className:"ml-3",children:[g.jsxs("p",{className:"text-sm font-medium text-gray-700",children:[s?.firstName," ",s?.lastName]}),g.jsx("p",{className:"text-xs text-gray-500 capitalize",children:s?.role})]})]}),g.jsxs("button",{onClick:v,className:"mt-3 flex w-full items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg",children:[g.jsx(vy,{className:"h-4 w-4 mr-2"}),"Sign out"]})]})]})}),g.jsxs("div",{className:"lg:pl-64",children:[g.jsxs("div",{className:"flex h-16 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm lg:px-6",children:[g.jsx("button",{type:"button",className:"-m-2.5 p-2.5 text-gray-700 lg:hidden",onClick:()=>i(!0),children:g.jsx(Dx,{className:"h-6 w-6"})}),g.jsx("div",{className:"flex flex-1 gap-x-4 self-stretch lg:gap-x-6",children:g.jsx("div",{className:"flex flex-1"})})]}),g.jsx("main",{className:"py-6",children:g.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:n})})]})]})};var Wu=n=>n.type==="checkbox",Ua=n=>n instanceof Date,Et=n=>n==null;const wp=n=>typeof n=="object";var et=n=>!Et(n)&&!Array.isArray(n)&&wp(n)&&!Ua(n),Lx=n=>et(n)&&n.target?Wu(n.target)?n.target.checked:n.target.value:n,kx=n=>n.substring(0,n.search(/\.\d+(\.|$)/))||n,Bx=(n,l)=>n.has(kx(l)),Vx=n=>{const l=n.constructor&&n.constructor.prototype;return et(l)&&l.hasOwnProperty("isPrototypeOf")},Lo=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function dt(n){let l;const i=Array.isArray(n),s=typeof FileList<"u"?n instanceof FileList:!1;if(n instanceof Date)l=new Date(n);else if(!(Lo&&(n instanceof Blob||s))&&(i||et(n)))if(l=i?[]:{},!i&&!Vx(n))l=n;else for(const o in n)n.hasOwnProperty(o)&&(l[o]=dt(n[o]));else return n;return l}var Zr=n=>/^\w*$/.test(n),lt=n=>n===void 0,ko=n=>Array.isArray(n)?n.filter(Boolean):[],Bo=n=>ko(n.replace(/["|']|\]/g,"").split(/\.|\[/)),ne=(n,l,i)=>{if(!l||!et(n))return i;const s=(Zr(l)?[l]:Bo(l)).reduce((o,f)=>Et(o)?o:o[f],n);return lt(s)||s===n?lt(n[l])?i:n[l]:s},hn=n=>typeof n=="boolean",Le=(n,l,i)=>{let s=-1;const o=Zr(l)?[l]:Bo(l),f=o.length,h=f-1;for(;++s<f;){const y=o[s];let v=i;if(s!==h){const m=n[y];v=et(m)||Array.isArray(m)?m:isNaN(+o[s+1])?{}:[]}if(y==="__proto__"||y==="constructor"||y==="prototype")return;n[y]=v,n=n[y]}};const by={BLUR:"blur",FOCUS_OUT:"focusout"},nn={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Cn={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Yx=Dt.createContext(null);Yx.displayName="HookFormContext";var $x=(n,l,i,s=!0)=>{const o={defaultValues:l._defaultValues};for(const f in n)Object.defineProperty(o,f,{get:()=>{const h=f;return l._proxyFormState[h]!==nn.all&&(l._proxyFormState[h]=!s||nn.all),n[h]}});return o};const Qx=typeof window<"u"?R.useLayoutEffect:R.useEffect;var mn=n=>typeof n=="string",Gx=(n,l,i,s,o)=>mn(n)?(s&&l.watch.add(n),ne(i,n,o)):Array.isArray(n)?n.map(f=>(s&&l.watch.add(f),ne(i,f))):(s&&(l.watchAll=!0),i),Vo=(n,l,i,s,o)=>l?{...i[n],types:{...i[n]&&i[n].types?i[n].types:{},[s]:o||!0}}:{},ku=n=>Array.isArray(n)?n:[n],xy=()=>{let n=[];return{get observers(){return n},next:o=>{for(const f of n)f.next&&f.next(o)},subscribe:o=>(n.push(o),{unsubscribe:()=>{n=n.filter(f=>f!==o)}}),unsubscribe:()=>{n=[]}}},Eo=n=>Et(n)||!wp(n);function da(n,l,i=new WeakSet){if(Eo(n)||Eo(l))return n===l;if(Ua(n)&&Ua(l))return n.getTime()===l.getTime();const s=Object.keys(n),o=Object.keys(l);if(s.length!==o.length)return!1;if(i.has(n)||i.has(l))return!0;i.add(n),i.add(l);for(const f of s){const h=n[f];if(!o.includes(f))return!1;if(f!=="ref"){const y=l[f];if(Ua(h)&&Ua(y)||et(h)&&et(y)||Array.isArray(h)&&Array.isArray(y)?!da(h,y,i):h!==y)return!1}}return!0}var jt=n=>et(n)&&!Object.keys(n).length,Yo=n=>n.type==="file",an=n=>typeof n=="function",Tr=n=>{if(!Lo)return!1;const l=n?n.ownerDocument:0;return n instanceof(l&&l.defaultView?l.defaultView.HTMLElement:HTMLElement)},zp=n=>n.type==="select-multiple",$o=n=>n.type==="radio",Xx=n=>$o(n)||Wu(n),oo=n=>Tr(n)&&n.isConnected;function Kx(n,l){const i=l.slice(0,-1).length;let s=0;for(;s<i;)n=lt(n)?s++:n[l[s++]];return n}function Fx(n){for(const l in n)if(n.hasOwnProperty(l)&&!lt(n[l]))return!1;return!0}function at(n,l){const i=Array.isArray(l)?l:Zr(l)?[l]:Bo(l),s=i.length===1?n:Kx(n,i),o=i.length-1,f=i[o];return s&&delete s[f],o!==0&&(et(s)&&jt(s)||Array.isArray(s)&&Fx(s))&&at(n,i.slice(0,-1)),n}var Tp=n=>{for(const l in n)if(an(n[l]))return!0;return!1};function Or(n,l={}){const i=Array.isArray(n);if(et(n)||i)for(const s in n)Array.isArray(n[s])||et(n[s])&&!Tp(n[s])?(l[s]=Array.isArray(n[s])?[]:{},Or(n[s],l[s])):Et(n[s])||(l[s]=!0);return l}function Op(n,l,i){const s=Array.isArray(n);if(et(n)||s)for(const o in n)Array.isArray(n[o])||et(n[o])&&!Tp(n[o])?lt(l)||Eo(i[o])?i[o]=Array.isArray(n[o])?Or(n[o],[]):{...Or(n[o])}:Op(n[o],Et(l)?{}:l[o],i[o]):i[o]=!da(n[o],l[o]);return i}var Zu=(n,l)=>Op(n,l,Or(l));const Sy={value:!1,isValid:!1},_y={value:!0,isValid:!0};var Np=n=>{if(Array.isArray(n)){if(n.length>1){const l=n.filter(i=>i&&i.checked&&!i.disabled).map(i=>i.value);return{value:l,isValid:!!l.length}}return n[0].checked&&!n[0].disabled?n[0].attributes&&!lt(n[0].attributes.value)?lt(n[0].value)||n[0].value===""?_y:{value:n[0].value,isValid:!0}:_y:Sy}return Sy},Rp=(n,{valueAsNumber:l,valueAsDate:i,setValueAs:s})=>lt(n)?n:l?n===""?NaN:n&&+n:i&&mn(n)?new Date(n):s?s(n):n;const Ey={isValid:!1,value:null};var Dp=n=>Array.isArray(n)?n.reduce((l,i)=>i&&i.checked&&!i.disabled?{isValid:!0,value:i.value}:l,Ey):Ey;function Ay(n){const l=n.ref;return Yo(l)?l.files:$o(l)?Dp(n.refs).value:zp(l)?[...l.selectedOptions].map(({value:i})=>i):Wu(l)?Np(n.refs).value:Rp(lt(l.value)?n.ref.value:l.value,n)}var Jx=(n,l,i,s)=>{const o={};for(const f of n){const h=ne(l,f);h&&Le(o,f,h._f)}return{criteriaMode:i,names:[...n],fields:o,shouldUseNativeValidation:s}},Nr=n=>n instanceof RegExp,Hu=n=>lt(n)?n:Nr(n)?n.source:et(n)?Nr(n.value)?n.value.source:n.value:n,wy=n=>({isOnSubmit:!n||n===nn.onSubmit,isOnBlur:n===nn.onBlur,isOnChange:n===nn.onChange,isOnAll:n===nn.all,isOnTouch:n===nn.onTouched});const zy="AsyncFunction";var Px=n=>!!n&&!!n.validate&&!!(an(n.validate)&&n.validate.constructor.name===zy||et(n.validate)&&Object.values(n.validate).find(l=>l.constructor.name===zy)),Wx=n=>n.mount&&(n.required||n.min||n.max||n.maxLength||n.minLength||n.pattern||n.validate),Ty=(n,l,i)=>!i&&(l.watchAll||l.watch.has(n)||[...l.watch].some(s=>n.startsWith(s)&&/^\.\w+/.test(n.slice(s.length))));const Bu=(n,l,i,s)=>{for(const o of i||Object.keys(n)){const f=ne(n,o);if(f){const{_f:h,...y}=f;if(h){if(h.refs&&h.refs[0]&&l(h.refs[0],o)&&!s)return!0;if(h.ref&&l(h.ref,h.name)&&!s)return!0;if(Bu(y,l))break}else if(et(y)&&Bu(y,l))break}}};function Oy(n,l,i){const s=ne(n,i);if(s||Zr(i))return{error:s,name:i};const o=i.split(".");for(;o.length;){const f=o.join("."),h=ne(l,f),y=ne(n,f);if(h&&!Array.isArray(h)&&i!==f)return{name:i};if(y&&y.type)return{name:f,error:y};if(y&&y.root&&y.root.type)return{name:`${f}.root`,error:y.root};o.pop()}return{name:i}}var Ix=(n,l,i,s)=>{i(n);const{name:o,...f}=n;return jt(f)||Object.keys(f).length>=Object.keys(l).length||Object.keys(f).find(h=>l[h]===(!s||nn.all))},eS=(n,l,i)=>!n||!l||n===l||ku(n).some(s=>s&&(i?s===l:s.startsWith(l)||l.startsWith(s))),tS=(n,l,i,s,o)=>o.isOnAll?!1:!i&&o.isOnTouch?!(l||n):(i?s.isOnBlur:o.isOnBlur)?!n:(i?s.isOnChange:o.isOnChange)?n:!0,nS=(n,l)=>!ko(ne(n,l)).length&&at(n,l),aS=(n,l,i)=>{const s=ku(ne(n,i));return Le(s,"root",l[i]),Le(n,i,s),n},_r=n=>mn(n);function Ny(n,l,i="validate"){if(_r(n)||Array.isArray(n)&&n.every(_r)||hn(n)&&!n)return{type:i,message:_r(n)?n:"",ref:l}}var Rl=n=>et(n)&&!Nr(n)?n:{value:n,message:""},Ry=async(n,l,i,s,o,f)=>{const{ref:h,refs:y,required:v,maxLength:m,minLength:x,min:w,max:A,pattern:L,validate:C,name:M,valueAsNumber:G,mount:Z}=n._f,B=ne(i,M);if(!Z||l.has(M))return{};const Q=y?y[0]:h,ee=ce=>{o&&Q.reportValidity&&(Q.setCustomValidity(hn(ce)?"":ce||""),Q.reportValidity())},F={},ze=$o(h),Ee=Wu(h),Me=ze||Ee,be=(G||Yo(h))&&lt(h.value)&&lt(B)||Tr(h)&&h.value===""||B===""||Array.isArray(B)&&!B.length,Ge=Vo.bind(null,M,s,F),Xe=(ce,xe,Re,Te=Cn.maxLength,U=Cn.minLength)=>{const K=ce?xe:Re;F[M]={type:ce?Te:U,message:K,ref:h,...Ge(ce?Te:U,K)}};if(f?!Array.isArray(B)||!B.length:v&&(!Me&&(be||Et(B))||hn(B)&&!B||Ee&&!Np(y).isValid||ze&&!Dp(y).isValid)){const{value:ce,message:xe}=_r(v)?{value:!!v,message:v}:Rl(v);if(ce&&(F[M]={type:Cn.required,message:xe,ref:Q,...Ge(Cn.required,xe)},!s))return ee(xe),F}if(!be&&(!Et(w)||!Et(A))){let ce,xe;const Re=Rl(A),Te=Rl(w);if(!Et(B)&&!isNaN(B)){const U=h.valueAsNumber||B&&+B;Et(Re.value)||(ce=U>Re.value),Et(Te.value)||(xe=U<Te.value)}else{const U=h.valueAsDate||new Date(B),K=_=>new Date(new Date().toDateString()+" "+_),ue=h.type=="time",Ae=h.type=="week";mn(Re.value)&&B&&(ce=ue?K(B)>K(Re.value):Ae?B>Re.value:U>new Date(Re.value)),mn(Te.value)&&B&&(xe=ue?K(B)<K(Te.value):Ae?B<Te.value:U<new Date(Te.value))}if((ce||xe)&&(Xe(!!ce,Re.message,Te.message,Cn.max,Cn.min),!s))return ee(F[M].message),F}if((m||x)&&!be&&(mn(B)||f&&Array.isArray(B))){const ce=Rl(m),xe=Rl(x),Re=!Et(ce.value)&&B.length>+ce.value,Te=!Et(xe.value)&&B.length<+xe.value;if((Re||Te)&&(Xe(Re,ce.message,xe.message),!s))return ee(F[M].message),F}if(L&&!be&&mn(B)){const{value:ce,message:xe}=Rl(L);if(Nr(ce)&&!B.match(ce)&&(F[M]={type:Cn.pattern,message:xe,ref:h,...Ge(Cn.pattern,xe)},!s))return ee(xe),F}if(C){if(an(C)){const ce=await C(B,i),xe=Ny(ce,Q);if(xe&&(F[M]={...xe,...Ge(Cn.validate,xe.message)},!s))return ee(xe.message),F}else if(et(C)){let ce={};for(const xe in C){if(!jt(ce)&&!s)break;const Re=Ny(await C[xe](B,i),Q,xe);Re&&(ce={...Re,...Ge(xe,Re.message)},ee(Re.message),s&&(F[M]=ce))}if(!jt(ce)&&(F[M]={ref:Q,...ce},!s))return F}}return ee(!0),F};const lS={mode:nn.onSubmit,reValidateMode:nn.onChange,shouldFocusError:!0};function uS(n={}){let l={...lS,...n},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:an(l.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:l.errors||{},disabled:l.disabled||!1},s={},o=et(l.defaultValues)||et(l.values)?dt(l.defaultValues||l.values)||{}:{},f=l.shouldUnregister?{}:dt(o),h={action:!1,mount:!1,watch:!1},y={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},v,m=0;const x={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let w={...x};const A={array:xy(),state:xy()},L=l.criteriaMode===nn.all,C=S=>N=>{clearTimeout(m),m=setTimeout(S,N)},M=async S=>{if(!l.disabled&&(x.isValid||w.isValid||S)){const N=l.resolver?jt((await Ee()).errors):await be(s,!0);N!==i.isValid&&A.state.next({isValid:N})}},G=(S,N)=>{!l.disabled&&(x.isValidating||x.validatingFields||w.isValidating||w.validatingFields)&&((S||Array.from(y.mount)).forEach(q=>{q&&(N?Le(i.validatingFields,q,N):at(i.validatingFields,q))}),A.state.next({validatingFields:i.validatingFields,isValidating:!jt(i.validatingFields)}))},Z=(S,N=[],q,W,J=!0,X=!0)=>{if(W&&q&&!l.disabled){if(h.action=!0,X&&Array.isArray(ne(s,S))){const ae=q(ne(s,S),W.argA,W.argB);J&&Le(s,S,ae)}if(X&&Array.isArray(ne(i.errors,S))){const ae=q(ne(i.errors,S),W.argA,W.argB);J&&Le(i.errors,S,ae),nS(i.errors,S)}if((x.touchedFields||w.touchedFields)&&X&&Array.isArray(ne(i.touchedFields,S))){const ae=q(ne(i.touchedFields,S),W.argA,W.argB);J&&Le(i.touchedFields,S,ae)}(x.dirtyFields||w.dirtyFields)&&(i.dirtyFields=Zu(o,f)),A.state.next({name:S,isDirty:Xe(S,N),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else Le(f,S,N)},B=(S,N)=>{Le(i.errors,S,N),A.state.next({errors:i.errors})},Q=S=>{i.errors=S,A.state.next({errors:i.errors,isValid:!1})},ee=(S,N,q,W)=>{const J=ne(s,S);if(J){const X=ne(f,S,lt(q)?ne(o,S):q);lt(X)||W&&W.defaultChecked||N?Le(f,S,N?X:Ay(J._f)):Re(S,X),h.mount&&M()}},F=(S,N,q,W,J)=>{let X=!1,ae=!1;const he={name:S};if(!l.disabled){if(!q||W){(x.isDirty||w.isDirty)&&(ae=i.isDirty,i.isDirty=he.isDirty=Xe(),X=ae!==he.isDirty);const _e=da(ne(o,S),N);ae=!!ne(i.dirtyFields,S),_e?at(i.dirtyFields,S):Le(i.dirtyFields,S,!0),he.dirtyFields=i.dirtyFields,X=X||(x.dirtyFields||w.dirtyFields)&&ae!==!_e}if(q){const _e=ne(i.touchedFields,S);_e||(Le(i.touchedFields,S,q),he.touchedFields=i.touchedFields,X=X||(x.touchedFields||w.touchedFields)&&_e!==q)}X&&J&&A.state.next(he)}return X?he:{}},ze=(S,N,q,W)=>{const J=ne(i.errors,S),X=(x.isValid||w.isValid)&&hn(N)&&i.isValid!==N;if(l.delayError&&q?(v=C(()=>B(S,q)),v(l.delayError)):(clearTimeout(m),v=null,q?Le(i.errors,S,q):at(i.errors,S)),(q?!da(J,q):J)||!jt(W)||X){const ae={...W,...X&&hn(N)?{isValid:N}:{},errors:i.errors,name:S};i={...i,...ae},A.state.next(ae)}},Ee=async S=>{G(S,!0);const N=await l.resolver(f,l.context,Jx(S||y.mount,s,l.criteriaMode,l.shouldUseNativeValidation));return G(S),N},Me=async S=>{const{errors:N}=await Ee(S);if(S)for(const q of S){const W=ne(N,q);W?Le(i.errors,q,W):at(i.errors,q)}else i.errors=N;return N},be=async(S,N,q={valid:!0})=>{for(const W in S){const J=S[W];if(J){const{_f:X,...ae}=J;if(X){const he=y.array.has(X.name),_e=J._f&&Px(J._f);_e&&x.validatingFields&&G([W],!0);const ke=await Ry(J,y.disabled,f,L,l.shouldUseNativeValidation&&!N,he);if(_e&&x.validatingFields&&G([W]),ke[X.name]&&(q.valid=!1,N))break;!N&&(ne(ke,X.name)?he?aS(i.errors,ke,X.name):Le(i.errors,X.name,ke[X.name]):at(i.errors,X.name))}!jt(ae)&&await be(ae,N,q)}}return q.valid},Ge=()=>{for(const S of y.unMount){const N=ne(s,S);N&&(N._f.refs?N._f.refs.every(q=>!oo(q)):!oo(N._f.ref))&&mt(S)}y.unMount=new Set},Xe=(S,N)=>!l.disabled&&(S&&N&&Le(f,S,N),!da(_(),o)),ce=(S,N,q)=>Gx(S,y,{...h.mount?f:lt(N)?o:mn(S)?{[S]:N}:N},q,N),xe=S=>ko(ne(h.mount?f:o,S,l.shouldUnregister?ne(o,S,[]):[])),Re=(S,N,q={})=>{const W=ne(s,S);let J=N;if(W){const X=W._f;X&&(!X.disabled&&Le(f,S,Rp(N,X)),J=Tr(X.ref)&&Et(N)?"":N,zp(X.ref)?[...X.ref.options].forEach(ae=>ae.selected=J.includes(ae.value)):X.refs?Wu(X.ref)?X.refs.forEach(ae=>{(!ae.defaultChecked||!ae.disabled)&&(Array.isArray(J)?ae.checked=!!J.find(he=>he===ae.value):ae.checked=J===ae.value||!!J)}):X.refs.forEach(ae=>ae.checked=ae.value===J):Yo(X.ref)?X.ref.value="":(X.ref.value=J,X.ref.type||A.state.next({name:S,values:dt(f)})))}(q.shouldDirty||q.shouldTouch)&&F(S,J,q.shouldTouch,q.shouldDirty,!0),q.shouldValidate&&Ae(S)},Te=(S,N,q)=>{for(const W in N){if(!N.hasOwnProperty(W))return;const J=N[W],X=S+"."+W,ae=ne(s,X);(y.array.has(S)||et(J)||ae&&!ae._f)&&!Ua(J)?Te(X,J,q):Re(X,J,q)}},U=(S,N,q={})=>{const W=ne(s,S),J=y.array.has(S),X=dt(N);Le(f,S,X),J?(A.array.next({name:S,values:dt(f)}),(x.isDirty||x.dirtyFields||w.isDirty||w.dirtyFields)&&q.shouldDirty&&A.state.next({name:S,dirtyFields:Zu(o,f),isDirty:Xe(S,X)})):W&&!W._f&&!Et(X)?Te(S,X,q):Re(S,X,q),Ty(S,y)&&A.state.next({...i}),A.state.next({name:h.mount?S:void 0,values:dt(f)})},K=async S=>{h.mount=!0;const N=S.target;let q=N.name,W=!0;const J=ne(s,q),X=_e=>{W=Number.isNaN(_e)||Ua(_e)&&isNaN(_e.getTime())||da(_e,ne(f,q,_e))},ae=wy(l.mode),he=wy(l.reValidateMode);if(J){let _e,ke;const $a=N.type?Ay(J._f):Lx(S),rn=S.type===by.BLUR||S.type===by.FOCUS_OUT,Vr=!Wx(J._f)&&!l.resolver&&!ne(i.errors,q)&&!J._f.deps||tS(rn,ne(i.touchedFields,q),i.isSubmitted,he,ae),Ln=Ty(q,y,rn);Le(f,q,$a),rn?(J._f.onBlur&&J._f.onBlur(S),v&&v(0)):J._f.onChange&&J._f.onChange(S);const kn=F(q,$a,rn),vn=!jt(kn)||Ln;if(!rn&&A.state.next({name:q,type:S.type,values:dt(f)}),Vr)return(x.isValid||w.isValid)&&(l.mode==="onBlur"?rn&&M():rn||M()),vn&&A.state.next({name:q,...Ln?{}:kn});if(!rn&&Ln&&A.state.next({...i}),l.resolver){const{errors:ya}=await Ee([q]);if(X($a),W){const pa=Oy(i.errors,s,q),ti=Oy(ya,s,pa.name||q);_e=ti.error,q=ti.name,ke=jt(ya)}}else G([q],!0),_e=(await Ry(J,y.disabled,f,L,l.shouldUseNativeValidation))[q],G([q]),X($a),W&&(_e?ke=!1:(x.isValid||w.isValid)&&(ke=await be(s,!0)));W&&(J._f.deps&&Ae(J._f.deps),ze(q,ke,_e,kn))}},ue=(S,N)=>{if(ne(i.errors,N)&&S.focus)return S.focus(),1},Ae=async(S,N={})=>{let q,W;const J=ku(S);if(l.resolver){const X=await Me(lt(S)?S:J);q=jt(X),W=S?!J.some(ae=>ne(X,ae)):q}else S?(W=(await Promise.all(J.map(async X=>{const ae=ne(s,X);return await be(ae&&ae._f?{[X]:ae}:ae)}))).every(Boolean),!(!W&&!i.isValid)&&M()):W=q=await be(s);return A.state.next({...!mn(S)||(x.isValid||w.isValid)&&q!==i.isValid?{}:{name:S},...l.resolver||!S?{isValid:q}:{},errors:i.errors}),N.shouldFocus&&!W&&Bu(s,ue,S?J:y.mount),W},_=S=>{const N={...h.mount?f:o};return lt(S)?N:mn(S)?ne(N,S):S.map(q=>ne(N,q))},Y=(S,N)=>({invalid:!!ne((N||i).errors,S),isDirty:!!ne((N||i).dirtyFields,S),error:ne((N||i).errors,S),isValidating:!!ne(i.validatingFields,S),isTouched:!!ne((N||i).touchedFields,S)}),I=S=>{S&&ku(S).forEach(N=>at(i.errors,N)),A.state.next({errors:S?i.errors:{}})},P=(S,N,q)=>{const W=(ne(s,S,{_f:{}})._f||{}).ref,J=ne(i.errors,S)||{},{ref:X,message:ae,type:he,..._e}=J;Le(i.errors,S,{..._e,...N,ref:W}),A.state.next({name:S,errors:i.errors,isValid:!1}),q&&q.shouldFocus&&W&&W.focus&&W.focus()},ie=(S,N)=>an(S)?A.state.subscribe({next:q=>S(ce(void 0,N),q)}):ce(S,N,!0),Se=S=>A.state.subscribe({next:N=>{eS(S.name,N.name,S.exact)&&Ix(N,S.formState||x,Vt,S.reRenderRoot)&&S.callback({values:{...f},...i,...N})}}).unsubscribe,fe=S=>(h.mount=!0,w={...w,...S.formState},Se({...S,formState:w})),mt=(S,N={})=>{for(const q of S?ku(S):y.mount)y.mount.delete(q),y.array.delete(q),N.keepValue||(at(s,q),at(f,q)),!N.keepError&&at(i.errors,q),!N.keepDirty&&at(i.dirtyFields,q),!N.keepTouched&&at(i.touchedFields,q),!N.keepIsValidating&&at(i.validatingFields,q),!l.shouldUnregister&&!N.keepDefaultValue&&at(o,q);A.state.next({values:dt(f)}),A.state.next({...i,...N.keepDirty?{isDirty:Xe()}:{}}),!N.keepIsValid&&M()},He=({disabled:S,name:N})=>{(hn(S)&&h.mount||S||y.disabled.has(N))&&(S?y.disabled.add(N):y.disabled.delete(N))},Bt=(S,N={})=>{let q=ne(s,S);const W=hn(N.disabled)||hn(l.disabled);return Le(s,S,{...q||{},_f:{...q&&q._f?q._f:{ref:{name:S}},name:S,mount:!0,...N}}),y.mount.add(S),q?He({disabled:hn(N.disabled)?N.disabled:l.disabled,name:S}):ee(S,!0,N.value),{...W?{disabled:N.disabled||l.disabled}:{},...l.progressive?{required:!!N.required,min:Hu(N.min),max:Hu(N.max),minLength:Hu(N.minLength),maxLength:Hu(N.maxLength),pattern:Hu(N.pattern)}:{},name:S,onChange:K,onBlur:K,ref:J=>{if(J){Bt(S,N),q=ne(s,S);const X=lt(J.value)&&J.querySelectorAll&&J.querySelectorAll("input,select,textarea")[0]||J,ae=Xx(X),he=q._f.refs||[];if(ae?he.find(_e=>_e===X):X===q._f.ref)return;Le(s,S,{_f:{...q._f,...ae?{refs:[...he.filter(oo),X,...Array.isArray(ne(o,S))?[{}]:[]],ref:{type:X.type,name:S}}:{ref:X}}}),ee(S,!1,void 0,X)}else q=ne(s,S,{}),q._f&&(q._f.mount=!1),(l.shouldUnregister||N.shouldUnregister)&&!(Bx(y.array,S)&&h.action)&&y.unMount.add(S)}}},ma=()=>l.shouldFocusError&&Bu(s,ue,y.mount),Ba=S=>{hn(S)&&(A.state.next({disabled:S}),Bu(s,(N,q)=>{const W=ne(s,q);W&&(N.disabled=W._f.disabled||S,Array.isArray(W._f.refs)&&W._f.refs.forEach(J=>{J.disabled=W._f.disabled||S}))},0,!1))},Va=(S,N)=>async q=>{let W;q&&(q.preventDefault&&q.preventDefault(),q.persist&&q.persist());let J=dt(f);if(A.state.next({isSubmitting:!0}),l.resolver){const{errors:X,values:ae}=await Ee();i.errors=X,J=dt(ae)}else await be(s);if(y.disabled.size)for(const X of y.disabled)at(J,X);if(at(i.errors,"root"),jt(i.errors)){A.state.next({errors:{}});try{await S(J,q)}catch(X){W=X}}else N&&await N({...i.errors},q),ma(),setTimeout(ma);if(A.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:jt(i.errors)&&!W,submitCount:i.submitCount+1,errors:i.errors}),W)throw W},Ul=(S,N={})=>{ne(s,S)&&(lt(N.defaultValue)?U(S,dt(ne(o,S))):(U(S,N.defaultValue),Le(o,S,dt(N.defaultValue))),N.keepTouched||at(i.touchedFields,S),N.keepDirty||(at(i.dirtyFields,S),i.isDirty=N.defaultValue?Xe(S,dt(ne(o,S))):Xe()),N.keepError||(at(i.errors,S),x.isValid&&M()),A.state.next({...i}))},Ya=(S,N={})=>{const q=S?dt(S):o,W=dt(q),J=jt(S),X=J?o:W;if(N.keepDefaultValues||(o=q),!N.keepValues){if(N.keepDirtyValues){const ae=new Set([...y.mount,...Object.keys(Zu(o,f))]);for(const he of Array.from(ae))ne(i.dirtyFields,he)?Le(X,he,ne(f,he)):U(he,ne(X,he))}else{if(Lo&&lt(S))for(const ae of y.mount){const he=ne(s,ae);if(he&&he._f){const _e=Array.isArray(he._f.refs)?he._f.refs[0]:he._f.ref;if(Tr(_e)){const ke=_e.closest("form");if(ke){ke.reset();break}}}}if(N.keepFieldsRef)for(const ae of y.mount)U(ae,ne(X,ae));else s={}}f=l.shouldUnregister?N.keepDefaultValues?dt(o):{}:dt(X),A.array.next({values:{...X}}),A.state.next({values:{...X}})}y={mount:N.keepDirtyValues?y.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},h.mount=!x.isValid||!!N.keepIsValid||!!N.keepDirtyValues,h.watch=!!l.shouldUnregister,A.state.next({submitCount:N.keepSubmitCount?i.submitCount:0,isDirty:J?!1:N.keepDirty?i.isDirty:!!(N.keepDefaultValues&&!da(S,o)),isSubmitted:N.keepIsSubmitted?i.isSubmitted:!1,dirtyFields:J?{}:N.keepDirtyValues?N.keepDefaultValues&&f?Zu(o,f):i.dirtyFields:N.keepDefaultValues&&S?Zu(o,S):N.keepDirty?i.dirtyFields:{},touchedFields:N.keepTouched?i.touchedFields:{},errors:N.keepErrors?i.errors:{},isSubmitSuccessful:N.keepIsSubmitSuccessful?i.isSubmitSuccessful:!1,isSubmitting:!1})},ei=(S,N)=>Ya(an(S)?S(f):S,N),Br=(S,N={})=>{const q=ne(s,S),W=q&&q._f;if(W){const J=W.refs?W.refs[0]:W.ref;J.focus&&(J.focus(),N.shouldSelect&&an(J.select)&&J.select())}},Vt=S=>{i={...i,...S}},Zl={control:{register:Bt,unregister:mt,getFieldState:Y,handleSubmit:Va,setError:P,_subscribe:Se,_runSchema:Ee,_focusError:ma,_getWatch:ce,_getDirty:Xe,_setValid:M,_setFieldArray:Z,_setDisabledField:He,_setErrors:Q,_getFieldArray:xe,_reset:Ya,_resetDefaultValues:()=>an(l.defaultValues)&&l.defaultValues().then(S=>{ei(S,l.resetOptions),A.state.next({isLoading:!1})}),_removeUnmounted:Ge,_disableForm:Ba,_subjects:A,_proxyFormState:x,get _fields(){return s},get _formValues(){return f},get _state(){return h},set _state(S){h=S},get _defaultValues(){return o},get _names(){return y},set _names(S){y=S},get _formState(){return i},get _options(){return l},set _options(S){l={...l,...S}}},subscribe:fe,trigger:Ae,register:Bt,handleSubmit:Va,watch:ie,setValue:U,getValues:_,reset:ei,resetField:Ul,clearErrors:I,unregister:mt,setError:P,setFocus:Br,getFieldState:Y};return{...Zl,formControl:Zl}}function jp(n={}){const l=Dt.useRef(void 0),i=Dt.useRef(void 0),[s,o]=Dt.useState({isDirty:!1,isValidating:!1,isLoading:an(n.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:n.errors||{},disabled:n.disabled||!1,isReady:!1,defaultValues:an(n.defaultValues)?void 0:n.defaultValues});if(!l.current)if(n.formControl)l.current={...n.formControl,formState:s},n.defaultValues&&!an(n.defaultValues)&&n.formControl.reset(n.defaultValues,n.resetOptions);else{const{formControl:h,...y}=uS(n);l.current={...y,formState:s}}const f=l.current.control;return f._options=n,Qx(()=>{const h=f._subscribe({formState:f._proxyFormState,callback:()=>o({...f._formState}),reRenderRoot:!0});return o(y=>({...y,isReady:!0})),f._formState.isReady=!0,h},[f]),Dt.useEffect(()=>f._disableForm(n.disabled),[f,n.disabled]),Dt.useEffect(()=>{n.mode&&(f._options.mode=n.mode),n.reValidateMode&&(f._options.reValidateMode=n.reValidateMode)},[f,n.mode,n.reValidateMode]),Dt.useEffect(()=>{n.errors&&(f._setErrors(n.errors),f._focusError())},[f,n.errors]),Dt.useEffect(()=>{n.shouldUnregister&&f._subjects.state.next({values:f._getWatch()})},[f,n.shouldUnregister]),Dt.useEffect(()=>{if(f._proxyFormState.isDirty){const h=f._getDirty();h!==s.isDirty&&f._subjects.state.next({isDirty:h})}},[f,s.isDirty]),Dt.useEffect(()=>{n.values&&!da(n.values,i.current)?(f._reset(n.values,{keepFieldsRef:!0,...f._options.resetOptions}),i.current=n.values,o(h=>({...h}))):f._resetDefaultValues()},[f,n.values]),Dt.useEffect(()=>{f._state.mount||(f._setValid(),f._state.mount=!0),f._state.watch&&(f._state.watch=!1,f._subjects.state.next({...f._formState})),f._removeUnmounted()}),l.current.formState=$x(s,f),l.current}const Dy=(n,l,i)=>{if(n&&"reportValidity"in n){const s=ne(i,l);n.setCustomValidity(s&&s.message||""),n.reportValidity()}},Ao=(n,l)=>{for(const i in l.fields){const s=l.fields[i];s&&s.ref&&"reportValidity"in s.ref?Dy(s.ref,i,n):s&&s.refs&&s.refs.forEach(o=>Dy(o,i,n))}},jy=(n,l)=>{l.shouldUseNativeValidation&&Ao(n,l);const i={};for(const s in n){const o=ne(l.fields,s),f=Object.assign(n[s]||{},{ref:o&&o.ref});if(iS(l.names||Object.keys(n),s)){const h=Object.assign({},ne(i,s));Le(h,"root",f),Le(i,s,h)}else Le(i,s,f)}return i},iS=(n,l)=>{const i=My(l);return n.some(s=>My(s).match(`^${i}\\.\\d+`))};function My(n){return n.replace(/\]|\[/g,"")}function $(n,l,i){function s(y,v){var m;Object.defineProperty(y,"_zod",{value:y._zod??{},enumerable:!1}),(m=y._zod).traits??(m.traits=new Set),y._zod.traits.add(n),l(y,v);for(const x in h.prototype)x in y||Object.defineProperty(y,x,{value:h.prototype[x].bind(y)});y._zod.constr=h,y._zod.def=v}const o=i?.Parent??Object;class f extends o{}Object.defineProperty(f,"name",{value:n});function h(y){var v;const m=i?.Parent?new f:this;s(m,y),(v=m._zod).deferred??(v.deferred=[]);for(const x of m._zod.deferred)x();return m}return Object.defineProperty(h,"init",{value:s}),Object.defineProperty(h,Symbol.hasInstance,{value:y=>i?.Parent&&y instanceof i.Parent?!0:y?._zod?.traits?.has(n)}),Object.defineProperty(h,"name",{value:n}),h}class Gu extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const Mp={};function Ha(n){return Mp}function rS(n){const l=Object.values(n).filter(s=>typeof s=="number");return Object.entries(n).filter(([s,o])=>l.indexOf(+s)===-1).map(([s,o])=>o)}function sS(n,l){return typeof l=="bigint"?l.toString():l}function Cp(n){return{get value(){{const l=n();return Object.defineProperty(this,"value",{value:l}),l}}}}function Qo(n){return n==null}function Go(n){const l=n.startsWith("^")?1:0,i=n.endsWith("$")?n.length-1:n.length;return n.slice(l,i)}function Ve(n,l,i){Object.defineProperty(n,l,{get(){{const s=i();return n[l]=s,s}},set(s){Object.defineProperty(n,l,{value:s})},configurable:!0})}function Xo(n,l,i){Object.defineProperty(n,l,{value:i,writable:!0,enumerable:!0,configurable:!0})}function qu(n){return JSON.stringify(n)}const Up=Error.captureStackTrace?Error.captureStackTrace:(...n)=>{};function wo(n){return typeof n=="object"&&n!==null&&!Array.isArray(n)}const cS=Cp(()=>{if(typeof navigator<"u"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{const n=Function;return new n(""),!0}catch{return!1}});function zo(n){if(wo(n)===!1)return!1;const l=n.constructor;if(l===void 0)return!0;const i=l.prototype;return!(wo(i)===!1||Object.prototype.hasOwnProperty.call(i,"isPrototypeOf")===!1)}const oS=new Set(["string","number","symbol"]);function Hr(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ka(n,l,i){const s=new n._zod.constr(l??n._zod.def);return(!l||i?.parent)&&(s._zod.parent=n),s}function de(n){const l=n;if(!l)return{};if(typeof l=="string")return{error:()=>l};if(l?.message!==void 0){if(l?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");l.error=l.message}return delete l.message,typeof l.error=="string"?{...l,error:()=>l.error}:l}function fS(n){return Object.keys(n).filter(l=>n[l]._zod.optin==="optional"&&n[l]._zod.optout==="optional")}function dS(n,l){const i={},s=n._zod.def;for(const o in l){if(!(o in s.shape))throw new Error(`Unrecognized key: "${o}"`);l[o]&&(i[o]=s.shape[o])}return ka(n,{...n._zod.def,shape:i,checks:[]})}function hS(n,l){const i={...n._zod.def.shape},s=n._zod.def;for(const o in l){if(!(o in s.shape))throw new Error(`Unrecognized key: "${o}"`);l[o]&&delete i[o]}return ka(n,{...n._zod.def,shape:i,checks:[]})}function mS(n,l){if(!zo(l))throw new Error("Invalid input to extend: expected a plain object");const i={...n._zod.def,get shape(){const s={...n._zod.def.shape,...l};return Xo(this,"shape",s),s},checks:[]};return ka(n,i)}function yS(n,l){return ka(n,{...n._zod.def,get shape(){const i={...n._zod.def.shape,...l._zod.def.shape};return Xo(this,"shape",i),i},catchall:l._zod.def.catchall,checks:[]})}function pS(n,l,i){const s=l._zod.def.shape,o={...s};if(i)for(const f in i){if(!(f in s))throw new Error(`Unrecognized key: "${f}"`);i[f]&&(o[f]=n?new n({type:"optional",innerType:s[f]}):s[f])}else for(const f in s)o[f]=n?new n({type:"optional",innerType:s[f]}):s[f];return ka(l,{...l._zod.def,shape:o,checks:[]})}function vS(n,l,i){const s=l._zod.def.shape,o={...s};if(i)for(const f in i){if(!(f in o))throw new Error(`Unrecognized key: "${f}"`);i[f]&&(o[f]=new n({type:"nonoptional",innerType:s[f]}))}else for(const f in s)o[f]=new n({type:"nonoptional",innerType:s[f]});return ka(l,{...l._zod.def,shape:o,checks:[]})}function Vu(n,l=0){for(let i=l;i<n.issues.length;i++)if(n.issues[i]?.continue!==!0)return!0;return!1}function Ko(n,l){return l.map(i=>{var s;return(s=i).path??(s.path=[]),i.path.unshift(n),i})}function yr(n){return typeof n=="string"?n:n?.message}function qa(n,l,i){const s={...n,path:n.path??[]};if(!n.message){const o=yr(n.inst?._zod.def?.error?.(n))??yr(l?.error?.(n))??yr(i.customError?.(n))??yr(i.localeError?.(n))??"Invalid input";s.message=o}return delete s.inst,delete s.continue,l?.reportInput||delete s.input,s}function Fo(n){return Array.isArray(n)?"array":typeof n=="string"?"string":"unknown"}function Xu(...n){const[l,i,s]=n;return typeof l=="string"?{message:l,code:"custom",input:i,inst:s}:{...l}}const Zp=(n,l)=>{n.name="$ZodError",Object.defineProperty(n,"_zod",{value:n._zod,enumerable:!1}),Object.defineProperty(n,"issues",{value:l,enumerable:!1}),Object.defineProperty(n,"message",{get(){return JSON.stringify(l,sS,2)},enumerable:!0}),Object.defineProperty(n,"toString",{value:()=>n.message,enumerable:!1})},Jo=$("$ZodError",Zp),qr=$("$ZodError",Zp,{Parent:Error});function gS(n,l=i=>i.message){const i={},s=[];for(const o of n.issues)o.path.length>0?(i[o.path[0]]=i[o.path[0]]||[],i[o.path[0]].push(l(o))):s.push(l(o));return{formErrors:s,fieldErrors:i}}function bS(n,l){const i=l||function(f){return f.message},s={_errors:[]},o=f=>{for(const h of f.issues)if(h.code==="invalid_union"&&h.errors.length)h.errors.map(y=>o({issues:y}));else if(h.code==="invalid_key")o({issues:h.issues});else if(h.code==="invalid_element")o({issues:h.issues});else if(h.path.length===0)s._errors.push(i(h));else{let y=s,v=0;for(;v<h.path.length;){const m=h.path[v];v===h.path.length-1?(y[m]=y[m]||{_errors:[]},y[m]._errors.push(i(h))):y[m]=y[m]||{_errors:[]},y=y[m],v++}}};return o(n),s}const Hp=n=>(l,i,s,o)=>{const f=s?Object.assign(s,{async:!1}):{async:!1},h=l._zod.run({value:i,issues:[]},f);if(h instanceof Promise)throw new Gu;if(h.issues.length){const y=new(o?.Err??n)(h.issues.map(v=>qa(v,f,Ha())));throw Up(y,o?.callee),y}return h.value},xS=Hp(qr),qp=n=>async(l,i,s,o)=>{const f=s?Object.assign(s,{async:!0}):{async:!0};let h=l._zod.run({value:i,issues:[]},f);if(h instanceof Promise&&(h=await h),h.issues.length){const y=new(o?.Err??n)(h.issues.map(v=>qa(v,f,Ha())));throw Up(y,o?.callee),y}return h.value},SS=qp(qr),Lp=n=>(l,i,s)=>{const o=s?{...s,async:!1}:{async:!1},f=l._zod.run({value:i,issues:[]},o);if(f instanceof Promise)throw new Gu;return f.issues.length?{success:!1,error:new(n??Jo)(f.issues.map(h=>qa(h,o,Ha())))}:{success:!0,data:f.value}},_S=Lp(qr),kp=n=>async(l,i,s)=>{const o=s?Object.assign(s,{async:!0}):{async:!0};let f=l._zod.run({value:i,issues:[]},o);return f instanceof Promise&&(f=await f),f.issues.length?{success:!1,error:new n(f.issues.map(h=>qa(h,o,Ha())))}:{success:!0,data:f.value}},ES=kp(qr),AS=/^[cC][^\s-]{8,}$/,wS=/^[0-9a-z]+$/,zS=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,TS=/^[0-9a-vA-V]{20}$/,OS=/^[A-Za-z0-9]{27}$/,NS=/^[a-zA-Z0-9_-]{21}$/,RS=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,DS=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Cy=n=>n?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${n}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,jS=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,MS="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function CS(){return new RegExp(MS,"u")}const US=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ZS=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,HS=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,qS=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,LS=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,Bp=/^[A-Za-z0-9_-]*$/,kS=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,BS=/^\+(?:[0-9]){6,14}[0-9]$/,Vp="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",VS=new RegExp(`^${Vp}$`);function Yp(n){const l="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof n.precision=="number"?n.precision===-1?`${l}`:n.precision===0?`${l}:[0-5]\\d`:`${l}:[0-5]\\d\\.\\d{${n.precision}}`:`${l}(?::[0-5]\\d(?:\\.\\d+)?)?`}function YS(n){return new RegExp(`^${Yp(n)}$`)}function $S(n){const l=Yp({precision:n.precision}),i=["Z"];n.local&&i.push(""),n.offset&&i.push("([+-]\\d{2}:\\d{2})");const s=`${l}(?:${i.join("|")})`;return new RegExp(`^${Vp}T(?:${s})$`)}const QS=n=>{const l=n?`[\\s\\S]{${n?.minimum??0},${n?.maximum??""}}`:"[\\s\\S]*";return new RegExp(`^${l}$`)},GS=/^[^A-Z]*$/,XS=/^[^a-z]*$/,pn=$("$ZodCheck",(n,l)=>{var i;n._zod??(n._zod={}),n._zod.def=l,(i=n._zod).onattach??(i.onattach=[])}),KS=$("$ZodCheckMaxLength",(n,l)=>{var i;pn.init(n,l),(i=n._zod.def).when??(i.when=s=>{const o=s.value;return!Qo(o)&&o.length!==void 0}),n._zod.onattach.push(s=>{const o=s._zod.bag.maximum??Number.POSITIVE_INFINITY;l.maximum<o&&(s._zod.bag.maximum=l.maximum)}),n._zod.check=s=>{const o=s.value;if(o.length<=l.maximum)return;const h=Fo(o);s.issues.push({origin:h,code:"too_big",maximum:l.maximum,inclusive:!0,input:o,inst:n,continue:!l.abort})}}),FS=$("$ZodCheckMinLength",(n,l)=>{var i;pn.init(n,l),(i=n._zod.def).when??(i.when=s=>{const o=s.value;return!Qo(o)&&o.length!==void 0}),n._zod.onattach.push(s=>{const o=s._zod.bag.minimum??Number.NEGATIVE_INFINITY;l.minimum>o&&(s._zod.bag.minimum=l.minimum)}),n._zod.check=s=>{const o=s.value;if(o.length>=l.minimum)return;const h=Fo(o);s.issues.push({origin:h,code:"too_small",minimum:l.minimum,inclusive:!0,input:o,inst:n,continue:!l.abort})}}),JS=$("$ZodCheckLengthEquals",(n,l)=>{var i;pn.init(n,l),(i=n._zod.def).when??(i.when=s=>{const o=s.value;return!Qo(o)&&o.length!==void 0}),n._zod.onattach.push(s=>{const o=s._zod.bag;o.minimum=l.length,o.maximum=l.length,o.length=l.length}),n._zod.check=s=>{const o=s.value,f=o.length;if(f===l.length)return;const h=Fo(o),y=f>l.length;s.issues.push({origin:h,...y?{code:"too_big",maximum:l.length}:{code:"too_small",minimum:l.length},inclusive:!0,exact:!0,input:s.value,inst:n,continue:!l.abort})}}),Lr=$("$ZodCheckStringFormat",(n,l)=>{var i,s;pn.init(n,l),n._zod.onattach.push(o=>{const f=o._zod.bag;f.format=l.format,l.pattern&&(f.patterns??(f.patterns=new Set),f.patterns.add(l.pattern))}),l.pattern?(i=n._zod).check??(i.check=o=>{l.pattern.lastIndex=0,!l.pattern.test(o.value)&&o.issues.push({origin:"string",code:"invalid_format",format:l.format,input:o.value,...l.pattern?{pattern:l.pattern.toString()}:{},inst:n,continue:!l.abort})}):(s=n._zod).check??(s.check=()=>{})}),PS=$("$ZodCheckRegex",(n,l)=>{Lr.init(n,l),n._zod.check=i=>{l.pattern.lastIndex=0,!l.pattern.test(i.value)&&i.issues.push({origin:"string",code:"invalid_format",format:"regex",input:i.value,pattern:l.pattern.toString(),inst:n,continue:!l.abort})}}),WS=$("$ZodCheckLowerCase",(n,l)=>{l.pattern??(l.pattern=GS),Lr.init(n,l)}),IS=$("$ZodCheckUpperCase",(n,l)=>{l.pattern??(l.pattern=XS),Lr.init(n,l)}),e2=$("$ZodCheckIncludes",(n,l)=>{pn.init(n,l);const i=Hr(l.includes),s=new RegExp(typeof l.position=="number"?`^.{${l.position}}${i}`:i);l.pattern=s,n._zod.onattach.push(o=>{const f=o._zod.bag;f.patterns??(f.patterns=new Set),f.patterns.add(s)}),n._zod.check=o=>{o.value.includes(l.includes,l.position)||o.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:l.includes,input:o.value,inst:n,continue:!l.abort})}}),t2=$("$ZodCheckStartsWith",(n,l)=>{pn.init(n,l);const i=new RegExp(`^${Hr(l.prefix)}.*`);l.pattern??(l.pattern=i),n._zod.onattach.push(s=>{const o=s._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(i)}),n._zod.check=s=>{s.value.startsWith(l.prefix)||s.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:l.prefix,input:s.value,inst:n,continue:!l.abort})}}),n2=$("$ZodCheckEndsWith",(n,l)=>{pn.init(n,l);const i=new RegExp(`.*${Hr(l.suffix)}$`);l.pattern??(l.pattern=i),n._zod.onattach.push(s=>{const o=s._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(i)}),n._zod.check=s=>{s.value.endsWith(l.suffix)||s.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:l.suffix,input:s.value,inst:n,continue:!l.abort})}}),a2=$("$ZodCheckOverwrite",(n,l)=>{pn.init(n,l),n._zod.check=i=>{i.value=l.tx(i.value)}});class l2{constructor(l=[]){this.content=[],this.indent=0,this&&(this.args=l)}indented(l){this.indent+=1,l(this),this.indent-=1}write(l){if(typeof l=="function"){l(this,{execution:"sync"}),l(this,{execution:"async"});return}const s=l.split(`
`).filter(h=>h),o=Math.min(...s.map(h=>h.length-h.trimStart().length)),f=s.map(h=>h.slice(o)).map(h=>" ".repeat(this.indent*2)+h);for(const h of f)this.content.push(h)}compile(){const l=Function,i=this?.args,o=[...(this?.content??[""]).map(f=>`  ${f}`)];return new l(...i,o.join(`
`))}}const u2={major:4,minor:0,patch:5},ut=$("$ZodType",(n,l)=>{var i;n??(n={}),n._zod.def=l,n._zod.bag=n._zod.bag||{},n._zod.version=u2;const s=[...n._zod.def.checks??[]];n._zod.traits.has("$ZodCheck")&&s.unshift(n);for(const o of s)for(const f of o._zod.onattach)f(n);if(s.length===0)(i=n._zod).deferred??(i.deferred=[]),n._zod.deferred?.push(()=>{n._zod.run=n._zod.parse});else{const o=(f,h,y)=>{let v=Vu(f),m;for(const x of h){if(x._zod.def.when){if(!x._zod.def.when(f))continue}else if(v)continue;const w=f.issues.length,A=x._zod.check(f);if(A instanceof Promise&&y?.async===!1)throw new Gu;if(m||A instanceof Promise)m=(m??Promise.resolve()).then(async()=>{await A,f.issues.length!==w&&(v||(v=Vu(f,w)))});else{if(f.issues.length===w)continue;v||(v=Vu(f,w))}}return m?m.then(()=>f):f};n._zod.run=(f,h)=>{const y=n._zod.parse(f,h);if(y instanceof Promise){if(h.async===!1)throw new Gu;return y.then(v=>o(v,s,h))}return o(y,s,h)}}n["~standard"]={validate:o=>{try{const f=_S(n,o);return f.success?{value:f.data}:{issues:f.error?.issues}}catch{return ES(n,o).then(h=>h.success?{value:h.data}:{issues:h.error?.issues})}},vendor:"zod",version:1}}),Po=$("$ZodString",(n,l)=>{ut.init(n,l),n._zod.pattern=[...n?._zod.bag?.patterns??[]].pop()??QS(n._zod.bag),n._zod.parse=(i,s)=>{if(l.coerce)try{i.value=String(i.value)}catch{}return typeof i.value=="string"||i.issues.push({expected:"string",code:"invalid_type",input:i.value,inst:n}),i}}),Qe=$("$ZodStringFormat",(n,l)=>{Lr.init(n,l),Po.init(n,l)}),i2=$("$ZodGUID",(n,l)=>{l.pattern??(l.pattern=DS),Qe.init(n,l)}),r2=$("$ZodUUID",(n,l)=>{if(l.version){const s={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[l.version];if(s===void 0)throw new Error(`Invalid UUID version: "${l.version}"`);l.pattern??(l.pattern=Cy(s))}else l.pattern??(l.pattern=Cy());Qe.init(n,l)}),s2=$("$ZodEmail",(n,l)=>{l.pattern??(l.pattern=jS),Qe.init(n,l)}),c2=$("$ZodURL",(n,l)=>{Qe.init(n,l),n._zod.check=i=>{try{const s=i.value,o=new URL(s),f=o.href;l.hostname&&(l.hostname.lastIndex=0,l.hostname.test(o.hostname)||i.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:kS.source,input:i.value,inst:n,continue:!l.abort})),l.protocol&&(l.protocol.lastIndex=0,l.protocol.test(o.protocol.endsWith(":")?o.protocol.slice(0,-1):o.protocol)||i.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:l.protocol.source,input:i.value,inst:n,continue:!l.abort})),!s.endsWith("/")&&f.endsWith("/")?i.value=f.slice(0,-1):i.value=f;return}catch{i.issues.push({code:"invalid_format",format:"url",input:i.value,inst:n,continue:!l.abort})}}}),o2=$("$ZodEmoji",(n,l)=>{l.pattern??(l.pattern=CS()),Qe.init(n,l)}),f2=$("$ZodNanoID",(n,l)=>{l.pattern??(l.pattern=NS),Qe.init(n,l)}),d2=$("$ZodCUID",(n,l)=>{l.pattern??(l.pattern=AS),Qe.init(n,l)}),h2=$("$ZodCUID2",(n,l)=>{l.pattern??(l.pattern=wS),Qe.init(n,l)}),m2=$("$ZodULID",(n,l)=>{l.pattern??(l.pattern=zS),Qe.init(n,l)}),y2=$("$ZodXID",(n,l)=>{l.pattern??(l.pattern=TS),Qe.init(n,l)}),p2=$("$ZodKSUID",(n,l)=>{l.pattern??(l.pattern=OS),Qe.init(n,l)}),v2=$("$ZodISODateTime",(n,l)=>{l.pattern??(l.pattern=$S(l)),Qe.init(n,l)}),g2=$("$ZodISODate",(n,l)=>{l.pattern??(l.pattern=VS),Qe.init(n,l)}),b2=$("$ZodISOTime",(n,l)=>{l.pattern??(l.pattern=YS(l)),Qe.init(n,l)}),x2=$("$ZodISODuration",(n,l)=>{l.pattern??(l.pattern=RS),Qe.init(n,l)}),S2=$("$ZodIPv4",(n,l)=>{l.pattern??(l.pattern=US),Qe.init(n,l),n._zod.onattach.push(i=>{const s=i._zod.bag;s.format="ipv4"})}),_2=$("$ZodIPv6",(n,l)=>{l.pattern??(l.pattern=ZS),Qe.init(n,l),n._zod.onattach.push(i=>{const s=i._zod.bag;s.format="ipv6"}),n._zod.check=i=>{try{new URL(`http://[${i.value}]`)}catch{i.issues.push({code:"invalid_format",format:"ipv6",input:i.value,inst:n,continue:!l.abort})}}}),E2=$("$ZodCIDRv4",(n,l)=>{l.pattern??(l.pattern=HS),Qe.init(n,l)}),A2=$("$ZodCIDRv6",(n,l)=>{l.pattern??(l.pattern=qS),Qe.init(n,l),n._zod.check=i=>{const[s,o]=i.value.split("/");try{if(!o)throw new Error;const f=Number(o);if(`${f}`!==o)throw new Error;if(f<0||f>128)throw new Error;new URL(`http://[${s}]`)}catch{i.issues.push({code:"invalid_format",format:"cidrv6",input:i.value,inst:n,continue:!l.abort})}}});function $p(n){if(n==="")return!0;if(n.length%4!==0)return!1;try{return atob(n),!0}catch{return!1}}const w2=$("$ZodBase64",(n,l)=>{l.pattern??(l.pattern=LS),Qe.init(n,l),n._zod.onattach.push(i=>{i._zod.bag.contentEncoding="base64"}),n._zod.check=i=>{$p(i.value)||i.issues.push({code:"invalid_format",format:"base64",input:i.value,inst:n,continue:!l.abort})}});function z2(n){if(!Bp.test(n))return!1;const l=n.replace(/[-_]/g,s=>s==="-"?"+":"/"),i=l.padEnd(Math.ceil(l.length/4)*4,"=");return $p(i)}const T2=$("$ZodBase64URL",(n,l)=>{l.pattern??(l.pattern=Bp),Qe.init(n,l),n._zod.onattach.push(i=>{i._zod.bag.contentEncoding="base64url"}),n._zod.check=i=>{z2(i.value)||i.issues.push({code:"invalid_format",format:"base64url",input:i.value,inst:n,continue:!l.abort})}}),O2=$("$ZodE164",(n,l)=>{l.pattern??(l.pattern=BS),Qe.init(n,l)});function N2(n,l=null){try{const i=n.split(".");if(i.length!==3)return!1;const[s]=i;if(!s)return!1;const o=JSON.parse(atob(s));return!("typ"in o&&o?.typ!=="JWT"||!o.alg||l&&(!("alg"in o)||o.alg!==l))}catch{return!1}}const R2=$("$ZodJWT",(n,l)=>{Qe.init(n,l),n._zod.check=i=>{N2(i.value,l.alg)||i.issues.push({code:"invalid_format",format:"jwt",input:i.value,inst:n,continue:!l.abort})}}),D2=$("$ZodUnknown",(n,l)=>{ut.init(n,l),n._zod.parse=i=>i}),j2=$("$ZodNever",(n,l)=>{ut.init(n,l),n._zod.parse=(i,s)=>(i.issues.push({expected:"never",code:"invalid_type",input:i.value,inst:n}),i)});function Uy(n,l,i){n.issues.length&&l.issues.push(...Ko(i,n.issues)),l.value[i]=n.value}const M2=$("$ZodArray",(n,l)=>{ut.init(n,l),n._zod.parse=(i,s)=>{const o=i.value;if(!Array.isArray(o))return i.issues.push({expected:"array",code:"invalid_type",input:o,inst:n}),i;i.value=Array(o.length);const f=[];for(let h=0;h<o.length;h++){const y=o[h],v=l.element._zod.run({value:y,issues:[]},s);v instanceof Promise?f.push(v.then(m=>Uy(m,i,h))):Uy(v,i,h)}return f.length?Promise.all(f).then(()=>i):i}});function pr(n,l,i){n.issues.length&&l.issues.push(...Ko(i,n.issues)),l.value[i]=n.value}function Zy(n,l,i,s){n.issues.length?s[i]===void 0?i in s?l.value[i]=void 0:l.value[i]=n.value:l.issues.push(...Ko(i,n.issues)):n.value===void 0?i in s&&(l.value[i]=void 0):l.value[i]=n.value}const C2=$("$ZodObject",(n,l)=>{ut.init(n,l);const i=Cp(()=>{const w=Object.keys(l.shape);for(const L of w)if(!(l.shape[L]instanceof ut))throw new Error(`Invalid element at key "${L}": expected a Zod schema`);const A=fS(l.shape);return{shape:l.shape,keys:w,keySet:new Set(w),numKeys:w.length,optionalKeys:new Set(A)}});Ve(n._zod,"propValues",()=>{const w=l.shape,A={};for(const L in w){const C=w[L]._zod;if(C.values){A[L]??(A[L]=new Set);for(const M of C.values)A[L].add(M)}}return A});const s=w=>{const A=new l2(["shape","payload","ctx"]),L=i.value,C=B=>{const Q=qu(B);return`shape[${Q}]._zod.run({ value: input[${Q}], issues: [] }, ctx)`};A.write("const input = payload.value;");const M=Object.create(null);let G=0;for(const B of L.keys)M[B]=`key_${G++}`;A.write("const newResult = {}");for(const B of L.keys)if(L.optionalKeys.has(B)){const Q=M[B];A.write(`const ${Q} = ${C(B)};`);const ee=qu(B);A.write(`
        if (${Q}.issues.length) {
          if (input[${ee}] === undefined) {
            if (${ee} in input) {
              newResult[${ee}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${Q}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${ee}, ...iss.path] : [${ee}],
              }))
            );
          }
        } else if (${Q}.value === undefined) {
          if (${ee} in input) newResult[${ee}] = undefined;
        } else {
          newResult[${ee}] = ${Q}.value;
        }
        `)}else{const Q=M[B];A.write(`const ${Q} = ${C(B)};`),A.write(`
          if (${Q}.issues.length) payload.issues = payload.issues.concat(${Q}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${qu(B)}, ...iss.path] : [${qu(B)}]
          })));`),A.write(`newResult[${qu(B)}] = ${Q}.value`)}A.write("payload.value = newResult;"),A.write("return payload;");const Z=A.compile();return(B,Q)=>Z(w,B,Q)};let o;const f=wo,h=!Mp.jitless,v=h&&cS.value,m=l.catchall;let x;n._zod.parse=(w,A)=>{x??(x=i.value);const L=w.value;if(!f(L))return w.issues.push({expected:"object",code:"invalid_type",input:L,inst:n}),w;const C=[];if(h&&v&&A?.async===!1&&A.jitless!==!0)o||(o=s(l.shape)),w=o(w,A);else{w.value={};const Q=x.shape;for(const ee of x.keys){const F=Q[ee],ze=F._zod.run({value:L[ee],issues:[]},A),Ee=F._zod.optin==="optional"&&F._zod.optout==="optional";ze instanceof Promise?C.push(ze.then(Me=>Ee?Zy(Me,w,ee,L):pr(Me,w,ee))):Ee?Zy(ze,w,ee,L):pr(ze,w,ee)}}if(!m)return C.length?Promise.all(C).then(()=>w):w;const M=[],G=x.keySet,Z=m._zod,B=Z.def.type;for(const Q of Object.keys(L)){if(G.has(Q))continue;if(B==="never"){M.push(Q);continue}const ee=Z.run({value:L[Q],issues:[]},A);ee instanceof Promise?C.push(ee.then(F=>pr(F,w,Q))):pr(ee,w,Q)}return M.length&&w.issues.push({code:"unrecognized_keys",keys:M,input:L,inst:n}),C.length?Promise.all(C).then(()=>w):w}});function Hy(n,l,i,s){for(const o of n)if(o.issues.length===0)return l.value=o.value,l;return l.issues.push({code:"invalid_union",input:l.value,inst:i,errors:n.map(o=>o.issues.map(f=>qa(f,s,Ha())))}),l}const U2=$("$ZodUnion",(n,l)=>{ut.init(n,l),Ve(n._zod,"optin",()=>l.options.some(i=>i._zod.optin==="optional")?"optional":void 0),Ve(n._zod,"optout",()=>l.options.some(i=>i._zod.optout==="optional")?"optional":void 0),Ve(n._zod,"values",()=>{if(l.options.every(i=>i._zod.values))return new Set(l.options.flatMap(i=>Array.from(i._zod.values)))}),Ve(n._zod,"pattern",()=>{if(l.options.every(i=>i._zod.pattern)){const i=l.options.map(s=>s._zod.pattern);return new RegExp(`^(${i.map(s=>Go(s.source)).join("|")})$`)}}),n._zod.parse=(i,s)=>{let o=!1;const f=[];for(const h of l.options){const y=h._zod.run({value:i.value,issues:[]},s);if(y instanceof Promise)f.push(y),o=!0;else{if(y.issues.length===0)return y;f.push(y)}}return o?Promise.all(f).then(h=>Hy(h,i,n,s)):Hy(f,i,n,s)}}),Z2=$("$ZodIntersection",(n,l)=>{ut.init(n,l),n._zod.parse=(i,s)=>{const o=i.value,f=l.left._zod.run({value:o,issues:[]},s),h=l.right._zod.run({value:o,issues:[]},s);return f instanceof Promise||h instanceof Promise?Promise.all([f,h]).then(([v,m])=>qy(i,v,m)):qy(i,f,h)}});function To(n,l){if(n===l)return{valid:!0,data:n};if(n instanceof Date&&l instanceof Date&&+n==+l)return{valid:!0,data:n};if(zo(n)&&zo(l)){const i=Object.keys(l),s=Object.keys(n).filter(f=>i.indexOf(f)!==-1),o={...n,...l};for(const f of s){const h=To(n[f],l[f]);if(!h.valid)return{valid:!1,mergeErrorPath:[f,...h.mergeErrorPath]};o[f]=h.data}return{valid:!0,data:o}}if(Array.isArray(n)&&Array.isArray(l)){if(n.length!==l.length)return{valid:!1,mergeErrorPath:[]};const i=[];for(let s=0;s<n.length;s++){const o=n[s],f=l[s],h=To(o,f);if(!h.valid)return{valid:!1,mergeErrorPath:[s,...h.mergeErrorPath]};i.push(h.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}function qy(n,l,i){if(l.issues.length&&n.issues.push(...l.issues),i.issues.length&&n.issues.push(...i.issues),Vu(n))return n;const s=To(l.value,i.value);if(!s.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(s.mergeErrorPath)}`);return n.value=s.data,n}const H2=$("$ZodEnum",(n,l)=>{ut.init(n,l);const i=rS(l.entries);n._zod.values=new Set(i),n._zod.pattern=new RegExp(`^(${i.filter(s=>oS.has(typeof s)).map(s=>typeof s=="string"?Hr(s):s.toString()).join("|")})$`),n._zod.parse=(s,o)=>{const f=s.value;return n._zod.values.has(f)||s.issues.push({code:"invalid_value",values:i,input:f,inst:n}),s}}),q2=$("$ZodTransform",(n,l)=>{ut.init(n,l),n._zod.parse=(i,s)=>{const o=l.transform(i.value,i);if(s.async)return(o instanceof Promise?o:Promise.resolve(o)).then(h=>(i.value=h,i));if(o instanceof Promise)throw new Gu;return i.value=o,i}}),L2=$("$ZodOptional",(n,l)=>{ut.init(n,l),n._zod.optin="optional",n._zod.optout="optional",Ve(n._zod,"values",()=>l.innerType._zod.values?new Set([...l.innerType._zod.values,void 0]):void 0),Ve(n._zod,"pattern",()=>{const i=l.innerType._zod.pattern;return i?new RegExp(`^(${Go(i.source)})?$`):void 0}),n._zod.parse=(i,s)=>l.innerType._zod.optin==="optional"?l.innerType._zod.run(i,s):i.value===void 0?i:l.innerType._zod.run(i,s)}),k2=$("$ZodNullable",(n,l)=>{ut.init(n,l),Ve(n._zod,"optin",()=>l.innerType._zod.optin),Ve(n._zod,"optout",()=>l.innerType._zod.optout),Ve(n._zod,"pattern",()=>{const i=l.innerType._zod.pattern;return i?new RegExp(`^(${Go(i.source)}|null)$`):void 0}),Ve(n._zod,"values",()=>l.innerType._zod.values?new Set([...l.innerType._zod.values,null]):void 0),n._zod.parse=(i,s)=>i.value===null?i:l.innerType._zod.run(i,s)}),B2=$("$ZodDefault",(n,l)=>{ut.init(n,l),n._zod.optin="optional",Ve(n._zod,"values",()=>l.innerType._zod.values),n._zod.parse=(i,s)=>{if(i.value===void 0)return i.value=l.defaultValue,i;const o=l.innerType._zod.run(i,s);return o instanceof Promise?o.then(f=>Ly(f,l)):Ly(o,l)}});function Ly(n,l){return n.value===void 0&&(n.value=l.defaultValue),n}const V2=$("$ZodPrefault",(n,l)=>{ut.init(n,l),n._zod.optin="optional",Ve(n._zod,"values",()=>l.innerType._zod.values),n._zod.parse=(i,s)=>(i.value===void 0&&(i.value=l.defaultValue),l.innerType._zod.run(i,s))}),Y2=$("$ZodNonOptional",(n,l)=>{ut.init(n,l),Ve(n._zod,"values",()=>{const i=l.innerType._zod.values;return i?new Set([...i].filter(s=>s!==void 0)):void 0}),n._zod.parse=(i,s)=>{const o=l.innerType._zod.run(i,s);return o instanceof Promise?o.then(f=>ky(f,n)):ky(o,n)}});function ky(n,l){return!n.issues.length&&n.value===void 0&&n.issues.push({code:"invalid_type",expected:"nonoptional",input:n.value,inst:l}),n}const $2=$("$ZodCatch",(n,l)=>{ut.init(n,l),n._zod.optin="optional",Ve(n._zod,"optout",()=>l.innerType._zod.optout),Ve(n._zod,"values",()=>l.innerType._zod.values),n._zod.parse=(i,s)=>{const o=l.innerType._zod.run(i,s);return o instanceof Promise?o.then(f=>(i.value=f.value,f.issues.length&&(i.value=l.catchValue({...i,error:{issues:f.issues.map(h=>qa(h,s,Ha()))},input:i.value}),i.issues=[]),i)):(i.value=o.value,o.issues.length&&(i.value=l.catchValue({...i,error:{issues:o.issues.map(f=>qa(f,s,Ha()))},input:i.value}),i.issues=[]),i)}}),Q2=$("$ZodPipe",(n,l)=>{ut.init(n,l),Ve(n._zod,"values",()=>l.in._zod.values),Ve(n._zod,"optin",()=>l.in._zod.optin),Ve(n._zod,"optout",()=>l.out._zod.optout),Ve(n._zod,"propValues",()=>l.in._zod.propValues),n._zod.parse=(i,s)=>{const o=l.in._zod.run(i,s);return o instanceof Promise?o.then(f=>By(f,l,s)):By(o,l,s)}});function By(n,l,i){return Vu(n)?n:l.out._zod.run({value:n.value,issues:n.issues},i)}const G2=$("$ZodReadonly",(n,l)=>{ut.init(n,l),Ve(n._zod,"propValues",()=>l.innerType._zod.propValues),Ve(n._zod,"values",()=>l.innerType._zod.values),Ve(n._zod,"optin",()=>l.innerType._zod.optin),Ve(n._zod,"optout",()=>l.innerType._zod.optout),n._zod.parse=(i,s)=>{const o=l.innerType._zod.run(i,s);return o instanceof Promise?o.then(Vy):Vy(o)}});function Vy(n){return n.value=Object.freeze(n.value),n}const X2=$("$ZodCustom",(n,l)=>{pn.init(n,l),ut.init(n,l),n._zod.parse=(i,s)=>i,n._zod.check=i=>{const s=i.value,o=l.fn(s);if(o instanceof Promise)return o.then(f=>Yy(f,i,s,n));Yy(o,i,s,n)}});function Yy(n,l,i,s){if(!n){const o={code:"custom",input:i,inst:s,path:[...s._zod.def.path??[]],continue:!s._zod.def.abort};s._zod.def.params&&(o.params=s._zod.def.params),l.issues.push(Xu(o))}}class K2{constructor(){this._map=new Map,this._idmap=new Map}add(l,...i){const s=i[0];if(this._map.set(l,s),s&&typeof s=="object"&&"id"in s){if(this._idmap.has(s.id))throw new Error(`ID ${s.id} already exists in the registry`);this._idmap.set(s.id,l)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(l){const i=this._map.get(l);return i&&typeof i=="object"&&"id"in i&&this._idmap.delete(i.id),this._map.delete(l),this}get(l){const i=l._zod.parent;if(i){const s={...this.get(i)??{}};return delete s.id,{...s,...this._map.get(l)}}return this._map.get(l)}has(l){return this._map.has(l)}}function F2(){return new K2}const vr=F2();function J2(n,l){return new n({type:"string",...de(l)})}function P2(n,l){return new n({type:"string",format:"email",check:"string_format",abort:!1,...de(l)})}function $y(n,l){return new n({type:"string",format:"guid",check:"string_format",abort:!1,...de(l)})}function W2(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,...de(l)})}function I2(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...de(l)})}function e_(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...de(l)})}function t_(n,l){return new n({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...de(l)})}function n_(n,l){return new n({type:"string",format:"url",check:"string_format",abort:!1,...de(l)})}function a_(n,l){return new n({type:"string",format:"emoji",check:"string_format",abort:!1,...de(l)})}function l_(n,l){return new n({type:"string",format:"nanoid",check:"string_format",abort:!1,...de(l)})}function u_(n,l){return new n({type:"string",format:"cuid",check:"string_format",abort:!1,...de(l)})}function i_(n,l){return new n({type:"string",format:"cuid2",check:"string_format",abort:!1,...de(l)})}function r_(n,l){return new n({type:"string",format:"ulid",check:"string_format",abort:!1,...de(l)})}function s_(n,l){return new n({type:"string",format:"xid",check:"string_format",abort:!1,...de(l)})}function c_(n,l){return new n({type:"string",format:"ksuid",check:"string_format",abort:!1,...de(l)})}function o_(n,l){return new n({type:"string",format:"ipv4",check:"string_format",abort:!1,...de(l)})}function f_(n,l){return new n({type:"string",format:"ipv6",check:"string_format",abort:!1,...de(l)})}function d_(n,l){return new n({type:"string",format:"cidrv4",check:"string_format",abort:!1,...de(l)})}function h_(n,l){return new n({type:"string",format:"cidrv6",check:"string_format",abort:!1,...de(l)})}function m_(n,l){return new n({type:"string",format:"base64",check:"string_format",abort:!1,...de(l)})}function y_(n,l){return new n({type:"string",format:"base64url",check:"string_format",abort:!1,...de(l)})}function p_(n,l){return new n({type:"string",format:"e164",check:"string_format",abort:!1,...de(l)})}function v_(n,l){return new n({type:"string",format:"jwt",check:"string_format",abort:!1,...de(l)})}function g_(n,l){return new n({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...de(l)})}function b_(n,l){return new n({type:"string",format:"date",check:"string_format",...de(l)})}function x_(n,l){return new n({type:"string",format:"time",check:"string_format",precision:null,...de(l)})}function S_(n,l){return new n({type:"string",format:"duration",check:"string_format",...de(l)})}function __(n){return new n({type:"unknown"})}function E_(n,l){return new n({type:"never",...de(l)})}function Qp(n,l){return new KS({check:"max_length",...de(l),maximum:n})}function Rr(n,l){return new FS({check:"min_length",...de(l),minimum:n})}function Gp(n,l){return new JS({check:"length_equals",...de(l),length:n})}function A_(n,l){return new PS({check:"string_format",format:"regex",...de(l),pattern:n})}function w_(n){return new WS({check:"string_format",format:"lowercase",...de(n)})}function z_(n){return new IS({check:"string_format",format:"uppercase",...de(n)})}function T_(n,l){return new e2({check:"string_format",format:"includes",...de(l),includes:n})}function O_(n,l){return new t2({check:"string_format",format:"starts_with",...de(l),prefix:n})}function N_(n,l){return new n2({check:"string_format",format:"ends_with",...de(l),suffix:n})}function Iu(n){return new a2({check:"overwrite",tx:n})}function R_(n){return Iu(l=>l.normalize(n))}function D_(){return Iu(n=>n.trim())}function j_(){return Iu(n=>n.toLowerCase())}function M_(){return Iu(n=>n.toUpperCase())}function C_(n,l,i){return new n({type:"array",element:l,...de(i)})}function U_(n,l,i){return new n({type:"custom",check:"custom",fn:l,...de(i)})}function Qy(n,l){try{var i=n()}catch(s){return l(s)}return i&&i.then?i.then(void 0,l):i}function Z_(n,l){for(var i={};n.length;){var s=n[0],o=s.code,f=s.message,h=s.path.join(".");if(!i[h])if("unionErrors"in s){var y=s.unionErrors[0].errors[0];i[h]={message:y.message,type:y.code}}else i[h]={message:f,type:o};if("unionErrors"in s&&s.unionErrors.forEach(function(x){return x.errors.forEach(function(w){return n.push(w)})}),l){var v=i[h].types,m=v&&v[s.code];i[h]=Vo(h,l,i,o,m?[].concat(m,s.message):s.message)}n.shift()}return i}function H_(n,l){for(var i={};n.length;){var s=n[0],o=s.code,f=s.message,h=s.path.join(".");if(!i[h])if(s.code==="invalid_union"){var y=s.errors[0][0];i[h]={message:y.message,type:y.code}}else i[h]={message:f,type:o};if(s.code==="invalid_union"&&s.errors.forEach(function(x){return x.forEach(function(w){return n.push(w)})}),l){var v=i[h].types,m=v&&v[s.code];i[h]=Vo(h,l,i,o,m?[].concat(m,s.message):s.message)}n.shift()}return i}function Xp(n,l,i){if(i===void 0&&(i={}),function(s){return"_def"in s&&typeof s._def=="object"&&"typeName"in s._def}(n))return function(s,o,f){try{return Promise.resolve(Qy(function(){return Promise.resolve(n[i.mode==="sync"?"parse":"parseAsync"](s,l)).then(function(h){return f.shouldUseNativeValidation&&Ao({},f),{errors:{},values:i.raw?Object.assign({},s):h}})},function(h){if(function(y){return Array.isArray(y?.issues)}(h))return{values:{},errors:jy(Z_(h.errors,!f.shouldUseNativeValidation&&f.criteriaMode==="all"),f)};throw h}))}catch(h){return Promise.reject(h)}};if(function(s){return"_zod"in s&&typeof s._zod=="object"}(n))return function(s,o,f){try{return Promise.resolve(Qy(function(){return Promise.resolve((i.mode==="sync"?xS:SS)(n,s,l)).then(function(h){return f.shouldUseNativeValidation&&Ao({},f),{errors:{},values:i.raw?Object.assign({},s):h}})},function(h){if(function(y){return y instanceof Jo}(h))return{values:{},errors:jy(H_(h.issues,!f.shouldUseNativeValidation&&f.criteriaMode==="all"),f)};throw h}))}catch(h){return Promise.reject(h)}};throw new Error("Invalid input: not a Zod schema")}const q_=$("ZodISODateTime",(n,l)=>{v2.init(n,l),Fe.init(n,l)});function L_(n){return g_(q_,n)}const k_=$("ZodISODate",(n,l)=>{g2.init(n,l),Fe.init(n,l)});function B_(n){return b_(k_,n)}const V_=$("ZodISOTime",(n,l)=>{b2.init(n,l),Fe.init(n,l)});function Y_(n){return x_(V_,n)}const $_=$("ZodISODuration",(n,l)=>{x2.init(n,l),Fe.init(n,l)});function Q_(n){return S_($_,n)}const G_=(n,l)=>{Jo.init(n,l),n.name="ZodError",Object.defineProperties(n,{format:{value:i=>bS(n,i)},flatten:{value:i=>gS(n,i)},addIssue:{value:i=>n.issues.push(i)},addIssues:{value:i=>n.issues.push(...i)},isEmpty:{get(){return n.issues.length===0}}})},kr=$("ZodError",G_,{Parent:Error}),X_=Hp(kr),K_=qp(kr),F_=Lp(kr),J_=kp(kr),ht=$("ZodType",(n,l)=>(ut.init(n,l),n.def=l,Object.defineProperty(n,"_def",{value:l}),n.check=(...i)=>n.clone({...l,checks:[...l.checks??[],...i.map(s=>typeof s=="function"?{_zod:{check:s,def:{check:"custom"},onattach:[]}}:s)]}),n.clone=(i,s)=>ka(n,i,s),n.brand=()=>n,n.register=(i,s)=>(i.add(n,s),n),n.parse=(i,s)=>X_(n,i,s,{callee:n.parse}),n.safeParse=(i,s)=>F_(n,i,s),n.parseAsync=async(i,s)=>K_(n,i,s,{callee:n.parseAsync}),n.safeParseAsync=async(i,s)=>J_(n,i,s),n.spa=n.safeParseAsync,n.refine=(i,s)=>n.check(kE(i,s)),n.superRefine=i=>n.check(BE(i)),n.overwrite=i=>n.check(Iu(i)),n.optional=()=>Ky(n),n.nullable=()=>Fy(n),n.nullish=()=>Ky(Fy(n)),n.nonoptional=i=>jE(n,i),n.array=()=>bE(n),n.or=i=>_E([n,i]),n.and=i=>AE(n,i),n.transform=i=>Jy(n,zE(i)),n.default=i=>NE(n,i),n.prefault=i=>DE(n,i),n.catch=i=>CE(n,i),n.pipe=i=>Jy(n,i),n.readonly=()=>HE(n),n.describe=i=>{const s=n.clone();return vr.add(s,{description:i}),s},Object.defineProperty(n,"description",{get(){return vr.get(n)?.description},configurable:!0}),n.meta=(...i)=>{if(i.length===0)return vr.get(n);const s=n.clone();return vr.add(s,i[0]),s},n.isOptional=()=>n.safeParse(void 0).success,n.isNullable=()=>n.safeParse(null).success,n)),Kp=$("_ZodString",(n,l)=>{Po.init(n,l),ht.init(n,l);const i=n._zod.bag;n.format=i.format??null,n.minLength=i.minimum??null,n.maxLength=i.maximum??null,n.regex=(...s)=>n.check(A_(...s)),n.includes=(...s)=>n.check(T_(...s)),n.startsWith=(...s)=>n.check(O_(...s)),n.endsWith=(...s)=>n.check(N_(...s)),n.min=(...s)=>n.check(Rr(...s)),n.max=(...s)=>n.check(Qp(...s)),n.length=(...s)=>n.check(Gp(...s)),n.nonempty=(...s)=>n.check(Rr(1,...s)),n.lowercase=s=>n.check(w_(s)),n.uppercase=s=>n.check(z_(s)),n.trim=()=>n.check(D_()),n.normalize=(...s)=>n.check(R_(...s)),n.toLowerCase=()=>n.check(j_()),n.toUpperCase=()=>n.check(M_())}),P_=$("ZodString",(n,l)=>{Po.init(n,l),Kp.init(n,l),n.email=i=>n.check(P2(W_,i)),n.url=i=>n.check(n_(I_,i)),n.jwt=i=>n.check(v_(mE,i)),n.emoji=i=>n.check(a_(eE,i)),n.guid=i=>n.check($y(Gy,i)),n.uuid=i=>n.check(W2(gr,i)),n.uuidv4=i=>n.check(I2(gr,i)),n.uuidv6=i=>n.check(e_(gr,i)),n.uuidv7=i=>n.check(t_(gr,i)),n.nanoid=i=>n.check(l_(tE,i)),n.guid=i=>n.check($y(Gy,i)),n.cuid=i=>n.check(u_(nE,i)),n.cuid2=i=>n.check(i_(aE,i)),n.ulid=i=>n.check(r_(lE,i)),n.base64=i=>n.check(m_(fE,i)),n.base64url=i=>n.check(y_(dE,i)),n.xid=i=>n.check(s_(uE,i)),n.ksuid=i=>n.check(c_(iE,i)),n.ipv4=i=>n.check(o_(rE,i)),n.ipv6=i=>n.check(f_(sE,i)),n.cidrv4=i=>n.check(d_(cE,i)),n.cidrv6=i=>n.check(h_(oE,i)),n.e164=i=>n.check(p_(hE,i)),n.datetime=i=>n.check(L_(i)),n.date=i=>n.check(B_(i)),n.time=i=>n.check(Y_(i)),n.duration=i=>n.check(Q_(i))});function oa(n){return J2(P_,n)}const Fe=$("ZodStringFormat",(n,l)=>{Qe.init(n,l),Kp.init(n,l)}),W_=$("ZodEmail",(n,l)=>{s2.init(n,l),Fe.init(n,l)}),Gy=$("ZodGUID",(n,l)=>{i2.init(n,l),Fe.init(n,l)}),gr=$("ZodUUID",(n,l)=>{r2.init(n,l),Fe.init(n,l)}),I_=$("ZodURL",(n,l)=>{c2.init(n,l),Fe.init(n,l)}),eE=$("ZodEmoji",(n,l)=>{o2.init(n,l),Fe.init(n,l)}),tE=$("ZodNanoID",(n,l)=>{f2.init(n,l),Fe.init(n,l)}),nE=$("ZodCUID",(n,l)=>{d2.init(n,l),Fe.init(n,l)}),aE=$("ZodCUID2",(n,l)=>{h2.init(n,l),Fe.init(n,l)}),lE=$("ZodULID",(n,l)=>{m2.init(n,l),Fe.init(n,l)}),uE=$("ZodXID",(n,l)=>{y2.init(n,l),Fe.init(n,l)}),iE=$("ZodKSUID",(n,l)=>{p2.init(n,l),Fe.init(n,l)}),rE=$("ZodIPv4",(n,l)=>{S2.init(n,l),Fe.init(n,l)}),sE=$("ZodIPv6",(n,l)=>{_2.init(n,l),Fe.init(n,l)}),cE=$("ZodCIDRv4",(n,l)=>{E2.init(n,l),Fe.init(n,l)}),oE=$("ZodCIDRv6",(n,l)=>{A2.init(n,l),Fe.init(n,l)}),fE=$("ZodBase64",(n,l)=>{w2.init(n,l),Fe.init(n,l)}),dE=$("ZodBase64URL",(n,l)=>{T2.init(n,l),Fe.init(n,l)}),hE=$("ZodE164",(n,l)=>{O2.init(n,l),Fe.init(n,l)}),mE=$("ZodJWT",(n,l)=>{R2.init(n,l),Fe.init(n,l)}),yE=$("ZodUnknown",(n,l)=>{D2.init(n,l),ht.init(n,l)});function Xy(){return __(yE)}const pE=$("ZodNever",(n,l)=>{j2.init(n,l),ht.init(n,l)});function vE(n){return E_(pE,n)}const gE=$("ZodArray",(n,l)=>{M2.init(n,l),ht.init(n,l),n.element=l.element,n.min=(i,s)=>n.check(Rr(i,s)),n.nonempty=i=>n.check(Rr(1,i)),n.max=(i,s)=>n.check(Qp(i,s)),n.length=(i,s)=>n.check(Gp(i,s)),n.unwrap=()=>n.element});function bE(n,l){return C_(gE,n,l)}const xE=$("ZodObject",(n,l)=>{C2.init(n,l),ht.init(n,l),Ve(n,"shape",()=>l.shape),n.keyof=()=>Jp(Object.keys(n._zod.def.shape)),n.catchall=i=>n.clone({...n._zod.def,catchall:i}),n.passthrough=()=>n.clone({...n._zod.def,catchall:Xy()}),n.loose=()=>n.clone({...n._zod.def,catchall:Xy()}),n.strict=()=>n.clone({...n._zod.def,catchall:vE()}),n.strip=()=>n.clone({...n._zod.def,catchall:void 0}),n.extend=i=>mS(n,i),n.merge=i=>yS(n,i),n.pick=i=>dS(n,i),n.omit=i=>hS(n,i),n.partial=(...i)=>pS(Pp,n,i[0]),n.required=(...i)=>vS(Wp,n,i[0])});function Fp(n,l){const i={type:"object",get shape(){return Xo(this,"shape",{...n}),this.shape},...de(l)};return new xE(i)}const SE=$("ZodUnion",(n,l)=>{U2.init(n,l),ht.init(n,l),n.options=l.options});function _E(n,l){return new SE({type:"union",options:n,...de(l)})}const EE=$("ZodIntersection",(n,l)=>{Z2.init(n,l),ht.init(n,l)});function AE(n,l){return new EE({type:"intersection",left:n,right:l})}const Oo=$("ZodEnum",(n,l)=>{H2.init(n,l),ht.init(n,l),n.enum=l.entries,n.options=Object.values(l.entries);const i=new Set(Object.keys(l.entries));n.extract=(s,o)=>{const f={};for(const h of s)if(i.has(h))f[h]=l.entries[h];else throw new Error(`Key ${h} not found in enum`);return new Oo({...l,checks:[],...de(o),entries:f})},n.exclude=(s,o)=>{const f={...l.entries};for(const h of s)if(i.has(h))delete f[h];else throw new Error(`Key ${h} not found in enum`);return new Oo({...l,checks:[],...de(o),entries:f})}});function Jp(n,l){const i=Array.isArray(n)?Object.fromEntries(n.map(s=>[s,s])):n;return new Oo({type:"enum",entries:i,...de(l)})}const wE=$("ZodTransform",(n,l)=>{q2.init(n,l),ht.init(n,l),n._zod.parse=(i,s)=>{i.addIssue=f=>{if(typeof f=="string")i.issues.push(Xu(f,i.value,l));else{const h=f;h.fatal&&(h.continue=!1),h.code??(h.code="custom"),h.input??(h.input=i.value),h.inst??(h.inst=n),h.continue??(h.continue=!0),i.issues.push(Xu(h))}};const o=l.transform(i.value,i);return o instanceof Promise?o.then(f=>(i.value=f,i)):(i.value=o,i)}});function zE(n){return new wE({type:"transform",transform:n})}const Pp=$("ZodOptional",(n,l)=>{L2.init(n,l),ht.init(n,l),n.unwrap=()=>n._zod.def.innerType});function Ky(n){return new Pp({type:"optional",innerType:n})}const TE=$("ZodNullable",(n,l)=>{k2.init(n,l),ht.init(n,l),n.unwrap=()=>n._zod.def.innerType});function Fy(n){return new TE({type:"nullable",innerType:n})}const OE=$("ZodDefault",(n,l)=>{B2.init(n,l),ht.init(n,l),n.unwrap=()=>n._zod.def.innerType,n.removeDefault=n.unwrap});function NE(n,l){return new OE({type:"default",innerType:n,get defaultValue(){return typeof l=="function"?l():l}})}const RE=$("ZodPrefault",(n,l)=>{V2.init(n,l),ht.init(n,l),n.unwrap=()=>n._zod.def.innerType});function DE(n,l){return new RE({type:"prefault",innerType:n,get defaultValue(){return typeof l=="function"?l():l}})}const Wp=$("ZodNonOptional",(n,l)=>{Y2.init(n,l),ht.init(n,l),n.unwrap=()=>n._zod.def.innerType});function jE(n,l){return new Wp({type:"nonoptional",innerType:n,...de(l)})}const ME=$("ZodCatch",(n,l)=>{$2.init(n,l),ht.init(n,l),n.unwrap=()=>n._zod.def.innerType,n.removeCatch=n.unwrap});function CE(n,l){return new ME({type:"catch",innerType:n,catchValue:typeof l=="function"?l:()=>l})}const UE=$("ZodPipe",(n,l)=>{Q2.init(n,l),ht.init(n,l),n.in=l.in,n.out=l.out});function Jy(n,l){return new UE({type:"pipe",in:n,out:l})}const ZE=$("ZodReadonly",(n,l)=>{G2.init(n,l),ht.init(n,l)});function HE(n){return new ZE({type:"readonly",innerType:n})}const qE=$("ZodCustom",(n,l)=>{X2.init(n,l),ht.init(n,l)});function LE(n){const l=new pn({check:"custom"});return l._zod.check=n,l}function kE(n,l={}){return U_(qE,n,l)}function BE(n){const l=LE(i=>(i.addIssue=s=>{if(typeof s=="string")i.issues.push(Xu(s,i.value,l._zod.def));else{const o=s;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=i.value),o.inst??(o.inst=l),o.continue??(o.continue=!l._zod.def.abort),i.issues.push(Xu(o))}},n(i.value,i)));return l}const VE=Fp({email:oa().email("Please enter a valid email address"),password:oa().min(6,"Password must be at least 6 characters")}),YE=()=>{const[n,l]=R.useState(!1),[i,s]=R.useState(!1),{login:o}=Pu(),{register:f,handleSubmit:h,formState:{errors:y}}=jp({resolver:Xp(VE)}),v=async m=>{s(!0);try{await o(m.email,m.password),zr.success("Welcome back!")}catch(x){zr.error(x.message||"Login failed. Please try again.")}finally{s(!1)}};return g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:g.jsxs("div",{className:"max-w-md w-full space-y-8",children:[g.jsxs("div",{children:[g.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100",children:g.jsx(Za,{className:"h-8 w-8 text-primary-600"})}),g.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"}),g.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",g.jsx(Dl,{to:"/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"create a new account"})]})]}),g.jsxs("form",{className:"mt-8 space-y-6",onSubmit:h(v),children:[g.jsxs("div",{className:"space-y-4",children:[g.jsxs("div",{children:[g.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),g.jsx("input",{...f("email"),type:"email",autoComplete:"email",className:"input-field mt-1",placeholder:"Enter your email"}),y.email&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:y.email.message})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),g.jsxs("div",{className:"mt-1 relative",children:[g.jsx("input",{...f("password"),type:n?"text":"password",autoComplete:"current-password",className:"input-field pr-10",placeholder:"Enter your password"}),g.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>l(!n),children:n?g.jsx(xo,{className:"h-5 w-5 text-gray-400"}):g.jsx(So,{className:"h-5 w-5 text-gray-400"})})]}),y.password&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:y.password.message})]})]}),g.jsxs("div",{className:"flex items-center justify-between",children:[g.jsxs("div",{className:"flex items-center",children:[g.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),g.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),g.jsx("div",{className:"text-sm",children:g.jsx("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:"Forgot your password?"})})]}),g.jsx("div",{children:g.jsx("button",{type:"submit",disabled:i,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:i?g.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):"Sign in"})})]})]})})},$E=Fp({firstName:oa().min(2,"First name must be at least 2 characters"),lastName:oa().min(2,"Last name must be at least 2 characters"),email:oa().email("Please enter a valid email address"),phone:oa().optional(),password:oa().min(6,"Password must be at least 6 characters"),confirmPassword:oa(),role:Jp(["client","staff"]).default("client")}).refine(n=>n.password===n.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),QE=()=>{const[n,l]=R.useState(!1),[i,s]=R.useState(!1),[o,f]=R.useState(!1),{register:h}=Pu(),{register:y,handleSubmit:v,formState:{errors:m}}=jp({resolver:Xp($E),defaultValues:{role:"client"}}),x=async w=>{f(!0);try{await h({email:w.email,password:w.password,firstName:w.firstName,lastName:w.lastName,phone:w.phone,role:w.role}),zr.success("Account created successfully!")}catch(A){zr.error(A.message||"Registration failed. Please try again.")}finally{f(!1)}};return g.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:g.jsxs("div",{className:"max-w-md w-full space-y-8",children:[g.jsxs("div",{children:[g.jsx("div",{className:"mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100",children:g.jsx(Za,{className:"h-8 w-8 text-primary-600"})}),g.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Create your account"}),g.jsxs("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",g.jsx(Dl,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"sign in to your existing account"})]})]}),g.jsxs("form",{className:"mt-8 space-y-6",onSubmit:v(x),children:[g.jsxs("div",{className:"space-y-4",children:[g.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[g.jsxs("div",{children:[g.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First Name"}),g.jsx("input",{...y("firstName"),type:"text",className:"input-field mt-1",placeholder:"First name"}),m.firstName&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.firstName.message})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last Name"}),g.jsx("input",{...y("lastName"),type:"text",className:"input-field mt-1",placeholder:"Last name"}),m.lastName&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.lastName.message})]})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),g.jsx("input",{...y("email"),type:"email",autoComplete:"email",className:"input-field mt-1",placeholder:"Enter your email"}),m.email&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.email.message})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone Number (Optional)"}),g.jsx("input",{...y("phone"),type:"tel",className:"input-field mt-1",placeholder:"Enter your phone number"}),m.phone&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.phone.message})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700",children:"Account Type"}),g.jsxs("select",{...y("role"),className:"input-field mt-1",children:[g.jsx("option",{value:"client",children:"Client"}),g.jsx("option",{value:"staff",children:"Staff Member"})]}),m.role&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.role.message})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),g.jsxs("div",{className:"mt-1 relative",children:[g.jsx("input",{...y("password"),type:n?"text":"password",className:"input-field pr-10",placeholder:"Enter your password"}),g.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>l(!n),children:n?g.jsx(xo,{className:"h-5 w-5 text-gray-400"}):g.jsx(So,{className:"h-5 w-5 text-gray-400"})})]}),m.password&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.password.message})]}),g.jsxs("div",{children:[g.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm Password"}),g.jsxs("div",{className:"mt-1 relative",children:[g.jsx("input",{...y("confirmPassword"),type:i?"text":"password",className:"input-field pr-10",placeholder:"Confirm your password"}),g.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>s(!i),children:i?g.jsx(xo,{className:"h-5 w-5 text-gray-400"}):g.jsx(So,{className:"h-5 w-5 text-gray-400"})})]}),m.confirmPassword&&g.jsx("p",{className:"mt-1 text-sm text-red-600",children:m.confirmPassword.message})]})]}),g.jsx("div",{children:g.jsx("button",{type:"submit",disabled:o,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:o?g.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):"Create Account"})})]})]})})},GE=()=>{const{user:n}=Pu(),l=[{name:"Total Appointments",value:"24",icon:Za,change:"+12%",changeType:"positive"},{name:"Active Clients",value:"156",icon:_o,change:"+8%",changeType:"positive"},{name:"Revenue",value:"$12,450",icon:py,change:"+15%",changeType:"positive"},{name:"Avg. Session",value:"45 min",icon:bo,change:"-2%",changeType:"negative"}];return g.jsxs("div",{children:[g.jsxs("div",{className:"mb-8",children:[g.jsxs("h1",{className:"text-2xl font-bold text-gray-900",children:["Welcome back, ",n?.firstName,"!"]}),g.jsx("p",{className:"mt-1 text-sm text-gray-600",children:"Here's what's happening with your appointments today."})]}),g.jsx("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8",children:l.map(i=>g.jsx("div",{className:"card",children:g.jsxs("div",{className:"flex items-center",children:[g.jsx("div",{className:"flex-shrink-0",children:g.jsx(i.icon,{className:"h-8 w-8 text-primary-600"})}),g.jsx("div",{className:"ml-5 w-0 flex-1",children:g.jsxs("dl",{children:[g.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:i.name}),g.jsxs("dd",{className:"flex items-baseline",children:[g.jsx("div",{className:"text-2xl font-semibold text-gray-900",children:i.value}),g.jsx("div",{className:`ml-2 flex items-baseline text-sm font-semibold ${i.changeType==="positive"?"text-green-600":"text-red-600"}`,children:i.change})]})]})})]})},i.name))}),g.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[g.jsxs("div",{className:"card",children:[g.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Appointments"}),g.jsx("div",{className:"space-y-3",children:[1,2,3].map(i=>g.jsxs("div",{className:"flex items-center justify-between py-2",children:[g.jsxs("div",{className:"flex items-center",children:[g.jsx("div",{className:"h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center",children:g.jsx(Za,{className:"h-4 w-4 text-primary-600"})}),g.jsxs("div",{className:"ml-3",children:[g.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Consultation with John Doe"}),g.jsx("p",{className:"text-sm text-gray-500",children:"Today at 2:00 PM"})]})]}),g.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Confirmed"})]},i))})]}),g.jsxs("div",{className:"card",children:[g.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Actions"}),g.jsxs("div",{className:"space-y-3",children:[g.jsx("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:g.jsxs("div",{className:"flex items-center",children:[g.jsx(Za,{className:"h-5 w-5 text-primary-600 mr-3"}),g.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Schedule New Appointment"})]})}),g.jsx("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:g.jsxs("div",{className:"flex items-center",children:[g.jsx(_o,{className:"h-5 w-5 text-primary-600 mr-3"}),g.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Manage Clients"})]})}),g.jsx("button",{className:"w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors",children:g.jsxs("div",{className:"flex items-center",children:[g.jsx(py,{className:"h-5 w-5 text-primary-600 mr-3"}),g.jsx("span",{className:"text-sm font-medium text-gray-900",children:"View Reports"})]})})]})]})]})]})},XE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Appointments"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Appointments management coming soon..."})})]}),KE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Services"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Services management coming soon..."})})]}),FE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Users Management"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Users management coming soon..."})})]}),JE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Reports & Analytics"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Reports and analytics coming soon..."})})]}),PE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Settings"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Settings page coming soon..."})})]}),WE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Book Appointment"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Appointment booking coming soon..."})})]}),IE=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"My Appointments"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Your appointments will appear here..."})})]}),eA=()=>g.jsxs("div",{children:[g.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-6",children:"My Schedule"}),g.jsx("div",{className:"card",children:g.jsx("p",{className:"text-gray-600",children:"Staff schedule management coming soon..."})})]});function tA(){const{user:n,isLoading:l,checkAuth:i}=Pu();return R.useEffect(()=>{i()},[]),l?g.jsx("div",{className:"min-h-screen flex items-center justify-center",children:g.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})}):n?g.jsx(qx,{children:g.jsxs(ly,{children:[g.jsx(_t,{path:"/",element:g.jsx(ro,{to:"/dashboard",replace:!0})}),g.jsx(_t,{path:"/dashboard",element:g.jsx(GE,{})}),g.jsx(_t,{path:"/appointments",element:g.jsx(XE,{})}),g.jsx(_t,{path:"/services",element:g.jsx(KE,{})}),g.jsx(_t,{path:"/settings",element:g.jsx(PE,{})}),n.role==="client"&&g.jsxs(g.Fragment,{children:[g.jsx(_t,{path:"/book",element:g.jsx(WE,{})}),g.jsx(_t,{path:"/my-appointments",element:g.jsx(IE,{})})]}),n.role==="staff"&&g.jsx(g.Fragment,{children:g.jsx(_t,{path:"/schedule",element:g.jsx(eA,{})})}),n.role==="admin"&&g.jsxs(g.Fragment,{children:[g.jsx(_t,{path:"/users",element:g.jsx(FE,{})}),g.jsx(_t,{path:"/reports",element:g.jsx(JE,{})})]}),g.jsx(_t,{path:"*",element:g.jsx(ro,{to:"/dashboard",replace:!0})})]})}):g.jsxs(ly,{children:[g.jsx(_t,{path:"/login",element:g.jsx(YE,{})}),g.jsx(_t,{path:"/register",element:g.jsx(QE,{})}),g.jsx(_t,{path:"*",element:g.jsx(ro,{to:"/login",replace:!0})})]})}const nA=new hb({defaultOptions:{queries:{staleTime:1e3*60*5,retry:1}}});Sg.createRoot(document.getElementById("root")).render(g.jsx(Dt.StrictMode,{children:g.jsx(yb,{client:nA,children:g.jsxs(U1,{children:[g.jsx(tA,{}),g.jsx(ux,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"}}})]})})}));
