import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from '../lib/supabase'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      isLoading: true,

      login: async (email, password) => {
        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          })

          if (error) throw error

          if (data.user) {
            // Fetch user profile
            const { data: profile, error: profileError } = await supabase
              .from('users')
              .select('*')
              .eq('id', data.user.id)
              .single()

            if (profileError) throw profileError

            set({ user: profile })
          }
        } catch (error) {
          console.error('Login error:', error)
          throw error
        }
      },

      register: async (userData) => {
        try {
          const { data, error } = await supabase.auth.signUp({
            email: userData.email,
            password: userData.password,
            options: {
              data: {
                firstName: userData.firstName,
                lastName: userData.lastName,
                phone: userData.phone,
                role: userData.role || 'client',
              },
            },
          })

          if (error) throw error

          if (data.user) {
            // Create user profile
            const { data: profile, error: profileError } = await supabase
              .from('users')
              .insert([
                {
                  id: data.user.id,
                  email: userData.email,
                  firstName: userData.firstName,
                  lastName: userData.lastName,
                  phone: userData.phone,
                  role: userData.role || 'client',
                },
              ])
              .select()
              .single()

            if (profileError) throw profileError

            set({ user: profile })
          }
        } catch (error) {
          console.error('Registration error:', error)
          throw error
        }
      },

      logout: async () => {
        try {
          const { error } = await supabase.auth.signOut()
          if (error) throw error
          set({ user: null })
        } catch (error) {
          console.error('Logout error:', error)
          throw error
        }
      },

      updateProfile: async (updates) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        try {
          const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', user.id)
            .select()
            .single()

          if (error) throw error

          set({ user: data })
        } catch (error) {
          console.error('Profile update error:', error)
          throw error
        }
      },

      checkAuth: async () => {
        try {
          const { data: { session } } = await supabase.auth.getSession()
          
          if (session?.user) {
            const { data: profile, error } = await supabase
              .from('users')
              .select('*')
              .eq('id', session.user.id)
              .single()

            if (error) throw error
            set({ user: profile, isLoading: false })
          } else {
            set({ user: null, isLoading: false })
          }
        } catch (error) {
          console.error('Auth check error:', error)
          set({ user: null, isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ user: state.user }),
    }
  )
)
