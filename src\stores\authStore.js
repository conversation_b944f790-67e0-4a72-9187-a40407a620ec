import { create } from 'zustand'
import { persist } from 'zustand/middleware'
// import { supabase } from '../lib/supabase'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      isLoading: false, // Set to false for now

      login: async (email, password) => {
        try {
          // Mock login for now
          const mockUser = {
            id: '1',
            email: email,
            firstName: 'John',
            lastName: 'Doe',
            role: 'admin',
            phone: '+1234567890',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }

          set({ user: mockUser })
        } catch (error) {
          console.error('Login error:', error)
          throw error
        }
      },

      register: async (userData) => {
        try {
          // Mock registration for now
          const mockUser = {
            id: '1',
            email: userData.email,
            firstName: userData.firstName,
            lastName: userData.lastName,
            phone: userData.phone,
            role: userData.role || 'client',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }

          set({ user: mockUser })
        } catch (error) {
          console.error('Registration error:', error)
          throw error
        }
      },

      logout: async () => {
        try {
          set({ user: null })
        } catch (error) {
          console.error('Logout error:', error)
          throw error
        }
      },

      updateProfile: async (updates) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        try {
          const updatedUser = { ...user, ...updates }
          set({ user: updatedUser })
        } catch (error) {
          console.error('Profile update error:', error)
          throw error
        }
      },

      checkAuth: async () => {
        try {
          // Mock auth check - no user logged in initially
          set({ user: null, isLoading: false })
        } catch (error) {
          console.error('Auth check error:', error)
          set({ user: null, isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ user: state.user }),
    }
  )
)
