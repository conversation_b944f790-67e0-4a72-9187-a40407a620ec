import { type Container, type IDelta, type IParticleUpdater, type Particle } from "tsparticles-engine";
import type { StrokeParticle } from "./Types";
export declare class StrokeColorUpdater implements IParticleUpdater {
    private readonly container;
    constructor(container: Container);
    init(particle: StrokeParticle): void;
    isEnabled(particle: StrokeParticle): boolean;
    update(particle: Particle, delta: IDel<PERSON>): void;
}
