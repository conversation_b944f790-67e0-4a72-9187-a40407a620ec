import type { LinkLineDrawParams, LinkParticle, LinkTriangleDrawParams } from "./Types";
export declare function drawLinkLine(params: LinkLineDrawParams): void;
export declare function drawLinkTriangle(params: LinkTriangleDrawParams): void;
export declare function getLinkKey(ids: number[]): string;
export declare function setLinkFrequency(particles: LinkParticle[], dictionary: Map<string, number>): number;
