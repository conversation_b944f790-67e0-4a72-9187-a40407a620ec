import { AlterType } from "../Enums/Types/AlterType";
import type { Container } from "../Core/Container";
import type { IContainerPlugin } from "../Core/Interfaces/IContainerPlugin";
import type { ICoordinates } from "../Core/Interfaces/ICoordinates";
import type { IDelta } from "../Core/Interfaces/IDelta";
import type { IDimension } from "../Core/Interfaces/IDimension";
import type { IDrawParticleParams } from "../Core/Interfaces/IDrawParticleParams";
import type { IHsl } from "../Core/Interfaces/Colors";
import type { Particle } from "../Core/Particle";
export declare function drawLine(context: CanvasRenderingContext2D, begin: ICoordinates, end: ICoordinates): void;
export declare function drawTriangle(context: CanvasRenderingContext2D, p1: ICoordinates, p2: ICoordinates, p3: ICoordinates): void;
export declare function paintBase(context: CanvasRenderingContext2D, dimension: IDimension, baseColor?: string): void;
export declare function paintImage(context: CanvasRenderingContext2D, dimension: IDimension, image: HTMLImageElement | undefined, opacity: number): void;
export declare function clear(context: CanvasRenderingContext2D, dimension: IDimension): void;
export declare function drawParticle(data: IDrawParticleParams): void;
export declare function drawShape(container: Container, context: CanvasRenderingContext2D, particle: Particle, radius: number, opacity: number, delta: IDelta): void;
export declare function drawShapeAfterEffect(container: Container, context: CanvasRenderingContext2D, particle: Particle, radius: number, opacity: number, delta: IDelta): void;
export declare function drawPlugin(context: CanvasRenderingContext2D, plugin: IContainerPlugin, delta: IDelta): void;
export declare function drawParticlePlugin(context: CanvasRenderingContext2D, plugin: IContainerPlugin, particle: Particle, delta: IDelta): void;
export declare function alterHsl(color: IHsl, type: AlterType, value: number): IHsl;
