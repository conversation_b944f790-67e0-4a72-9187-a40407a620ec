export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  role: 'admin' | 'staff' | 'client'
  avatar?: string
  createdAt: string
  updatedAt: string
}

export interface Service {
  id: string
  name: string
  description: string
  duration: number // in minutes
  price: number
  category: string
  isActive: boolean
  staffIds: string[]
  createdAt: string
  updatedAt: string
}

export interface Appointment {
  id: string
  clientId: string
  staffId: string
  serviceId: string
  startTime: string
  endTime: string
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show'
  notes?: string
  paymentStatus: 'pending' | 'paid' | 'refunded'
  paymentId?: string
  reminderSent: boolean
  createdAt: string
  updatedAt: string
  
  // Populated fields
  client?: User
  staff?: User
  service?: Service
}

export interface TimeSlot {
  start: Date
  end: Date
  isAvailable: boolean
  staffId?: string
}

export interface StaffAvailability {
  id: string
  staffId: string
  dayOfWeek: number // 0-6 (Sunday-Saturday)
  startTime: string // HH:mm format
  endTime: string // HH:mm format
  isActive: boolean
}

export interface Notification {
  id: string
  userId: string
  type: 'appointment_confirmation' | 'appointment_reminder' | 'appointment_cancelled' | 'payment_received'
  title: string
  message: string
  isRead: boolean
  createdAt: string
}

export interface Payment {
  id: string
  appointmentId: string
  amount: number
  currency: string
  status: 'pending' | 'succeeded' | 'failed' | 'refunded'
  paymentMethod: string
  stripePaymentIntentId?: string
  createdAt: string
  updatedAt: string
}

export interface Report {
  totalAppointments: number
  totalRevenue: number
  completedAppointments: number
  cancelledAppointments: number
  averageRating: number
  topServices: Array<{
    serviceId: string
    serviceName: string
    count: number
    revenue: number
  }>
  staffPerformance: Array<{
    staffId: string
    staffName: string
    appointmentCount: number
    revenue: number
    rating: number
  }>
}

export interface CalendarEvent {
  id: string
  title: string
  start: Date
  end: Date
  resource?: Appointment
}
