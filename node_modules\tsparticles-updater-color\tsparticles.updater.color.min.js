/*! For license information please see tsparticles.updater.color.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],o);else{var t="object"==typeof exports?o(require("tsparticles-engine")):o(e.window);for(var r in t)("object"==typeof exports?exports:e)[r]=t[r]}}(this,(e=>(()=>{"use strict";var o={961:o=>{o.exports=e}},t={};function r(e){var n=t[e];if(void 0!==n)return n.exports;var i=t[e]={exports:{}};return o[e](i,i.exports,r),i.exports}r.d=(e,o)=>{for(var t in o)r.o(o,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},r.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{r.r(n),r.d(n,{loadColorUpdater:()=>i});var e=r(961);function o(o,t,r,n,i){if(!t||!r.enable||(t.maxLoops??0)>0&&(t.loops??0)>(t.maxLoops??0))return;if(t.time||(t.time=0),(t.delayTime??0)>0&&t.time<(t.delayTime??0)&&(t.time+=o.value),(t.delayTime??0)>0&&t.time<(t.delayTime??0))return;const a=(0,e.randomInRange)(r.offset),l=(t.velocity??0)*o.factor+3.6*a,s=t.decay??1;i&&"increasing"!==t.status?(t.value-=l,t.value<0&&(t.loops||(t.loops=0),t.loops++,t.status="increasing",t.value+=t.value)):(t.value+=l,t.value>n&&(t.loops||(t.loops=0),t.loops++,i&&(t.status="decreasing",t.value-=t.value%n))),t.velocity&&1!==s&&(t.velocity*=s),t.value>n&&(t.value%=n)}class t{constructor(e){this.container=e}init(o){const t=(0,e.rangeColorToHsl)(o.options.color,o.id,o.options.reduceDuplicates);t&&(o.color=(0,e.getHslAnimationFromHsl)(t,o.options.color.animation,this.container.retina.reduceFactor))}isEnabled(e){const{h:o,s:t,l:r}=e.options.color.animation,{color:n}=e;return!e.destroyed&&!e.spawning&&(void 0!==n?.h.value&&o.enable||void 0!==n?.s.value&&t.enable||void 0!==n?.l.value&&r.enable)}update(e,t){!function(e,t){const{h:r,s:n,l:i}=e.options.color.animation,{color:a}=e;if(!a)return;const{h:l,s,l:c}=a;l&&o(t,l,r,360,!1),s&&o(t,s,n,100,!0),c&&o(t,c,i,100,!0)}(e,t)}}async function i(e,o=!0){await e.addParticleUpdater("color",(e=>new t(e)),o)}})(),n})()));