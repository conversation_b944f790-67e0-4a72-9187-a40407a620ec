import type { ICenterCoordinates, ICoordinates, ICoordinates3d } from "./Interfaces/ICoordinates";
import type { IHsl, IRgb } from "./Interfaces/Colors";
import type { Container } from "./Container";
import type { Engine } from "./Engine";
import type { IBubbleParticleData } from "./Interfaces/IBubbleParticleData";
import type { IDelta } from "./Interfaces/IDelta";
import type { IMovePathGenerator } from "./Interfaces/IMovePathGenerator";
import type { IParticle } from "./Interfaces/IParticle";
import type { IParticleHslAnimation } from "./Interfaces/IParticleHslAnimation";
import type { IParticleNumericValueAnimation } from "./Interfaces/IParticleValueAnimation";
import type { IParticleRetinaProps } from "./Interfaces/IParticleRetinaProps";
import type { IParticleRoll } from "./Interfaces/IParticleRoll";
import type { IParticlesOptions } from "../Options/Interfaces/Particles/IParticlesOptions";
import type { IShapeValues } from "./Interfaces/IShapeValues";
import type { ISlowParticleData } from "./Interfaces/ISlowParticleData";
import { Interactivity } from "../Options/Classes/Interactivity/Interactivity";
import { ParticleOutType } from "../Enums/Types/ParticleOutType";
import type { ParticlesOptions } from "../Options/Classes/Particles/ParticlesOptions";
import type { RecursivePartial } from "../Types/RecursivePartial";
import { Vector } from "./Utils/Vector";
import { Vector3d } from "./Utils/Vector3d";
export declare class Particle implements IParticle {
    readonly container: Container;
    backColor?: IHsl;
    bubble: IBubbleParticleData;
    close: boolean;
    color?: IParticleHslAnimation;
    destroyed: boolean;
    direction: number;
    fill: boolean;
    group?: string;
    id: number;
    ignoresResizeRatio: boolean;
    initialPosition: Vector;
    initialVelocity: Vector;
    interactivity: Interactivity;
    lastPathTime: number;
    misplaced: boolean;
    moveCenter: ICenterCoordinates;
    moveDecay: number;
    offset: Vector;
    opacity?: IParticleNumericValueAnimation;
    options: ParticlesOptions;
    outType: ParticleOutType;
    pathDelay: number;
    pathGenerator?: IMovePathGenerator;
    pathRotation: boolean;
    position: Vector3d;
    randomIndexData?: number;
    retina: IParticleRetinaProps;
    roll?: IParticleRoll;
    rotation: number;
    shadowColor?: IRgb;
    shape: string;
    shapeData?: IShapeValues;
    sides: number;
    size: IParticleNumericValueAnimation;
    slow: ISlowParticleData;
    spawning: boolean;
    strokeColor?: IParticleHslAnimation;
    strokeOpacity?: number;
    strokeWidth?: number;
    unbreakable: boolean;
    velocity: Vector;
    zIndexFactor: number;
    private readonly _engine;
    constructor(engine: Engine, id: number, container: Container, position?: ICoordinates, overrideOptions?: RecursivePartial<IParticlesOptions>, group?: string);
    destroy(override?: boolean): void;
    draw(delta: IDelta): void;
    getFillColor(): IHsl | undefined;
    getMass(): number;
    getPosition(): ICoordinates3d;
    getRadius(): number;
    getStrokeColor(): IHsl | undefined;
    init(id: number, position?: ICoordinates, overrideOptions?: RecursivePartial<IParticlesOptions>, group?: string): void;
    isInsideCanvas(): boolean;
    isVisible(): boolean;
    reset(): void;
    private readonly _calcPosition;
    private readonly _calculateVelocity;
    private readonly _checkOverlap;
    private readonly _getRollColor;
    private readonly _initPosition;
    private readonly _loadShapeData;
}
