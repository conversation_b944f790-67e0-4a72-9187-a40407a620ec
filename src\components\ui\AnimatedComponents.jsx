import { motion } from 'framer-motion'

// Page transition animations
export const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.98,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Stagger animation for lists
export const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}

export const itemVariants = {
  hidden: { 
    opacity: 0, 
    y: 20,
    scale: 0.95
  },
  visible: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Floating animation
export const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

// Pulse animation
export const pulseVariants = {
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

// Slide in from different directions
export const slideInVariants = {
  left: {
    initial: { x: -100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: -100, opacity: 0 }
  },
  right: {
    initial: { x: 100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: 100, opacity: 0 }
  },
  up: {
    initial: { y: 100, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: 100, opacity: 0 }
  },
  down: {
    initial: { y: -100, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: -100, opacity: 0 }
  }
}

// Animated Page Wrapper
export const AnimatedPage = ({ children, className = "" }) => (
  <motion.div
    variants={pageVariants}
    initial="initial"
    animate="animate"
    exit="exit"
    className={className}
  >
    {children}
  </motion.div>
)

// Animated List Container
export const AnimatedList = ({ children, className = "" }) => (
  <motion.div
    variants={containerVariants}
    initial="hidden"
    animate="visible"
    className={className}
  >
    {children}
  </motion.div>
)

// Animated List Item
export const AnimatedListItem = ({ children, className = "", delay = 0 }) => (
  <motion.div
    variants={itemVariants}
    className={className}
    style={{ animationDelay: `${delay}s` }}
  >
    {children}
  </motion.div>
)

// Floating Element
export const FloatingElement = ({ children, className = "" }) => (
  <motion.div
    variants={floatingVariants}
    animate="animate"
    className={className}
  >
    {children}
  </motion.div>
)

// Pulsing Element
export const PulsingElement = ({ children, className = "" }) => (
  <motion.div
    variants={pulseVariants}
    animate="animate"
    className={className}
  >
    {children}
  </motion.div>
)

// Slide In Element
export const SlideInElement = ({ children, direction = "up", className = "", delay = 0 }) => (
  <motion.div
    variants={slideInVariants[direction]}
    initial="initial"
    animate="animate"
    exit="exit"
    transition={{ delay, duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
    className={className}
  >
    {children}
  </motion.div>
)

// Scale on Hover
export const ScaleOnHover = ({ children, scale = 1.05, className = "" }) => (
  <motion.div
    whileHover={{ scale }}
    whileTap={{ scale: scale - 0.02 }}
    transition={{ duration: 0.2 }}
    className={className}
  >
    {children}
  </motion.div>
)

// Rotate on Hover
export const RotateOnHover = ({ children, rotation = 5, className = "" }) => (
  <motion.div
    whileHover={{ rotate: rotation }}
    transition={{ duration: 0.3 }}
    className={className}
  >
    {children}
  </motion.div>
)

// Bounce on Click
export const BounceOnClick = ({ children, className = "" }) => (
  <motion.div
    whileTap={{ scale: 0.95, y: 2 }}
    transition={{ duration: 0.1 }}
    className={className}
  >
    {children}
  </motion.div>
)

// Fade In Up
export const FadeInUp = ({ children, delay = 0, className = "" }) => (
  <motion.div
    initial={{ opacity: 0, y: 30 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ delay, duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }}
    className={className}
  >
    {children}
  </motion.div>
)

// Stagger Children
export const StaggerChildren = ({ children, className = "" }) => (
  <motion.div
    initial="hidden"
    animate="visible"
    variants={{
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: 0.1
        }
      }
    }}
    className={className}
  >
    {children}
  </motion.div>
)
