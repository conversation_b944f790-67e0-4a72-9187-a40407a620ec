"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.resolveCollision = void 0;
const Absorb_1 = require("./Absorb");
const Bounce_1 = require("./Bounce");
const Destroy_1 = require("./Destroy");
function resolveCollision(p1, p2, delta, pixelRatio) {
    switch (p1.options.collisions.mode) {
        case "absorb": {
            (0, Absorb_1.absorb)(p1, p2, delta, pixelRatio);
            break;
        }
        case "bounce": {
            (0, Bounce_1.bounce)(p1, p2);
            break;
        }
        case "destroy": {
            (0, Destroy_1.destroy)(p1, p2);
            break;
        }
    }
}
exports.resolveCollision = resolveCollision;
