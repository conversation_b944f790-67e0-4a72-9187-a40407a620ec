"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OutOfCanvasUpdater = void 0;
const BounceOutMode_1 = require("./BounceOutMode");
const DestroyOutMode_1 = require("./DestroyOutMode");
const NoneOutMode_1 = require("./NoneOutMode");
const OutOutMode_1 = require("./OutOutMode");
class OutOfCanvasUpdater {
    constructor(container) {
        this.container = container;
        this._updateOutMode = (particle, delta, outMode, direction) => {
            for (const updater of this.updaters) {
                updater.update(particle, direction, delta, outMode);
            }
        };
        this.updaters = [
            new BounceOutMode_1.BounceOutMode(container),
            new DestroyOutMode_1.DestroyOutMode(container),
            new OutOutMode_1.OutOutMode(container),
            new NoneOutMode_1.NoneOutMode(container),
        ];
    }
    init() {
    }
    isEnabled(particle) {
        return !particle.destroyed && !particle.spawning;
    }
    update(particle, delta) {
        const outModes = particle.options.move.outModes;
        this._updateOutMode(particle, delta, outModes.bottom ?? outModes.default, "bottom");
        this._updateOutMode(particle, delta, outModes.left ?? outModes.default, "left");
        this._updateOutMode(particle, delta, outModes.right ?? outModes.default, "right");
        this._updateOutMode(particle, delta, outModes.top ?? outModes.default, "top");
    }
}
exports.OutOfCanvasUpdater = OutOfCanvasUpdater;
