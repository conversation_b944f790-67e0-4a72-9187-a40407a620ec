import type { Container } from "../Container";
import type { Engine } from "../Engine";
import type { IContainerPlugin } from "../Interfaces/IContainerPlugin";
import type { IInteractor } from "../Interfaces/IInteractor";
import type { IMovePathGenerator } from "../Interfaces/IMovePathGenerator";
import type { IParticleMover } from "../Interfaces/IParticleMover";
import type { IParticleUpdater } from "../Interfaces/IParticleUpdater";
import type { IParticlesOptions } from "../../Options/Interfaces/Particles/IParticlesOptions";
import type { IPlugin } from "../Interfaces/IPlugin";
import type { IShapeDrawer } from "../Interfaces/IShapeDrawer";
import type { ISourceOptions } from "../../Types/ISourceOptions";
import type { Options } from "../../Options/Classes/Options";
import type { ParticlesOptions } from "../../Options/Classes/Particles/ParticlesOptions";
import type { RecursivePartial } from "../../Types/RecursivePartial";
import type { SingleOrMultiple } from "../../Types/SingleOrMultiple";
type GenericInitializer<T> = (container: Container) => T;
type InteractorInitializer = GenericInitializer<IInteractor>;
type MoverInitializer = GenericInitializer<IParticleMover>;
type UpdaterInitializer = GenericInitializer<IParticleUpdater>;
export declare class Plugins {
    readonly drawers: Map<string, IShapeDrawer>;
    readonly interactors: Map<Container, IInteractor[]>;
    readonly movers: Map<Container, IParticleMover[]>;
    readonly pathGenerators: Map<string, IMovePathGenerator>;
    readonly plugins: IPlugin[];
    readonly presets: Map<string, RecursivePartial<import("../..").IOptions>>;
    readonly updaters: Map<Container, IParticleUpdater[]>;
    private readonly _engine;
    private readonly _initializers;
    constructor(engine: Engine);
    addInteractor(name: string, initInteractor: InteractorInitializer): void;
    addParticleMover(name: string, initMover: MoverInitializer): void;
    addParticleUpdater(name: string, initUpdater: UpdaterInitializer): void;
    addPathGenerator(type: string, pathGenerator: IMovePathGenerator): void;
    addPlugin(plugin: IPlugin): void;
    addPreset(presetKey: string, options: ISourceOptions, override?: boolean): void;
    addShapeDrawer(types: SingleOrMultiple<string>, drawer: IShapeDrawer): void;
    destroy(container: Container): void;
    getAvailablePlugins(container: Container): Map<string, IContainerPlugin>;
    getInteractors(container: Container, force?: boolean): IInteractor[];
    getMovers(container: Container, force?: boolean): IParticleMover[];
    getPathGenerator(type: string): IMovePathGenerator | undefined;
    getPlugin(plugin: string): IPlugin | undefined;
    getPreset(preset: string): ISourceOptions | undefined;
    getShapeDrawer(type: string): IShapeDrawer | undefined;
    getSupportedShapes(): IterableIterator<string>;
    getUpdaters(container: Container, force?: boolean): IParticleUpdater[];
    loadOptions(options: Options, sourceOptions: ISourceOptions): void;
    loadParticlesOptions(container: Container, options: ParticlesOptions, ...sourceOptions: (RecursivePartial<IParticlesOptions> | undefined)[]): void;
}
export {};
