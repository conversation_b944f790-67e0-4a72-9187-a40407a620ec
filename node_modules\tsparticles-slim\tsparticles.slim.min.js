/*! For license information please see tsparticles.slim.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-particles.js"),require("tsparticles-basic"),require("tsparticles-plugin-easing-quad"),require("tsparticles-interaction-external-attract"),require("tsparticles-interaction-external-bounce"),require("tsparticles-interaction-external-bubble"),require("tsparticles-interaction-external-connect"),require("tsparticles-interaction-external-grab"),require("tsparticles-interaction-external-pause"),require("tsparticles-interaction-external-push"),require("tsparticles-interaction-external-remove"),require("tsparticles-interaction-external-repulse"),require("tsparticles-interaction-external-slow"),require("tsparticles-shape-image"),require("tsparticles-updater-life"),require("tsparticles-shape-line"),require("tsparticles-move-parallax"),require("tsparticles-interaction-particles-attract"),require("tsparticles-interaction-particles-collisions"),require("tsparticles-interaction-particles-links"),require("tsparticles-shape-polygon"),require("tsparticles-updater-rotate"),require("tsparticles-shape-square"),require("tsparticles-shape-star"),require("tsparticles-updater-stroke-color"),require("tsparticles-shape-text"));else if("function"==typeof define&&define.amd)define(["tsparticles-particles.js","tsparticles-basic","tsparticles-plugin-easing-quad","tsparticles-interaction-external-attract","tsparticles-interaction-external-bounce","tsparticles-interaction-external-bubble","tsparticles-interaction-external-connect","tsparticles-interaction-external-grab","tsparticles-interaction-external-pause","tsparticles-interaction-external-push","tsparticles-interaction-external-remove","tsparticles-interaction-external-repulse","tsparticles-interaction-external-slow","tsparticles-shape-image","tsparticles-updater-life","tsparticles-shape-line","tsparticles-move-parallax","tsparticles-interaction-particles-attract","tsparticles-interaction-particles-collisions","tsparticles-interaction-particles-links","tsparticles-shape-polygon","tsparticles-updater-rotate","tsparticles-shape-square","tsparticles-shape-star","tsparticles-updater-stroke-color","tsparticles-shape-text"],t);else{var r="object"==typeof exports?t(require("tsparticles-particles.js"),require("tsparticles-basic"),require("tsparticles-plugin-easing-quad"),require("tsparticles-interaction-external-attract"),require("tsparticles-interaction-external-bounce"),require("tsparticles-interaction-external-bubble"),require("tsparticles-interaction-external-connect"),require("tsparticles-interaction-external-grab"),require("tsparticles-interaction-external-pause"),require("tsparticles-interaction-external-push"),require("tsparticles-interaction-external-remove"),require("tsparticles-interaction-external-repulse"),require("tsparticles-interaction-external-slow"),require("tsparticles-shape-image"),require("tsparticles-updater-life"),require("tsparticles-shape-line"),require("tsparticles-move-parallax"),require("tsparticles-interaction-particles-attract"),require("tsparticles-interaction-particles-collisions"),require("tsparticles-interaction-particles-links"),require("tsparticles-shape-polygon"),require("tsparticles-updater-rotate"),require("tsparticles-shape-square"),require("tsparticles-shape-star"),require("tsparticles-updater-stroke-color"),require("tsparticles-shape-text")):t(e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window,e.window);for(var a in r)("object"==typeof exports?exports:e)[a]=r[a]}}(this,((e,t,r,a,i,s,n,o,l,p,c,u,d,x,w,q,b,h,f,g,v,m,y,S,I,P)=>(()=>{"use strict";var j={477:e=>{e.exports=t},947:e=>{e.exports=a},428:e=>{e.exports=i},557:e=>{e.exports=s},240:e=>{e.exports=n},354:e=>{e.exports=o},199:e=>{e.exports=l},997:e=>{e.exports=p},341:e=>{e.exports=c},142:e=>{e.exports=u},563:e=>{e.exports=d},718:e=>{e.exports=h},203:e=>{e.exports=f},39:e=>{e.exports=g},845:e=>{e.exports=b},736:t=>{t.exports=e},534:e=>{e.exports=r},520:e=>{e.exports=x},598:e=>{e.exports=q},841:e=>{e.exports=v},860:e=>{e.exports=y},208:e=>{e.exports=S},706:e=>{e.exports=P},86:e=>{e.exports=w},389:e=>{e.exports=m},226:e=>{e.exports=I}},E={};function k(e){var t=E[e];if(void 0!==t)return t.exports;var r=E[e]={exports:{}};return j[e](r,r.exports,k),r.exports}k.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return k.d(t,{a:t}),t},k.d=(e,t)=>{for(var r in t)k.o(t,r)&&!k.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},k.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),k.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var O={};return(()=>{k.r(O),k.d(O,{loadSlim:()=>j});var e=k(736),t=k(477),r=k(534),a=k(947),i=k(428),s=k(557),n=k(240),o=k(354),l=k(199),p=k(997),c=k(341),u=k(142),d=k(563),x=k(520),w=k(86),q=k(598),b=k(845),h=k(718),f=k(203),g=k(39),v=k(841),m=k(389),y=k(860),S=k(208),I=k(226),P=k(706);async function j(j,E=!0){(0,e.initPjs)(j),await(0,b.loadParallaxMover)(j,!1),await(0,a.loadExternalAttractInteraction)(j,!1),await(0,i.loadExternalBounceInteraction)(j,!1),await(0,s.loadExternalBubbleInteraction)(j,!1),await(0,n.loadExternalConnectInteraction)(j,!1),await(0,o.loadExternalGrabInteraction)(j,!1),await(0,l.loadExternalPauseInteraction)(j,!1),await(0,p.loadExternalPushInteraction)(j,!1),await(0,c.loadExternalRemoveInteraction)(j,!1),await(0,u.loadExternalRepulseInteraction)(j,!1),await(0,d.loadExternalSlowInteraction)(j,!1),await(0,h.loadParticlesAttractInteraction)(j,!1),await(0,f.loadParticlesCollisionsInteraction)(j,!1),await(0,g.loadParticlesLinksInteraction)(j,!1),await(0,r.loadEasingQuadPlugin)(),await(0,x.loadImageShape)(j,!1),await(0,q.loadLineShape)(j,!1),await(0,v.loadPolygonShape)(j,!1),await(0,y.loadSquareShape)(j,!1),await(0,S.loadStarShape)(j,!1),await(0,P.loadTextShape)(j,!1),await(0,w.loadLifeUpdater)(j,!1),await(0,m.loadRotateUpdater)(j,!1),await(0,I.loadStrokeColorUpdater)(j,!1),await(0,t.loadBasic)(j,E)}})(),O})()));