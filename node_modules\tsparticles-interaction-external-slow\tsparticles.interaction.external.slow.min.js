/*! For license information please see tsparticles.interaction.external.slow.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],e);else{var o="object"==typeof exports?e(require("tsparticles-engine")):e(t.window);for(var r in o)("object"==typeof exports?exports:t)[r]=o[r]}}(this,(t=>(()=>{"use strict";var e={961:e=>{e.exports=t}},o={};function r(t){var i=o[t];if(void 0!==i)return i.exports;var n=o[t]={exports:{}};return e[t](n,n.exports,r),n.exports}r.d=(t,e)=>{for(var o in e)r.o(e,o)&&!r.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var i={};return(()=>{r.r(i),r.d(i,{Slow:()=>e,loadExternalSlowInteraction:()=>n});var t=r(961);class e{constructor(){this.factor=3,this.radius=200}load(t){t&&(void 0!==t.factor&&(this.factor=t.factor),void 0!==t.radius&&(this.radius=t.radius))}}class o extends t.ExternalInteractorBase{constructor(t){super(t)}clear(t,e,o){t.slow.inRange&&!o||(t.slow.factor=1)}init(){const t=this.container,e=t.actualOptions.interactivity.modes.slow;e&&(t.retina.slowModeRadius=e.radius*t.retina.pixelRatio)}async interact(){}isEnabled(e){const o=this.container,r=o.interactivity.mouse,i=(e?.interactivity??o.actualOptions.interactivity).events;return i.onHover.enable&&!!r.position&&(0,t.isInArray)("slow",i.onHover.mode)}loadModeOptions(t,...o){t.slow||(t.slow=new e);for(const e of o)t.slow.load(e?.slow)}reset(e){e.slow.inRange=!1;const o=this.container,r=o.actualOptions,i=o.interactivity.mouse.position,n=o.retina.slowModeRadius,s=r.interactivity.modes.slow;if(!s||!n||n<0||!i)return;const a=e.getPosition(),c=(0,t.getDistance)(i,a),l=c/n,d=s.factor,{slow:u}=e;c>n||(u.inRange=!0,u.factor=l/d)}}async function n(t,e=!0){await t.addInteractor("externalSlow",(t=>new o(t)),e)}})(),i})()));