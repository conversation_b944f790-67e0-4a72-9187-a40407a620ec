/*! For license information please see tsparticles.shape.text.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var o="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var r in o)("object"==typeof exports?exports:e)[r]=o[r]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},o={};function r(e){var n=o[e];if(void 0!==n)return n.exports;var a=o[e]={exports:{}};return t[e](a,a.exports,r),a.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{r.r(n),r.d(n,{loadTextShape:()=>a});var e=r(961);const t=["text","character","char"];class o{draw(t,o,r,n){const a=o.shapeData;if(void 0===a)return;const i=a.value;if(void 0===i)return;void 0===o.text&&(o.text=(0,e.itemFromSingleOrMultiple)(i,o.randomIndexData));const s=o.text,l=a.style??"",p=a.weight??"400",c=2*Math.round(r),d=a.font??"Verdana",f=o.fill,u=s.length*r/2;t.font=`${l} ${p} ${c}px "${d}"`;const x={x:-u,y:r/2};t.globalAlpha=n,f?t.fillText(s,x.x,x.y):t.strokeText(s,x.x,x.y),t.globalAlpha=1}getSidesCount(){return 12}async init(o){const r=o.actualOptions;if(t.find((t=>(0,e.isInArray)(t,r.particles.shape.type)))){const o=t.map((e=>r.particles.shape.options[e])).find((e=>!!e)),n=[];(0,e.executeOnSingleOrMultiple)(o,(t=>{n.push((0,e.loadFont)(t.font,t.weight))})),await Promise.all(n)}}particleInit(o,r){if(!r.shape||!t.includes(r.shape))return;const n=r.shapeData;if(void 0===n)return;const a=n.value;void 0!==a&&(r.text=(0,e.itemFromSingleOrMultiple)(a,r.randomIndexData))}}async function a(e,r=!0){await e.addShape(t,new o,r)}})(),n})()));