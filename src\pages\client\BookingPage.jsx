import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Calendar,
  Clock,
  User,
  CreditCard,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Star,
  MapPin,
  Phone,
  Mail
} from 'lucide-react'
import Card from '../../components/ui/Card'
import Button from '../../components/ui/Button'
import Modal from '../../components/ui/Modal'

const BookingPage = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [selectedService, setSelectedService] = useState(null)
  const [selectedStaff, setSelectedStaff] = useState(null)
  const [selectedDate, setSelectedDate] = useState(null)
  const [selectedTime, setSelectedTime] = useState(null)
  const [showConfirmation, setShowConfirmation] = useState(false)

  const services = [
    {
      id: 1,
      name: 'Initial Consultation',
      duration: 60,
      price: 200,
      description: 'Comprehensive assessment and treatment planning',
      category: 'Consultation',
      image: '🩺'
    },
    {
      id: 2,
      name: 'Follow-up Session',
      duration: 45,
      price: 150,
      description: 'Progress review and treatment adjustment',
      category: 'Follow-up',
      image: '📋'
    },
    {
      id: 3,
      name: 'Therapy Session',
      duration: 50,
      price: 180,
      description: 'Individual therapy session',
      category: 'Therapy',
      image: '🧠'
    },
    {
      id: 4,
      name: 'Group Session',
      duration: 90,
      price: 120,
      description: 'Group therapy session (max 8 participants)',
      category: 'Group',
      image: '👥'
    },
  ]

  const staff = [
    {
      id: 1,
      name: 'Dr. Sarah Johnson',
      title: 'Clinical Psychologist',
      rating: 4.9,
      reviews: 127,
      specialties: ['Anxiety', 'Depression', 'PTSD'],
      image: '👩‍⚕️',
      bio: 'Dr. Johnson has over 10 years of experience in clinical psychology.',
      availability: ['Monday', 'Tuesday', 'Wednesday', 'Friday']
    },
    {
      id: 2,
      name: 'Dr. Michael Chen',
      title: 'Psychiatrist',
      rating: 4.8,
      reviews: 89,
      specialties: ['Bipolar Disorder', 'Schizophrenia', 'Medication Management'],
      image: '👨‍⚕️',
      bio: 'Dr. Chen specializes in psychiatric medication management.',
      availability: ['Tuesday', 'Wednesday', 'Thursday', 'Friday']
    },
    {
      id: 3,
      name: 'Dr. Emily Davis',
      title: 'Licensed Therapist',
      rating: 4.9,
      reviews: 156,
      specialties: ['Family Therapy', 'Couples Counseling', 'Child Psychology'],
      image: '👩‍💼',
      bio: 'Dr. Davis focuses on family and relationship therapy.',
      availability: ['Monday', 'Wednesday', 'Thursday', 'Saturday']
    },
  ]

  const timeSlots = [
    '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM'
  ]

  const steps = [
    { id: 1, name: 'Service', icon: CreditCard },
    { id: 2, name: 'Provider', icon: User },
    { id: 3, name: 'Date & Time', icon: Calendar },
    { id: 4, name: 'Confirmation', icon: CheckCircle },
  ]

  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleBooking = () => {
    setShowConfirmation(true)
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Book Your Appointment
        </h1>
        <p className="mt-2 text-gray-600 text-lg">
          Choose your service, provider, and preferred time
        </p>
      </div>

      {/* Progress Steps */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all ${
                currentStep >= step.id
                  ? 'bg-blue-600 border-blue-600 text-white'
                  : 'border-gray-300 text-gray-400'
              }`}>
                <step.icon className="w-5 h-5" />
              </div>
              <span className={`ml-2 font-medium ${
                currentStep >= step.id ? 'text-blue-600' : 'text-gray-400'
              }`}>
                {step.name}
              </span>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-blue-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </Card>

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {currentStep === 1 && (
          <motion.div
            key="step1"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <Card.Header>
                <Card.Title>Select a Service</Card.Title>
                <Card.Description>Choose the type of appointment you need</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {services.map((service) => (
                    <motion.div
                      key={service.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-6 border-2 rounded-xl cursor-pointer transition-all ${
                        selectedService?.id === service.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedService(service)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <span className="text-3xl">{service.image}</span>
                            <div>
                              <h3 className="font-semibold text-gray-900">{service.name}</h3>
                              <p className="text-sm text-gray-500">{service.category}</p>
                            </div>
                          </div>
                          <p className="text-gray-600 text-sm mb-4">{service.description}</p>
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center text-sm text-gray-500">
                                <Clock className="w-4 h-4 mr-1" />
                                {service.duration} min
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="text-2xl font-bold text-gray-900">${service.price}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </Card.Content>
            </Card>
          </motion.div>
        )}

        {currentStep === 2 && (
          <motion.div
            key="step2"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <Card.Header>
                <Card.Title>Choose Your Provider</Card.Title>
                <Card.Description>Select a healthcare professional</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {staff.map((provider) => (
                    <motion.div
                      key={provider.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-6 border-2 rounded-xl cursor-pointer transition-all ${
                        selectedStaff?.id === provider.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedStaff(provider)}
                    >
                      <div className="text-center mb-4">
                        <div className="text-6xl mb-3">{provider.image}</div>
                        <h3 className="font-semibold text-gray-900">{provider.name}</h3>
                        <p className="text-sm text-gray-500">{provider.title}</p>
                      </div>

                      <div className="flex items-center justify-center mb-3">
                        <div className="flex items-center">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span className="ml-1 text-sm font-medium">{provider.rating}</span>
                          <span className="ml-1 text-sm text-gray-500">({provider.reviews} reviews)</span>
                        </div>
                      </div>

                      <div className="mb-4">
                        <p className="text-sm text-gray-600 text-center">{provider.bio}</p>
                      </div>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-900">Specialties:</h4>
                        <div className="flex flex-wrap gap-1">
                          {provider.specialties.map((specialty, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </Card.Content>
            </Card>
          </motion.div>
        )}

        {currentStep === 3 && (
          <motion.div
            key="step3"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Calendar */}
              <Card>
                <Card.Header>
                  <Card.Title>Select Date</Card.Title>
                  <Card.Description>Choose your preferred appointment date</Card.Description>
                </Card.Header>
                <Card.Content>
                  <div className="grid grid-cols-7 gap-2 mb-4">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                        {day}
                      </div>
                    ))}
                  </div>
                  <div className="grid grid-cols-7 gap-2">
                    {Array.from({ length: 35 }, (_, i) => {
                      const date = new Date()
                      date.setDate(date.getDate() + i - 7)
                      const isToday = date.toDateString() === new Date().toDateString()
                      const isPast = date < new Date().setHours(0, 0, 0, 0)
                      const isSelected = selectedDate === date.toDateString()

                      return (
                        <motion.button
                          key={i}
                          whileHover={{ scale: isPast ? 1 : 1.1 }}
                          whileTap={{ scale: isPast ? 1 : 0.95 }}
                          disabled={isPast}
                          onClick={() => !isPast && setSelectedDate(date.toDateString())}
                          className={`p-2 text-sm rounded-lg transition-all ${
                            isPast
                              ? 'text-gray-300 cursor-not-allowed'
                              : isSelected
                              ? 'bg-blue-600 text-white'
                              : isToday
                              ? 'bg-blue-100 text-blue-600 font-medium'
                              : 'hover:bg-gray-100'
                          }`}
                        >
                          {date.getDate()}
                        </motion.button>
                      )
                    })}
                  </div>
                </Card.Content>
              </Card>

              {/* Time Slots */}
              <Card>
                <Card.Header>
                  <Card.Title>Select Time</Card.Title>
                  <Card.Description>Available time slots for {selectedDate}</Card.Description>
                </Card.Header>
                <Card.Content>
                  {selectedDate ? (
                    <div className="grid grid-cols-2 gap-3">
                      {timeSlots.map((time) => (
                        <motion.button
                          key={time}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setSelectedTime(time)}
                          className={`p-3 text-sm rounded-lg border-2 transition-all ${
                            selectedTime === time
                              ? 'border-blue-500 bg-blue-50 text-blue-600'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          {time}
                        </motion.button>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      Please select a date first
                    </div>
                  )}
                </Card.Content>
              </Card>
            </div>
          </motion.div>
        )}

        {currentStep === 4 && (
          <motion.div
            key="step4"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <Card.Header>
                <Card.Title>Confirm Your Appointment</Card.Title>
                <Card.Description>Review your appointment details</Card.Description>
              </Card.Header>
              <Card.Content>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Appointment Summary */}
                  <div className="space-y-6">
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl">
                      <h3 className="font-semibold text-gray-900 mb-4">Appointment Summary</h3>

                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Service:</span>
                          <span className="font-medium">{selectedService?.name}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Provider:</span>
                          <span className="font-medium">{selectedStaff?.name}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Date:</span>
                          <span className="font-medium">{selectedDate}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Time:</span>
                          <span className="font-medium">{selectedTime}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-gray-600">Duration:</span>
                          <span className="font-medium">{selectedService?.duration} minutes</span>
                        </div>
                        <div className="border-t pt-4">
                          <div className="flex items-center justify-between text-lg font-semibold">
                            <span>Total:</span>
                            <span>${selectedService?.price}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Information */}
                  <div className="space-y-6">
                    <div className="bg-gray-50 p-6 rounded-xl">
                      <h3 className="font-semibold text-gray-900 mb-4">Payment Information</h3>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Card Number
                          </label>
                          <input
                            type="text"
                            placeholder="1234 5678 9012 3456"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Expiry Date
                            </label>
                            <input
                              type="text"
                              placeholder="MM/YY"
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              CVV
                            </label>
                            <input
                              type="text"
                              placeholder="123"
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card.Content>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Navigation Buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 1}
          icon={<ArrowLeft className="w-4 h-4" />}
        >
          Previous
        </Button>

        {currentStep < 4 ? (
          <Button
            onClick={nextStep}
            disabled={
              (currentStep === 1 && !selectedService) ||
              (currentStep === 2 && !selectedStaff) ||
              (currentStep === 3 && (!selectedDate || !selectedTime))
            }
            icon={<ArrowRight className="w-4 h-4" />}
            iconPosition="right"
          >
            Next
          </Button>
        ) : (
          <Button
            onClick={handleBooking}
            icon={<CheckCircle className="w-4 h-4" />}
          >
            Confirm Booking
          </Button>
        )}
      </div>

      {/* Confirmation Modal */}
      <Modal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        title="Booking Confirmed!"
        size="md"
      >
        <div className="text-center py-6">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Your appointment has been booked successfully!
          </h3>
          <p className="text-gray-600 mb-6">
            You will receive a confirmation email shortly with all the details.
          </p>
          <div className="space-y-3">
            <Button fullWidth>
              Add to Calendar
            </Button>
            <Button variant="outline" fullWidth onClick={() => setShowConfirmation(false)}>
              Close
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  )
}

export default BookingPage
