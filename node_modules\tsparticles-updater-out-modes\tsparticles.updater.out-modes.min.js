/*! For license information please see tsparticles.updater.out-modes.min.js.LICENSE.txt */
!function(t,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],o);else{var e="object"==typeof exports?o(require("tsparticles-engine")):o(t.window);for(var i in e)("object"==typeof exports?exports:t)[i]=e[i]}}(this,(t=>(()=>{"use strict";var o={961:o=>{o.exports=t}},e={};function i(t){var n=e[t];if(void 0!==n)return n.exports;var s=e[t]={exports:{}};return o[t](s,s.exports,i),s.exports}i.d=(t,o)=>{for(var e in o)i.o(o,e)&&!i.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:o[e]})},i.o=(t,o)=>Object.prototype.hasOwnProperty.call(t,o),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{i.r(n),i.d(n,{loadOutModesUpdater:()=>c});var t=i(961);class o{constructor(t){this.container=t,this.modes=["bounce","bounce-vertical","bounce-horizontal","bounceVertical","bounceHorizontal","split"]}update(o,e,i,n){if(!this.modes.includes(n))return;const s=this.container;let r=!1;for(const[,t]of s.plugins)if(void 0!==t.particleBounce&&(r=t.particleBounce(o,i,e)),r)break;if(r)return;const a=o.getPosition(),c=o.offset,d=o.getRadius(),u=(0,t.calculateBounds)(a,d),p=s.canvas.size;!function(o){if("bounce"!==o.outMode&&"bounce-horizontal"!==o.outMode&&"bounceHorizontal"!==o.outMode&&"split"!==o.outMode||"left"!==o.direction&&"right"!==o.direction)return;o.bounds.right<0&&"left"===o.direction?o.particle.position.x=o.size+o.offset.x:o.bounds.left>o.canvasSize.width&&"right"===o.direction&&(o.particle.position.x=o.canvasSize.width-o.size-o.offset.x);const e=o.particle.velocity.x;let i=!1;if("right"===o.direction&&o.bounds.right>=o.canvasSize.width&&e>0||"left"===o.direction&&o.bounds.left<=0&&e<0){const e=(0,t.getValue)(o.particle.options.bounce.horizontal);o.particle.velocity.x*=-e,i=!0}if(!i)return;const n=o.offset.x+o.size;o.bounds.right>=o.canvasSize.width&&"right"===o.direction?o.particle.position.x=o.canvasSize.width-n:o.bounds.left<=0&&"left"===o.direction&&(o.particle.position.x=n),"split"===o.outMode&&o.particle.destroy()}({particle:o,outMode:n,direction:e,bounds:u,canvasSize:p,offset:c,size:d}),function(o){if("bounce"!==o.outMode&&"bounce-vertical"!==o.outMode&&"bounceVertical"!==o.outMode&&"split"!==o.outMode||"bottom"!==o.direction&&"top"!==o.direction)return;o.bounds.bottom<0&&"top"===o.direction?o.particle.position.y=o.size+o.offset.y:o.bounds.top>o.canvasSize.height&&"bottom"===o.direction&&(o.particle.position.y=o.canvasSize.height-o.size-o.offset.y);const e=o.particle.velocity.y;let i=!1;if("bottom"===o.direction&&o.bounds.bottom>=o.canvasSize.height&&e>0||"top"===o.direction&&o.bounds.top<=0&&e<0){const e=(0,t.getValue)(o.particle.options.bounce.vertical);o.particle.velocity.y*=-e,i=!0}if(!i)return;const n=o.offset.y+o.size;o.bounds.bottom>=o.canvasSize.height&&"bottom"===o.direction?o.particle.position.y=o.canvasSize.height-n:o.bounds.top<=0&&"top"===o.direction&&(o.particle.position.y=n),"split"===o.outMode&&o.particle.destroy()}({particle:o,outMode:n,direction:e,bounds:u,canvasSize:p,offset:c,size:d})}}class e{constructor(t){this.container=t,this.modes=["destroy"]}update(o,e,i,n){if(!this.modes.includes(n))return;const s=this.container;switch(o.outType){case"normal":case"outside":if((0,t.isPointInside)(o.position,s.canvas.size,t.Vector.origin,o.getRadius(),e))return;break;case"inside":{const{dx:e,dy:i}=(0,t.getDistances)(o.position,o.moveCenter),{x:n,y:s}=o.velocity;if(n<0&&e>o.moveCenter.radius||s<0&&i>o.moveCenter.radius||n>=0&&e<-o.moveCenter.radius||s>=0&&i<-o.moveCenter.radius)return;break}}s.particles.remove(o,void 0,!0)}}class s{constructor(t){this.container=t,this.modes=["none"]}update(o,e,i,n){if(!this.modes.includes(n))return;if(o.options.move.distance.horizontal&&("left"===e||"right"===e)||o.options.move.distance.vertical&&("top"===e||"bottom"===e))return;const s=o.options.move.gravity,r=this.container,a=r.canvas.size,c=o.getRadius();if(s.enable){const t=o.position;(!s.inverse&&t.y>a.height+c&&"bottom"===e||s.inverse&&t.y<-c&&"top"===e)&&r.particles.remove(o)}else{if(o.velocity.y>0&&o.position.y<=a.height+c||o.velocity.y<0&&o.position.y>=-c||o.velocity.x>0&&o.position.x<=a.width+c||o.velocity.x<0&&o.position.x>=-c)return;(0,t.isPointInside)(o.position,r.canvas.size,t.Vector.origin,c,e)||r.particles.remove(o)}}}class r{constructor(t){this.container=t,this.modes=["out"]}update(o,e,i,n){if(!this.modes.includes(n))return;const s=this.container;switch(o.outType){case"inside":{const{x:e,y:i}=o.velocity,n=t.Vector.origin;n.length=o.moveCenter.radius,n.angle=o.velocity.angle+Math.PI,n.addTo(t.Vector.create(o.moveCenter));const{dx:r,dy:a}=(0,t.getDistances)(o.position,n);if(e<=0&&r>=0||i<=0&&a>=0||e>=0&&r<=0||i>=0&&a<=0)return;o.position.x=Math.floor((0,t.randomInRange)({min:0,max:s.canvas.size.width})),o.position.y=Math.floor((0,t.randomInRange)({min:0,max:s.canvas.size.height}));const{dx:c,dy:d}=(0,t.getDistances)(o.position,o.moveCenter);o.direction=Math.atan2(-d,-c),o.velocity.angle=o.direction;break}default:if((0,t.isPointInside)(o.position,s.canvas.size,t.Vector.origin,o.getRadius(),e))return;switch(o.outType){case"outside":{o.position.x=Math.floor((0,t.randomInRange)({min:-o.moveCenter.radius,max:o.moveCenter.radius}))+o.moveCenter.x,o.position.y=Math.floor((0,t.randomInRange)({min:-o.moveCenter.radius,max:o.moveCenter.radius}))+o.moveCenter.y;const{dx:e,dy:i}=(0,t.getDistances)(o.position,o.moveCenter);o.moveCenter.radius&&(o.direction=Math.atan2(i,e),o.velocity.angle=o.direction);break}case"normal":{const i=o.options.move.warp,n=s.canvas.size,r={bottom:n.height+o.getRadius()+o.offset.y,left:-o.getRadius()-o.offset.x,right:n.width+o.getRadius()+o.offset.x,top:-o.getRadius()-o.offset.y},a=o.getRadius(),c=(0,t.calculateBounds)(o.position,a);"right"===e&&c.left>n.width+o.offset.x?(o.position.x=r.left,o.initialPosition.x=o.position.x,i||(o.position.y=(0,t.getRandom)()*n.height,o.initialPosition.y=o.position.y)):"left"===e&&c.right<-o.offset.x&&(o.position.x=r.right,o.initialPosition.x=o.position.x,i||(o.position.y=(0,t.getRandom)()*n.height,o.initialPosition.y=o.position.y)),"bottom"===e&&c.top>n.height+o.offset.y?(i||(o.position.x=(0,t.getRandom)()*n.width,o.initialPosition.x=o.position.x),o.position.y=r.top,o.initialPosition.y=o.position.y):"top"===e&&c.bottom<-o.offset.y&&(i||(o.position.x=(0,t.getRandom)()*n.width,o.initialPosition.x=o.position.x),o.position.y=r.bottom,o.initialPosition.y=o.position.y);break}}}}}class a{constructor(t){this.container=t,this._updateOutMode=(t,o,e,i)=>{for(const n of this.updaters)n.update(t,i,o,e)},this.updaters=[new o(t),new e(t),new r(t),new s(t)]}init(){}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,o){const e=t.options.move.outModes;this._updateOutMode(t,o,e.bottom??e.default,"bottom"),this._updateOutMode(t,o,e.left??e.default,"left"),this._updateOutMode(t,o,e.right??e.default,"right"),this._updateOutMode(t,o,e.top??e.default,"top")}}async function c(t,o=!0){await t.addParticleUpdater("outModes",(t=>new a(t)),o)}})(),n})()));