{"name": "tsparticles-shape-polygon", "version": "2.12.0", "description": "tsParticles polygon shape", "homepage": "https://particles.js.org", "repository": {"type": "git", "url": "git+https://github.com/matteobruni/tsparticles.git", "directory": "shapes/polygon"}, "keywords": ["front-end", "frontend", "tsparticles", "particles", "particle", "canvas", "jsparticles", "xparticles", "particles-js", "particles.js", "particles-ts", "particles.ts", "typescript", "javascript", "animation", "web", "html5", "web-design", "webdesign", "css", "html", "css3", "animated", "background", "tsparticles-shape"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/matteobruni/tsparticles/issues"}, "main": "cjs/index.js", "jsdelivr": "tsparticles.shape.polygon.min.js", "unpkg": "tsparticles.shape.polygon.min.js", "module": "esm/index.js", "types": "types/index.d.ts", "sideEffects": false, "dependencies": {"tsparticles-engine": "^2.12.0"}}