/*! For license information please see tsparticles.updater.life.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var n="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var i in n)("object"==typeof exports?exports:e)[i]=n[i]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},n={};function i(e){var o=n[e];if(void 0!==o)return o.exports;var a=n[e]={exports:{}};return t[e](a,a.exports,i),a.exports}i.d=(e,t)=>{for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return(()=>{i.r(o),i.d(o,{loadLifeUpdater:()=>s});var e=i(961);class t extends e.ValueWithRandom{constructor(){super(),this.sync=!1}load(e){e&&(super.load(e),void 0!==e.sync&&(this.sync=e.sync))}}class n extends e.ValueWithRandom{constructor(){super(),this.random.minimumValue=1e-4,this.sync=!1}load(e){e&&(super.load(e),void 0!==e.sync&&(this.sync=e.sync))}}class a{constructor(){this.count=0,this.delay=new t,this.duration=new n}load(e){e&&(void 0!==e.count&&(this.count=e.count),this.delay.load(e.delay),this.duration.load(e.duration))}}class r{constructor(e){this.container=e}init(t){const n=this.container,i=t.options.life;i&&(t.life={delay:n.retina.reduceFactor?(0,e.getRangeValue)(i.delay.value)*(i.delay.sync?1:(0,e.getRandom)())/n.retina.reduceFactor*1e3:0,delayTime:0,duration:n.retina.reduceFactor?(0,e.getRangeValue)(i.duration.value)*(i.duration.sync?1:(0,e.getRandom)())/n.retina.reduceFactor*1e3:0,time:0,count:i.count},t.life.duration<=0&&(t.life.duration=-1),t.life.count<=0&&(t.life.count=-1),t.life&&(t.spawning=t.life.delay>0))}isEnabled(e){return!e.destroyed}loadOptions(e,...t){e.life||(e.life=new a);for(const n of t)e.life.load(n?.life)}update(t,n){if(!this.isEnabled(t)||!t.life)return;const i=t.life;let o=!1;if(t.spawning){if(i.delayTime+=n.value,!(i.delayTime>=t.life.delay))return;o=!0,t.spawning=!1,i.delayTime=0,i.time=0}if(-1===i.duration)return;if(t.spawning)return;if(o?i.time=0:i.time+=n.value,i.time<i.duration)return;if(i.time=0,t.life.count>0&&t.life.count--,0===t.life.count)return void t.destroy();const a=this.container.canvas.size,r=(0,e.setRangeValue)(0,a.width),s=(0,e.setRangeValue)(0,a.width);t.position.x=(0,e.randomInRange)(r),t.position.y=(0,e.randomInRange)(s),t.spawning=!0,i.delayTime=0,i.time=0,t.reset();const l=t.options.life;l&&(i.delay=1e3*(0,e.getRangeValue)(l.delay.value),i.duration=1e3*(0,e.getRangeValue)(l.duration.value))}}async function s(e,t=!0){await e.addParticleUpdater("life",(e=>new r(e)),t)}})(),o})()));