/*! For license information please see tsparticles.shape.star.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var r="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var o in r)("object"==typeof exports?exports:e)[o]=r[o]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},r={};function o(e){var n=r[e];if(void 0!==n)return n.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,o),a.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{o.r(n),o.d(n,{loadStarShape:()=>r});var e=o(961);class t{draw(e,t,r){const o=t.sides,n=t.starInset??2;e.moveTo(0,0-r);for(let t=0;t<o;t++)e.rotate(Math.PI/o),e.lineTo(0,0-r*n),e.rotate(Math.PI/o),e.lineTo(0,0-r)}getSidesCount(t){const r=t.shapeData;return Math.round((0,e.getRangeValue)(r?.sides??r?.nb_sides??5))}particleInit(t,r){const o=r.shapeData,n=(0,e.getRangeValue)(o?.inset??2);r.starInset=n}}async function r(e,r=!0){await e.addShape("star",new t,r)}})(),n})()));