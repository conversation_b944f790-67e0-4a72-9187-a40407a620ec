/*! For license information please see tsparticles.updater.rotate.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var a="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var o in a)("object"==typeof exports?exports:e)[o]=a[o]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},a={};function o(e){var n=a[e];if(void 0!==n)return n.exports;var i=a[e]={exports:{}};return t[e](i,i.exports,o),i.exports}o.d=(e,t)=>{for(var a in t)o.o(t,a)&&!o.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{o.r(n),o.d(n,{loadRotateUpdater:()=>r});var e=o(961);class t{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=(0,e.setRangeValue)(t.speed)),void 0!==t.decay&&(this.decay=(0,e.setRangeValue)(t.decay)),void 0!==t.sync&&(this.sync=t.sync))}}class a extends e.ValueWithRandom{constructor(){super(),this.animation=new t,this.direction="clockwise",this.path=!1,this.value=0}load(e){e&&(super.load(e),void 0!==e.direction&&(this.direction=e.direction),this.animation.load(e.animation),void 0!==e.path&&(this.path=e.path))}}class i{constructor(e){this.container=e}init(t){const a=t.options.rotate;if(!a)return;t.rotate={enable:a.animation.enable,value:(0,e.getRangeValue)(a.value)*Math.PI/180},t.pathRotation=a.path;let o=a.direction;if("random"===o){o=Math.floor(2*(0,e.getRandom)())>0?"counter-clockwise":"clockwise"}switch(o){case"counter-clockwise":case"counterClockwise":t.rotate.status="decreasing";break;case"clockwise":t.rotate.status="increasing"}const n=a.animation;n.enable&&(t.rotate.decay=1-(0,e.getRangeValue)(n.decay),t.rotate.velocity=(0,e.getRangeValue)(n.speed)/360*this.container.retina.reduceFactor,n.sync||(t.rotate.velocity*=(0,e.getRandom)())),t.rotation=t.rotate.value}isEnabled(e){const t=e.options.rotate;return!!t&&(!e.destroyed&&!e.spawning&&t.animation.enable&&!t.path)}loadOptions(e,...t){e.rotate||(e.rotate=new a);for(const a of t)e.rotate.load(a?.rotate)}update(e,t){this.isEnabled(e)&&(!function(e,t){const a=e.rotate,o=e.options.rotate;if(!a||!o)return;const n=o.animation,i=(a.velocity??0)*t.factor,r=2*Math.PI,s=a.decay??1;n.enable&&("increasing"===a.status?(a.value+=i,a.value>r&&(a.value-=r)):(a.value-=i,a.value<0&&(a.value+=r)),a.velocity&&1!==s&&(a.velocity*=s))}(e,t),e.rotation=e.rotate?.value??0)}}async function r(e,t=!0){await e.addParticleUpdater("rotate",(e=>new i(e)),t)}})(),n})()));