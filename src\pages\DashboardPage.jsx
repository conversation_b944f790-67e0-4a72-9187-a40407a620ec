import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Calendar,
  Users,
  DollarSign,
  Clock,
  TrendingUp,
  TrendingDown,
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  Activity,
  BarChart3,
  PieChart,
  MapPin,
  Phone,
  Mail,
  Bell
} from 'lucide-react'
import { useAuthStore } from '../stores/authStore'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js'
import { Line, Bar, Doughnut } from 'react-chartjs-2'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

const DashboardPage = () => {
  const { user } = useAuthStore()
  const [timeRange, setTimeRange] = useState('7d')

  const stats = [
    {
      name: 'Total Appointments',
      value: '2,847',
      icon: Calendar,
      change: '+12.5%',
      changeType: 'positive',
      color: 'from-blue-500 to-cyan-500',
      description: 'vs last month'
    },
    {
      name: 'Active Clients',
      value: '1,256',
      icon: Users,
      change: '****%',
      changeType: 'positive',
      color: 'from-green-500 to-emerald-500',
      description: 'vs last month'
    },
    {
      name: 'Revenue',
      value: '$124,500',
      icon: DollarSign,
      change: '+15.3%',
      changeType: 'positive',
      color: 'from-purple-500 to-pink-500',
      description: 'vs last month'
    },
    {
      name: 'Avg. Rating',
      value: '4.8',
      icon: Star,
      change: '+0.2',
      changeType: 'positive',
      color: 'from-yellow-500 to-orange-500',
      description: 'out of 5.0'
    },
  ]

  // Chart data
  const revenueData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
    datasets: [
      {
        label: 'Revenue',
        data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        tension: 0.4,
        fill: true,
      },
    ],
  }

  const appointmentStatusData = {
    labels: ['Completed', 'Scheduled', 'Cancelled', 'No Show'],
    datasets: [
      {
        data: [65, 25, 7, 3],
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(156, 163, 175, 0.8)',
        ],
        borderWidth: 0,
      },
    ],
  }

  const recentAppointments = [
    {
      id: 1,
      client: 'Sarah Johnson',
      service: 'Consultation',
      time: '2:00 PM',
      status: 'confirmed',
      avatar: 'SJ',
      duration: '45 min',
      revenue: '$150'
    },
    {
      id: 2,
      client: 'Michael Chen',
      service: 'Follow-up',
      time: '3:30 PM',
      status: 'pending',
      avatar: 'MC',
      duration: '30 min',
      revenue: '$100'
    },
    {
      id: 3,
      client: 'Emily Davis',
      service: 'Initial Assessment',
      time: '4:15 PM',
      status: 'confirmed',
      avatar: 'ED',
      duration: '60 min',
      revenue: '$200'
    },
  ]

  const upcomingTasks = [
    { id: 1, task: 'Review patient files', priority: 'high', due: '2 hours' },
    { id: 2, task: 'Prepare consultation notes', priority: 'medium', due: '4 hours' },
    { id: 3, task: 'Update treatment plans', priority: 'low', due: '1 day' },
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Welcome back, {user?.firstName}! 👋
          </h1>
          <p className="mt-2 text-gray-600">
            Here's what's happening with your business today.
          </p>
        </div>
        <div className="mt-4 lg:mt-0 flex space-x-3">
          <Button variant="outline" icon={<Bell className="w-4 h-4" />}>
            Notifications
          </Button>
          <Button icon={<Calendar className="w-4 h-4" />}>
            Schedule Appointment
          </Button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="relative overflow-hidden">
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.color} opacity-5`} />
              <div className="relative">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                    <p className="text-3xl font-bold text-gray-900 mt-1">{stat.value}</p>
                  </div>
                  <div className={`p-3 rounded-xl bg-gradient-to-br ${stat.color}`}>
                    <stat.icon className="w-6 h-6 text-white" />
                  </div>
                </div>
                <div className="mt-4 flex items-center">
                  <div className={`flex items-center text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.changeType === 'positive' ? (
                      <TrendingUp className="w-4 h-4 mr-1" />
                    ) : (
                      <TrendingDown className="w-4 h-4 mr-1" />
                    )}
                    {stat.change}
                  </div>
                  <span className="text-sm text-gray-500 ml-2">{stat.description}</span>
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Revenue Chart */}
        <Card className="lg:col-span-2">
          <Card.Header>
            <div className="flex items-center justify-between">
              <Card.Title>Revenue Overview</Card.Title>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="text-sm border border-gray-300 rounded-lg px-3 py-1"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
              </select>
            </div>
          </Card.Header>
          <Card.Content>
            <div className="h-80">
              <Line
                data={revenueData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                      grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                      },
                    },
                    x: {
                      grid: {
                        display: false,
                      },
                    },
                  },
                }}
              />
            </div>
          </Card.Content>
        </Card>

        {/* Appointment Status */}
        <Card>
          <Card.Header>
            <Card.Title>Appointment Status</Card.Title>
            <Card.Description>Distribution of appointment statuses</Card.Description>
          </Card.Header>
          <Card.Content>
            <div className="h-64">
              <Doughnut
                data={appointmentStatusData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                    },
                  },
                }}
              />
            </div>
          </Card.Content>
        </Card>
      </div>

      {/* Recent Activity and Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Appointments */}
        <Card>
          <Card.Header>
            <div className="flex items-center justify-between">
              <Card.Title>Today's Appointments</Card.Title>
              <Button variant="ghost" size="sm">View All</Button>
            </div>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              {recentAppointments.map((appointment) => (
                <motion.div
                  key={appointment.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                      {appointment.avatar}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{appointment.client}</p>
                      <p className="text-sm text-gray-500">{appointment.service} • {appointment.duration}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{appointment.time}</p>
                    <div className="flex items-center mt-1">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        appointment.status === 'confirmed'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {appointment.status === 'confirmed' ? (
                          <CheckCircle className="w-3 h-3 mr-1" />
                        ) : (
                          <Clock className="w-3 h-3 mr-1" />
                        )}
                        {appointment.status}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </Card.Content>
        </Card>

        {/* Quick Actions & Tasks */}
        <Card>
          <Card.Header>
            <Card.Title>Quick Actions & Tasks</Card.Title>
          </Card.Header>
          <Card.Content>
            <div className="space-y-4">
              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  className="h-20 flex-col"
                  icon={<Calendar className="w-6 h-6 mb-2" />}
                >
                  New Appointment
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex-col"
                  icon={<Users className="w-6 h-6 mb-2" />}
                >
                  Add Client
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex-col"
                  icon={<BarChart3 className="w-6 h-6 mb-2" />}
                >
                  View Reports
                </Button>
                <Button
                  variant="outline"
                  className="h-20 flex-col"
                  icon={<Activity className="w-6 h-6 mb-2" />}
                >
                  Analytics
                </Button>
              </div>

              {/* Upcoming Tasks */}
              <div className="mt-6">
                <h4 className="font-medium text-gray-900 mb-3">Upcoming Tasks</h4>
                <div className="space-y-2">
                  {upcomingTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          task.priority === 'high' ? 'bg-red-500' :
                          task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                        }`} />
                        <span className="text-sm text-gray-900">{task.task}</span>
                      </div>
                      <span className="text-xs text-gray-500">{task.due}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card.Content>
        </Card>
      </div>
    </div>
  )
}

export default DashboardPage
