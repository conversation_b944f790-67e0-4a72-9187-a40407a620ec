import { useAuthStore } from '../stores/authStore'
import { Calendar, Users, DollarSign, Clock } from 'lucide-react'

const DashboardPage = () => {
  const { user } = useAuthStore()

  const stats = [
    {
      name: 'Total Appointments',
      value: '24',
      icon: Calendar,
      change: '+12%',
      changeType: 'positive',
    },
    {
      name: 'Active Clients',
      value: '156',
      icon: Users,
      change: '+8%',
      changeType: 'positive',
    },
    {
      name: 'Revenue',
      value: '$12,450',
      icon: DollarSign,
      change: '+15%',
      changeType: 'positive',
    },
    {
      name: 'Avg. Session',
      value: '45 min',
      icon: Clock,
      change: '-2%',
      changeType: 'negative',
    },
  ]

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="mt-1 text-sm text-gray-600">
          Here's what's happening with your appointments today.
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <stat.icon className="h-8 w-8 text-primary-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {stat.name}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {stat.value}
                    </div>
                    <div
                      className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'positive'
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}
                    >
                      {stat.change}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Recent Appointments
          </h3>
          <div className="space-y-3">
            {[1, 2, 3].map((item) => (
              <div key={item} className="flex items-center justify-between py-2">
                <div className="flex items-center">
                  <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-primary-600" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900">
                      Consultation with John Doe
                    </p>
                    <p className="text-sm text-gray-500">Today at 2:00 PM</p>
                  </div>
                </div>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Confirmed
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="space-y-3">
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">
                  Schedule New Appointment
                </span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
              <div className="flex items-center">
                <Users className="h-5 w-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">
                  Manage Clients
                </span>
              </div>
            </button>
            <button className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-primary-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">
                  View Reports
                </span>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
