import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User } from '../types'
import { supabase } from '../lib/supabase'

interface AuthState {
  user: User | null
  isLoading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (userData: {
    email: string
    password: string
    firstName: string
    lastName: string
    phone?: string
    role?: 'client' | 'staff'
  }) => Promise<void>
  logout: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  checkAuth: () => Promise<void>
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: true,

      login: async (email: string, password: string) => {
        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
          })

          if (error) throw error

          if (data.user) {
            // Fetch user profile
            const { data: profile, error: profileError } = await supabase
              .from('users')
              .select('*')
              .eq('id', data.user.id)
              .single()

            if (profileError) throw profileError

            set({ user: profile })
          }
        } catch (error) {
          console.error('Login error:', error)
          throw error
        }
      },

      register: async (userData) => {
        try {
          const { data, error } = await supabase.auth.signUp({
            email: userData.email,
            password: userData.password,
            options: {
              data: {
                firstName: userData.firstName,
                lastName: userData.lastName,
                phone: userData.phone,
                role: userData.role || 'client',
              },
            },
          })

          if (error) throw error

          if (data.user) {
            // Create user profile
            const { data: profile, error: profileError } = await supabase
              .from('users')
              .insert([
                {
                  id: data.user.id,
                  email: userData.email,
                  firstName: userData.firstName,
                  lastName: userData.lastName,
                  phone: userData.phone,
                  role: userData.role || 'client',
                },
              ])
              .select()
              .single()

            if (profileError) throw profileError

            set({ user: profile })
          }
        } catch (error) {
          console.error('Registration error:', error)
          throw error
        }
      },

      logout: async () => {
        try {
          const { error } = await supabase.auth.signOut()
          if (error) throw error
          set({ user: null })
        } catch (error) {
          console.error('Logout error:', error)
          throw error
        }
      },

      updateProfile: async (updates) => {
        const { user } = get()
        if (!user) throw new Error('No user logged in')

        try {
          const { data, error } = await supabase
            .from('users')
            .update(updates)
            .eq('id', user.id)
            .select()
            .single()

          if (error) throw error

          set({ user: data })
        } catch (error) {
          console.error('Profile update error:', error)
          throw error
        }
      },

      checkAuth: async () => {
        try {
          const { data: { session } } = await supabase.auth.getSession()
          
          if (session?.user) {
            const { data: profile, error } = await supabase
              .from('users')
              .select('*')
              .eq('id', session.user.id)
              .single()

            if (error) throw error
            set({ user: profile, isLoading: false })
          } else {
            set({ user: null, isLoading: false })
          }
        } catch (error) {
          console.error('Auth check error:', error)
          set({ user: null, isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ user: state.user }),
    }
  )
)
