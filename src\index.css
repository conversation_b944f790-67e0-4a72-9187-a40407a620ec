@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* React Big Calendar Styles */
@import 'react-big-calendar/lib/css/react-big-calendar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming */
:root {
  --toast-bg: #363636;
  --toast-color: #fff;
}

.dark {
  --toast-bg: #1f2937;
  --toast-color: #f9fafb;
}

/* Enhanced animations */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm border border-secondary-200 p-6;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .sidebar-nav {
    @apply flex flex-col space-y-1;
  }

  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }

  .sidebar-nav-item.active {
    @apply bg-primary-100 text-primary-700;
  }

  .sidebar-nav-item:hover {
    @apply bg-secondary-100 text-secondary-900;
  }

  /* Enhanced styles for the new UI */
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
  }

  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Calendar customizations */
.rbc-calendar {
  @apply bg-white rounded-lg shadow-sm border border-secondary-200;
}

.rbc-header {
  @apply bg-secondary-50 text-secondary-700 font-medium;
}

.rbc-event {
  @apply bg-primary-500 border-primary-600;
}

.rbc-selected {
  @apply bg-primary-600;
}

.rbc-today {
  @apply bg-primary-50;
}
