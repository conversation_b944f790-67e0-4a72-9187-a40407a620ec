@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* React Big Calendar Styles */
@import 'react-big-calendar/lib/css/react-big-calendar.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-secondary-200 p-6;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .sidebar-nav {
    @apply flex flex-col space-y-1;
  }
  
  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200;
  }
  
  .sidebar-nav-item.active {
    @apply bg-primary-100 text-primary-700;
  }
  
  .sidebar-nav-item:hover {
    @apply bg-secondary-100 text-secondary-900;
  }
}

/* Calendar customizations */
.rbc-calendar {
  @apply bg-white rounded-lg shadow-sm border border-secondary-200;
}

.rbc-header {
  @apply bg-secondary-50 text-secondary-700 font-medium;
}

.rbc-event {
  @apply bg-primary-500 border-primary-600;
}

.rbc-selected {
  @apply bg-primary-600;
}

.rbc-today {
  @apply bg-primary-50;
}
