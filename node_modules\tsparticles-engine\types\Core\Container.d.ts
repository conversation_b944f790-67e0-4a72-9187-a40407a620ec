import { Canvas } from "./Canvas";
import type { ClickMode } from "../Enums/Modes/ClickMode";
import type { Engine } from "./Engine";
import type { IContainerInteractivity } from "./Interfaces/IContainerInteractivity";
import type { IContainerPlugin } from "./Interfaces/IContainerPlugin";
import type { IMovePathGenerator } from "./Interfaces/IMovePathGenerator";
import type { IShapeDrawer } from "./Interfaces/IShapeDrawer";
import type { ISourceOptions } from "../Types/ISourceOptions";
import { Options } from "../Options/Classes/Options";
import type { Particle } from "./Particle";
import { Particles } from "./Particles";
import { Retina } from "./Retina";
import type { Vector } from "./Utils/Vector";
export declare class Container {
    readonly id: string;
    actualOptions: Options;
    readonly canvas: Canvas;
    destroyed: boolean;
    readonly drawers: Map<string, IShapeDrawer>;
    fpsLimit: number;
    interactivity: IContainerInteractivity;
    lastFrameTime?: number;
    pageHidden: boolean;
    readonly particles: Particles;
    pathGenerators: Map<string, IMovePathGenerator>;
    readonly plugins: Map<string, IContainerPlugin>;
    responsiveMaxWidth?: number;
    readonly retina: Retina;
    smooth: boolean;
    started: boolean;
    zLayers: number;
    private _currentTheme?;
    private _delay;
    private _delayTimeout?;
    private _drawAnimationFrame?;
    private _duration;
    private readonly _engine;
    private readonly _eventListeners;
    private _firstStart;
    private _initialSourceOptions;
    private readonly _intersectionObserver;
    private _lifeTime;
    private _options;
    private _paused;
    private _sourceOptions;
    constructor(engine: Engine, id: string, sourceOptions?: ISourceOptions);
    get options(): Options;
    get sourceOptions(): ISourceOptions | undefined;
    addClickHandler(callback: (evt: Event, particles?: Particle[]) => void): void;
    addLifeTime(value: number): void;
    addPath(key: string, generator?: IMovePathGenerator, override?: boolean): boolean;
    alive(): boolean;
    destroy(): void;
    draw(force: boolean): void;
    export(type: string, options?: Record<string, unknown>): Promise<Blob | undefined>;
    getAnimationStatus(): boolean;
    handleClickMode(mode: ClickMode | string): void;
    init(): Promise<void>;
    loadTheme(name?: string): Promise<void>;
    pause(): void;
    play(force?: boolean): void;
    refresh(): Promise<void>;
    reset(): Promise<void>;
    setNoise(noiseOrGenerator?: IMovePathGenerator | ((particle: Particle) => Vector), init?: () => void, update?: () => void): void;
    setPath(pathOrGenerator?: IMovePathGenerator | ((particle: Particle) => Vector), init?: () => void, update?: () => void): void;
    start(): Promise<void>;
    stop(): void;
    updateActualOptions(): boolean;
    private readonly _intersectionManager;
    private readonly _nextFrame;
}
