# Appointment Management System

A comprehensive appointment management system built with React + Vite, featuring role-based access control, real-time scheduling, payment integration, and more.

## Features

### 🔹 Key Components

- **User Management**: Admin, Staff, and Client panels with role-based access
- **Appointment Scheduling**: Real-time calendar with availability checking
- **Notification System**: Email/SMS notifications for confirmations, reminders, and cancellations
- **Calendar Integration**: Sync with Google Calendar and Outlook
- **Payment Integration**: Online payments via Stripe with invoice generation
- **Admin Dashboard**: Comprehensive analytics and reporting
- **Service Management**: Create and manage services with pricing and availability
- **Customer Support**: Feedback system and support features
- **Security & Privacy**: RBAC, data encryption, and audit logs

### 🚀 Tech Stack

- **Frontend**: React 18, Vite, JavaScript
- **Styling**: Tailwind CSS, Radix UI components
- **State Management**: Zustand
- **Forms**: React Hook Form with Zod validation
- **Backend**: Supabase (Database, Auth, Real-time)
- **Payments**: Stripe
- **Calendar**: React Big Calendar
- **Charts**: Recharts
- **Notifications**: React Hot Toast

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- Stripe account (for payments)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd appointment-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Update the `.env` file with your actual values:
   - Supabase URL and anon key
   - Stripe publishable key
   - Other configuration values

4. **Database Setup**
   - Create a new Supabase project
   - Run the database migrations (SQL scripts will be provided)
   - Set up Row Level Security (RLS) policies

5. **Start the development server**
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:3000`

## Project Structure

```
src/
├── components/          # Reusable UI components
│   └── Layout.jsx      # Main layout component
├── pages/              # Page components
│   ├── auth/           # Authentication pages
│   ├── admin/          # Admin-only pages
│   ├── staff/          # Staff-only pages
│   └── client/         # Client-only pages
├── stores/             # Zustand stores
│   └── authStore.js    # Authentication state
├── lib/                # Utility libraries
│   └── supabase.js     # Supabase client
├── App.jsx             # Main app component
└── main.jsx            # App entry point
```

## User Roles

### Admin
- Full system access
- User management
- Service management
- Reports and analytics
- System configuration

### Staff
- View and manage their appointments
- Update availability
- Client communication
- Basic reporting

### Client
- Book appointments
- View appointment history
- Manage profile
- Make payments

## Development Roadmap

- [x] Project setup and authentication
- [ ] Database schema implementation
- [ ] Appointment booking system
- [ ] Calendar integration
- [ ] Payment processing
- [ ] Notification system
- [ ] Admin dashboard
- [ ] Reports and analytics
- [ ] Mobile responsiveness
- [ ] Testing suite

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
