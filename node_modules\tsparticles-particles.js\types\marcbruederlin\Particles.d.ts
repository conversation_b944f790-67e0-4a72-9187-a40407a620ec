import { type RecursivePartial, type SingleOrMultiple } from "tsparticles-engine";
interface ResponsiveOptions {
    breakpoint: number;
    options: ParticlesOptions;
}
interface ParticlesOptions {
    color: SingleOrMultiple<string>;
    connectParticles: boolean;
    maxParticles: number;
    minDistance: number;
    responsive: ResponsiveOptions[];
    selector: string;
    sizeVariations: number;
    speed: number;
}
export declare class Particles {
    private _container?;
    static init(options: RecursivePartial<ParticlesOptions>): Particles;
    destroy(): void;
    pauseAnimation(): void;
    resumeAnimation(): void;
}
export {};
