import type { Container } from "./Container";
import type { IContainerPlugin } from "./Interfaces/IContainerPlugin";
import type { IDelta } from "./Interfaces/IDelta";
import type { IDimension } from "./Interfaces/IDimension";
import type { Particle } from "./Particle";
export declare class Canvas {
    private readonly container;
    element?: HTMLCanvasElement;
    resizeFactor?: IDimension;
    readonly size: IDimension;
    private _colorPlugins;
    private _context;
    private _coverColorStyle?;
    private _generated;
    private _mutationObserver?;
    private _originalStyle?;
    private _postDrawUpdaters;
    private _preDrawUpdaters;
    private _resizePlugins;
    private _trailFill?;
    constructor(container: Container);
    private get _fullScreen();
    clear(): void;
    destroy(): void;
    draw<T>(cb: (context: CanvasRenderingContext2D) => T): T | undefined;
    drawParticle(particle: Particle, delta: IDelta): void;
    drawParticlePlugin(plugin: IContainerPlugin, particle: Particle, delta: IDelta): void;
    drawPlugin(plugin: IContainerPlugin, delta: IDelta): void;
    init(): Promise<void>;
    initBackground(): void;
    initPlugins(): void;
    initUpdaters(): void;
    loadCanvas(canvas: HTMLCanvasElement): void;
    paint(): void;
    resize(): boolean;
    stop(): void;
    windowResize(): Promise<void>;
    private readonly _applyPostDrawUpdaters;
    private readonly _applyPreDrawUpdaters;
    private readonly _applyResizePlugins;
    private readonly _getPluginParticleColors;
    private readonly _initCover;
    private readonly _initStyle;
    private readonly _initTrail;
    private readonly _paintBase;
    private readonly _paintImage;
    private readonly _repairStyle;
    private readonly _resetOriginalStyle;
    private readonly _safeMutationObserver;
    private readonly _setFullScreenStyle;
}
