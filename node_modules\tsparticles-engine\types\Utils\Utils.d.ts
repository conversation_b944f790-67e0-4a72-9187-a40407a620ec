import type { ICoordinates, ICoordinatesWithMode } from "../Core/Interfaces/ICoordinates";
import type { IDimension, IDimensionWithMode } from "../Core/Interfaces/IDimension";
import type { DivEvent } from "../Options/Classes/Interactivity/Events/DivEvent";
import type { DivMode } from "../Enums/Modes/DivMode";
import type { IBounds } from "../Core/Interfaces/IBounds";
import type { ICircleBouncer } from "../Core/Interfaces/ICircleBouncer";
import type { IModeDiv } from "../Options/Interfaces/Interactivity/Modes/IModeDiv";
import type { IParticle } from "../Core/Interfaces/IParticle";
import type { IParticleNumericValueAnimation } from "../Core/Interfaces/IParticleValueAnimation";
import { OutModeDirection } from "../Enums/Directions/OutModeDirection";
import type { RangedAnimationValueWithRandom } from "../Options/Classes/ValueWithRandom";
import type { SingleOrMultiple } from "../Types/SingleOrMultiple";
interface ILogger {
    debug(message?: unknown, ...optionalParams: unknown[]): void;
    error(message?: unknown, ...optionalParams: unknown[]): void;
    info(message?: unknown, ...optionalParams: unknown[]): void;
    log(message?: unknown, ...optionalParams: unknown[]): void;
    verbose(message?: unknown, ...optionalParams: unknown[]): void;
    warning(message?: unknown, ...optionalParams: unknown[]): void;
}
export declare function setLogger(logger: ILogger): void;
export declare function getLogger(): ILogger;
export declare function isSsr(): boolean;
export declare function hasMatchMedia(): boolean;
export declare function safeMatchMedia(query: string): MediaQueryList | undefined;
export declare function safeMutationObserver(callback: (records: MutationRecord[]) => void): MutationObserver | undefined;
export declare function isInArray<T>(value: T, array: SingleOrMultiple<T>): boolean;
export declare function loadFont(font?: string, weight?: string): Promise<void>;
export declare function arrayRandomIndex<T>(array: T[]): number;
export declare function itemFromArray<T>(array: T[], index?: number, useIndex?: boolean): T;
export declare function isPointInside(point: ICoordinates, size: IDimension, offset: ICoordinates, radius?: number, direction?: OutModeDirection): boolean;
export declare function areBoundsInside(bounds: IBounds, size: IDimension, offset: ICoordinates, direction?: OutModeDirection): boolean;
export declare function calculateBounds(point: ICoordinates, radius: number): IBounds;
export declare function deepExtend(destination: unknown, ...sources: unknown[]): unknown;
export declare function isDivModeEnabled(mode: DivMode, divs: SingleOrMultiple<DivEvent>): boolean;
export declare function divModeExecute(mode: DivMode, divs: SingleOrMultiple<DivEvent>, callback: (id: string, div: DivEvent) => void): void;
export declare function singleDivModeExecute(div: DivEvent, callback: (selector: string, div: DivEvent) => void): void;
export declare function divMode<T extends IModeDiv>(divs?: SingleOrMultiple<T>, element?: HTMLElement): T | undefined;
export declare function circleBounceDataFromParticle(p: IParticle): ICircleBouncer;
export declare function circleBounce(p1: ICircleBouncer, p2: ICircleBouncer): void;
export declare function rectBounce(particle: IParticle, divBounds: IBounds): void;
export declare function executeOnSingleOrMultiple<T, U = void>(obj: SingleOrMultiple<T>, callback: (obj: T, index: number) => U): SingleOrMultiple<U>;
export declare function itemFromSingleOrMultiple<T>(obj: SingleOrMultiple<T>, index?: number, useIndex?: boolean): T;
export declare function findItemFromSingleOrMultiple<T>(obj: SingleOrMultiple<T>, callback: (obj: T, index: number) => boolean): T | undefined;
export declare function initParticleNumericAnimationValue(options: RangedAnimationValueWithRandom, pxRatio: number): IParticleNumericValueAnimation;
export declare function getPosition(position: ICoordinatesWithMode, canvasSize: IDimension): ICoordinates;
export declare function getSize(size: IDimensionWithMode, canvasSize: IDimension): IDimension;
export declare function isBoolean(arg: unknown): arg is boolean;
export declare function isString(arg: unknown): arg is string;
export declare function isNumber(arg: unknown): arg is number;
export declare function isFunction(arg: unknown): arg is Function;
export declare function isObject<T extends object>(arg: unknown): arg is T;
export declare function isArray<T>(arg: unknown): arg is T[];
export {};
