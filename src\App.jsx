import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './stores/authStore'
import Layout from './components/Layout'
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'
import DashboardPage from './pages/DashboardPage'
import AppointmentsPage from './pages/AppointmentsPage'
import ServicesPage from './pages/ServicesPage'
import UsersPage from './pages/admin/UsersPage'
import ReportsPage from './pages/admin/ReportsPage'
import SettingsPage from './pages/SettingsPage'
import BookingPage from './pages/client/BookingPage'
import MyAppointmentsPage from './pages/client/MyAppointmentsPage'
import StaffSchedulePage from './pages/staff/StaffSchedulePage'

function App() {
  const { user, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        <Route path="/dashboard" element={<DashboardPage />} />
        <Route path="/appointments" element={<AppointmentsPage />} />
        <Route path="/services" element={<ServicesPage />} />
        <Route path="/settings" element={<SettingsPage />} />
        
        {/* Client Routes */}
        {user.role === 'client' && (
          <>
            <Route path="/book" element={<BookingPage />} />
            <Route path="/my-appointments" element={<MyAppointmentsPage />} />
          </>
        )}
        
        {/* Staff Routes */}
        {user.role === 'staff' && (
          <>
            <Route path="/schedule" element={<StaffSchedulePage />} />
          </>
        )}
        
        {/* Admin Routes */}
        {user.role === 'admin' && (
          <>
            <Route path="/users" element={<UsersPage />} />
            <Route path="/reports" element={<ReportsPage />} />
          </>
        )}
        
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Routes>
    </Layout>
  )
}

export default App
