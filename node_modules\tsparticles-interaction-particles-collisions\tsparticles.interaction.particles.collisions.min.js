/*! For license information please see tsparticles.interaction.particles.collisions.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],o);else{var t="object"==typeof exports?o(require("tsparticles-engine")):o(e.window);for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(this,(e=>(()=>{"use strict";var o={961:o=>{o.exports=e}},t={};function i(e){var s=t[e];if(void 0!==s)return s.exports;var n=t[e]={exports:{}};return o[e](n,n.exports,i),n.exports}i.d=(e,o)=>{for(var t in o)i.o(o,t)&&!i.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},i.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};return(()=>{i.r(s),i.d(s,{loadParticlesCollisionsInteraction:()=>c});var e=i(961);function o(o,t,i,s,n,r){const a=(0,e.clamp)(o.options.collisions.absorb.speed*n.factor/10,0,s);o.size.value+=a/2,i.size.value-=a,s<=r&&(i.size.value=0,i.destroy())}const t=o=>{void 0===o.collisionMaxSpeed&&(o.collisionMaxSpeed=(0,e.getRangeValue)(o.options.collisions.maxSpeed)),o.velocity.length>o.collisionMaxSpeed&&(o.velocity.length=o.collisionMaxSpeed)};function n(o,i){(0,e.circleBounce)((0,e.circleBounceDataFromParticle)(o),(0,e.circleBounceDataFromParticle)(i)),t(o),t(i)}function r(e,t,i,s){switch(e.options.collisions.mode){case"absorb":!function(e,t,i,s){const n=e.getRadius(),r=t.getRadius();void 0===n&&void 0!==r?e.destroy():void 0!==n&&void 0===r?t.destroy():void 0!==n&&void 0!==r&&(n>=r?o(e,0,t,r,i,s):o(t,0,e,n,i,s))}(e,t,i,s);break;case"bounce":n(e,t);break;case"destroy":!function(e,o){e.unbreakable||o.unbreakable||n(e,o),void 0===e.getRadius()&&void 0!==o.getRadius()?e.destroy():void 0!==e.getRadius()&&void 0===o.getRadius()?o.destroy():void 0!==e.getRadius()&&void 0!==o.getRadius()&&(e.getRadius()>=o.getRadius()?o:e).destroy()}(e,t)}}class a extends e.ParticlesInteractorBase{constructor(e){super(e)}clear(){}init(){}async interact(o,t){if(o.destroyed||o.spawning)return;const i=this.container,s=o.getPosition(),n=o.getRadius(),a=i.particles.quadTree.queryCircle(s,2*n);for(const c of a){if(o===c||!c.options.collisions.enable||o.options.collisions.mode!==c.options.collisions.mode||c.destroyed||c.spawning)continue;const a=c.getPosition(),l=c.getRadius();if(Math.abs(Math.round(s.z)-Math.round(a.z))>n+l)continue;(0,e.getDistance)(s,a)>n+l||r(o,c,t,i.retina.pixelRatio)}}isEnabled(e){return e.options.collisions.enable}reset(){}}async function c(e,o=!0){await e.addInteractor("particlesCollisions",(e=>new a(e)),o)}})(),s})()));