import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  X, 
  CheckCircle, 
  AlertTriangle,
  MessageSquare,
  Save
} from 'lucide-react'
import Modal from '../ui/Modal'
import Button from '../ui/Button'
import { useAppointmentStore } from '../../stores/appointmentStore'
import toast from 'react-hot-toast'

export const CancelAppointmentModal = ({ isOpen, onClose, appointment }) => {
  const [reason, setReason] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { cancelAppointment } = useAppointmentStore()

  const handleCancel = async () => {
    if (!reason.trim()) {
      toast.error('Please provide a reason for cancellation')
      return
    }

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      cancelAppointment(appointment.id, reason)
      toast.success('Appointment cancelled successfully')
      onClose()
      setReason('')
    } catch (error) {
      toast.error('Failed to cancel appointment')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Cancel Appointment" size="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="flex items-center space-x-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
          <div>
            <h3 className="font-medium text-red-900 dark:text-red-100">
              Are you sure you want to cancel this appointment?
            </h3>
            <p className="text-sm text-red-700 dark:text-red-300">
              This action cannot be undone.
            </p>
          </div>
        </div>

        {appointment && (
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Appointment Details
            </h4>
            <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <p><strong>Client:</strong> {appointment.client.name}</p>
              <p><strong>Service:</strong> {appointment.service}</p>
              <p><strong>Date:</strong> {appointment.date} at {appointment.time}</p>
              <p><strong>Provider:</strong> {appointment.staff}</p>
            </div>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Reason for cancellation *
          </label>
          <textarea
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                     focus:ring-2 focus:ring-red-500 focus:border-red-500 
                     dark:bg-gray-700 dark:text-gray-100"
            placeholder="Please provide a reason for cancelling this appointment..."
          />
        </div>

        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Keep Appointment
          </Button>
          <Button 
            variant="danger" 
            onClick={handleCancel}
            loading={isLoading}
            icon={<X className="w-4 h-4" />}
          >
            Cancel Appointment
          </Button>
        </div>
      </motion.div>
    </Modal>
  )
}

export const RescheduleAppointmentModal = ({ isOpen, onClose, appointment }) => {
  const [newDate, setNewDate] = useState('')
  const [newTime, setNewTime] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { rescheduleAppointment } = useAppointmentStore()

  const timeSlots = [
    '9:00 AM', '9:30 AM', '10:00 AM', '10:30 AM', '11:00 AM', '11:30 AM',
    '2:00 PM', '2:30 PM', '3:00 PM', '3:30 PM', '4:00 PM', '4:30 PM'
  ]

  const handleReschedule = async () => {
    if (!newDate || !newTime) {
      toast.error('Please select both date and time')
      return
    }

    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      rescheduleAppointment(appointment.id, newDate, newTime)
      toast.success('Appointment rescheduled successfully')
      onClose()
      setNewDate('')
      setNewTime('')
    } catch (error) {
      toast.error('Failed to reschedule appointment')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Reschedule Appointment" size="lg">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {appointment && (
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Current Appointment
            </h4>
            <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
              <p><strong>Client:</strong> {appointment.client.name}</p>
              <p><strong>Current Date:</strong> {appointment.date} at {appointment.time}</p>
              <p><strong>Service:</strong> {appointment.service}</p>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              New Date *
            </label>
            <input
              type="date"
              value={newDate}
              onChange={(e) => setNewDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                       focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                       dark:bg-gray-700 dark:text-gray-100"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              New Time *
            </label>
            <select
              value={newTime}
              onChange={(e) => setNewTime(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                       focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                       dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="">Select time</option>
              {timeSlots.map(time => (
                <option key={time} value={time}>{time}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleReschedule}
            loading={isLoading}
            icon={<Calendar className="w-4 h-4" />}
          >
            Reschedule Appointment
          </Button>
        </div>
      </motion.div>
    </Modal>
  )
}

export const CompleteAppointmentModal = ({ isOpen, onClose, appointment }) => {
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { markAppointmentComplete } = useAppointmentStore()

  const handleComplete = async () => {
    setIsLoading(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      markAppointmentComplete(appointment.id, notes)
      toast.success('Appointment marked as completed')
      onClose()
      setNotes('')
    } catch (error) {
      toast.error('Failed to complete appointment')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Complete Appointment" size="md">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        <div className="flex items-center space-x-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
          <div>
            <h3 className="font-medium text-green-900 dark:text-green-100">
              Mark appointment as completed
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300">
              Add any final notes about the session.
            </p>
          </div>
        </div>

        {appointment && (
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Appointment Details
            </h4>
            <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
              <p><strong>Client:</strong> {appointment.client.name}</p>
              <p><strong>Service:</strong> {appointment.service}</p>
              <p><strong>Date:</strong> {appointment.date} at {appointment.time}</p>
              <p><strong>Duration:</strong> {appointment.duration} minutes</p>
            </div>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Session Notes (Optional)
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                     focus:ring-2 focus:ring-green-500 focus:border-green-500
                     dark:bg-gray-700 dark:text-gray-100"
            placeholder="Add any notes about the session, treatment progress, or follow-up recommendations..."
          />
        </div>

        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            variant="success"
            onClick={handleComplete}
            loading={isLoading}
            icon={<CheckCircle className="w-4 h-4" />}
          >
            Mark Complete
          </Button>
        </div>
      </motion.div>
    </Modal>
  )
}
