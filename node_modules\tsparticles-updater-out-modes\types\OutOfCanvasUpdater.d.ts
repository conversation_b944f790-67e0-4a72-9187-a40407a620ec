import { type Container, type IDelta, type IParticleUpdater, type Particle } from "tsparticles-engine";
import type { IOutModeManager } from "./IOutModeManager";
export declare class OutOfCanvasUpdater implements IParticleUpdater {
    private readonly container;
    updaters: IOutModeManager[];
    constructor(container: Container);
    init(): void;
    isEnabled(particle: Particle): boolean;
    update(particle: Particle, delta: IDelta): void;
    private readonly _updateOutMode;
}
