import { LinkInstance } from "./LinkInstance";
class LinksPlugin {
    constructor() {
        this.id = "links";
    }
    getPlugin(container) {
        return new LinkInstance(container);
    }
    loadOptions() {
    }
    needsPlugin() {
        return true;
    }
}
export async function loadLinksPlugin(engine, refresh = true) {
    const plugin = new LinksPlugin();
    await engine.addPlugin(plugin, refresh);
}
