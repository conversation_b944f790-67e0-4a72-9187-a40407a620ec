"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LifeDelay = void 0;
const tsparticles_engine_1 = require("tsparticles-engine");
class LifeDelay extends tsparticles_engine_1.ValueWithRandom {
    constructor() {
        super();
        this.sync = false;
    }
    load(data) {
        if (!data) {
            return;
        }
        super.load(data);
        if (data.sync !== undefined) {
            this.sync = data.sync;
        }
    }
}
exports.LifeDelay = LifeDelay;
