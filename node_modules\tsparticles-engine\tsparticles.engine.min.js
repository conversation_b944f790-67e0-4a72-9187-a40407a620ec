/*! For license information please see tsparticles.engine.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var s in i)("object"==typeof exports?exports:t)[s]=i[s]}}(this,(()=>(()=>{"use strict";var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{AnimatableColor:()=>Se,AnimationOptions:()=>Te,AnimationValueWithRandom:()=>Ae,Background:()=>le,BackgroundMask:()=>ue,BackgroundMaskCover:()=>de,Circle:()=>xi,ClickEvent:()=>fe,Collisions:()=>Be,CollisionsAbsorb:()=>Ce,CollisionsOverlap:()=>Re,ColorAnimation:()=>Oe,DivEvent:()=>me,Events:()=>_e,ExternalInteractorBase:()=>Fi,FullScreen:()=>pe,HoverEvent:()=>ge,HslAnimation:()=>ke,HslColorManager:()=>Ai,Interactivity:()=>we,ManualParticle:()=>xe,Modes:()=>be,Move:()=>Ne,MoveAngle:()=>He,MoveAttract:()=>We,MoveCenter:()=>Ue,MoveGravity:()=>$e,MovePath:()=>Ge,MoveTrail:()=>Xe,Opacity:()=>Qe,OpacityAnimation:()=>Ze,Options:()=>mi,OptionsColor:()=>ce,OutModes:()=>Ye,Parallax:()=>ve,ParticlesBounce:()=>Ve,ParticlesBounceFactor:()=>Fe,ParticlesDensity:()=>Je,ParticlesInteractorBase:()=>Vi,ParticlesNumber:()=>Ke,ParticlesOptions:()=>ui,Point:()=>_i,Range:()=>bi,RangedAnimationOptions:()=>Ie,RangedAnimationValueWithRandom:()=>Le,Rectangle:()=>wi,ResizeEvent:()=>ye,Responsive:()=>ze,RgbColorManager:()=>Li,Shadow:()=>ti,Shape:()=>ri,Size:()=>ci,SizeAnimation:()=>hi,Spin:()=>je,Stroke:()=>li,Theme:()=>Pe,ThemeDefault:()=>Me,ValueWithRandom:()=>De,Vector:()=>v,Vector3d:()=>m,ZIndex:()=>di,addColorManager:()=>kt,addEasing:()=>_,alterHsl:()=>oe,areBoundsInside:()=>et,arrayRandomIndex:()=>J,calcExactPositionOrRandomFromSize:()=>B,calcExactPositionOrRandomFromSizeRanged:()=>H,calcPositionFromSize:()=>L,calcPositionOrRandomFromSize:()=>F,calcPositionOrRandomFromSizeRanged:()=>V,calculateBounds:()=>it,circleBounce:()=>ct,circleBounceDataFromParticle:()=>ht,clamp:()=>z,clear:()=>Jt,collisionVelocity:()=>A,colorMix:()=>Ut,colorToHsl:()=>It,colorToRgb:()=>Tt,deepExtend:()=>st,divMode:()=>rt,divModeExecute:()=>nt,drawLine:()=>jt,drawParticle:()=>Kt,drawParticlePlugin:()=>se,drawPlugin:()=>ie,drawShape:()=>te,drawShapeAfterEffect:()=>ee,drawTriangle:()=>Nt,errorPrefix:()=>f,executeOnSingleOrMultiple:()=>dt,findItemFromSingleOrMultiple:()=>pt,generatedAttribute:()=>i,getDistance:()=>I,getDistances:()=>T,getEasing:()=>b,getHslAnimationFromHsl:()=>Xt,getHslFromAnimation:()=>qt,getLinkColor:()=>$t,getLinkRandomColor:()=>Gt,getLogger:()=>G,getParticleBaseVelocity:()=>D,getParticleDirectionAngle:()=>E,getPosition:()=>vt,getRandom:()=>x,getRandomRgbColor:()=>Bt,getRangeMax:()=>S,getRangeMin:()=>k,getRangeValue:()=>O,getSize:()=>gt,getStyleFromHsl:()=>Wt,getStyleFromRgb:()=>Ht,getValue:()=>R,hasMatchMedia:()=>Y,hslToRgb:()=>Ft,hslaToRgba:()=>Vt,initParticleNumericAnimationValue:()=>ft,isArray:()=>zt,isBoolean:()=>yt,isDivModeEnabled:()=>ot,isFunction:()=>wt,isInArray:()=>Z,isNumber:()=>bt,isObject:()=>xt,isPointInside:()=>tt,isSsr:()=>X,isString:()=>_t,itemFromArray:()=>K,itemFromSingleOrMultiple:()=>ut,loadFont:()=>Q,loadOptions:()=>pi,loadParticlesOptions:()=>fi,mix:()=>M,mouseDownEvent:()=>s,mouseLeaveEvent:()=>n,mouseMoveEvent:()=>r,mouseOutEvent:()=>a,mouseUpEvent:()=>o,paintBase:()=>Zt,paintImage:()=>Qt,parseAlpha:()=>W,randomInRange:()=>P,rangeColorToHsl:()=>Et,rangeColorToRgb:()=>Rt,rectBounce:()=>lt,resizeEvent:()=>u,rgbToHsl:()=>Dt,safeMatchMedia:()=>j,safeMutationObserver:()=>N,setLogger:()=>$,setRandom:()=>w,setRangeValue:()=>C,singleDivModeExecute:()=>at,stringToAlpha:()=>At,stringToRgb:()=>Lt,touchCancelEvent:()=>d,touchEndEvent:()=>c,touchMoveEvent:()=>l,touchStartEvent:()=>h,tsParticles:()=>Bi,visibilityChangeEvent:()=>p});const i="generated",s="pointerdown",o="pointerup",n="pointerleave",a="pointerout",r="pointermove",h="touchstart",c="touchend",l="touchmove",d="touchcancel",u="resize",p="visibilitychange",f="tsParticles - Error";class m{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!bt(t)&&t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:0}else{if(void 0===t||void 0===e)throw new Error(`${f} Vector3d not initialized correctly`);this.x=t,this.y=e,this.z=i??0}}static get origin(){return m.create(0,0,0)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return m.create(t.x,t.y,t.z)}static create(t,e,i){return new m(t,e,i)}add(t){return m.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return m.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return m.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**2+this.y**2}mult(t){return m.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){const t=this.length;0!=t&&this.multTo(1/t)}rotate(t){return m.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),0)}setTo(t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:0}sub(t){return m.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class v extends m{constructor(t,e){super(t,e,0)}static get origin(){return v.create(0,0)}static clone(t){return v.create(t.x,t.y)}static create(t,e){return new v(t,e)}}let g=Math.random;const y=new Map;function _(t,e){y.get(t)||y.set(t,e)}function b(t){return y.get(t)||(t=>t)}function w(t=Math.random){g=t}function x(){return z(g(),0,1-1e-16)}function z(t,e,i){return Math.min(Math.max(t,e),i)}function M(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function P(t){const e=S(t);let i=k(t);return e===i&&(i=0),x()*(e-i)+i}function O(t){return bt(t)?t:P(t)}function k(t){return bt(t)?t:t.min}function S(t){return bt(t)?t:t.max}function C(t,e){if(t===e||void 0===e&&bt(t))return t;const i=k(t),s=S(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:C(i,s)}function R(t){const e=t.random,{enable:i,minimumValue:s}=yt(e)?{enable:e,minimumValue:0}:e;return O(i?C(t.value,s):t.value)}function T(t,e){const i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function I(t,e){return T(t,e).distance}function E(t,e,i){if(bt(t))return t*Math.PI/180;switch(t){case"top":return-Math.PI/2;case"top-right":return-Math.PI/4;case"right":return 0;case"bottom-right":return Math.PI/4;case"bottom":return Math.PI/2;case"bottom-left":return 3*Math.PI/4;case"left":return Math.PI;case"top-left":return-3*Math.PI/4;case"inside":return Math.atan2(i.y-e.y,i.x-e.x);case"outside":return Math.atan2(e.y-i.y,e.x-i.x);default:return x()*Math.PI*2}}function D(t){const e=v.origin;return e.length=1,e.angle=t,e}function A(t,e,i,s){return v.create(t.x*(i-s)/(i+s)+2*e.x*s/(i+s),t.y)}function L(t){return t.position&&void 0!==t.position.x&&void 0!==t.position.y?{x:t.position.x*t.size.width/100,y:t.position.y*t.size.height/100}:void 0}function F(t){return{x:(t.position?.x??100*x())*t.size.width/100,y:(t.position?.y??100*x())*t.size.height/100}}function V(t){const e={x:void 0!==t.position?.x?O(t.position.x):void 0,y:void 0!==t.position?.y?O(t.position.y):void 0};return F({size:t.size,position:e})}function B(t){return{x:t.position?.x??x()*t.size.width,y:t.position?.y??x()*t.size.height}}function H(t){const e={x:void 0!==t.position?.x?O(t.position.x):void 0,y:void 0!==t.position?.y?O(t.position.y):void 0};return B({size:t.size,position:e})}function W(t){return t?t.endsWith("%")?parseFloat(t)/100:parseFloat(t):1}const U={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function $(t){U.debug=t.debug||U.debug,U.error=t.error||U.error,U.info=t.info||U.info,U.log=t.log||U.log,U.verbose=t.verbose||U.verbose,U.warning=t.warning||U.warning}function G(){return U}function q(t){const e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:o,rectOtherSide:n,velocity:a,factor:r}=t;return s.min<n.min||s.min>n.max||s.max<n.min||s.max>n.max||(i.max>=o.min&&i.max<=(o.max+o.min)/2&&a>0||i.min<=o.max&&i.min>(o.max+o.min)/2&&a<0)&&(e.velocity=a*-r,e.bounced=!0),e}function X(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function Y(){return!X()&&"undefined"!=typeof matchMedia}function j(t){if(Y())return matchMedia(t)}function N(t){if(!X()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}function Z(t,e){return t===e||zt(e)&&e.indexOf(t)>-1}async function Q(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function J(t){return Math.floor(x()*t.length)}function K(t,e,i=!0){return t[void 0!==e&&i?e%t.length:J(t)]}function tt(t,e,i,s,o){return et(it(t,s??0),e,i,o)}function et(t,e,i,s){let o=!0;return s&&"bottom"!==s||(o=t.top<e.height+i.x),!o||s&&"left"!==s||(o=t.right>i.x),!o||s&&"right"!==s||(o=t.left<e.width+i.y),!o||s&&"top"!==s||(o=t.bottom>i.y),o}function it(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function st(t,...e){for(const i of e){if(null==i)continue;if(!xt(i)){t=i;continue}const e=Array.isArray(i);!e||!xt(t)&&t&&Array.isArray(t)?e||!xt(t)&&t&&!Array.isArray(t)||(t={}):t=[];for(const e in i){if("__proto__"===e)continue;const s=i[e],o=t;o[e]=xt(s)&&Array.isArray(s)?s.map((t=>st(o[e],t))):st(o[e],s)}}return t}function ot(t,e){return!!pt(e,(e=>e.enable&&Z(t,e.mode)))}function nt(t,e,i){dt(e,(e=>{const s=e.mode;e.enable&&Z(t,s)&&at(e,i)}))}function at(t,e){dt(t.selectors,(i=>{e(i,t)}))}function rt(t,e){if(e&&t)return pt(t,(t=>function(t,e){const i=dt(e,(e=>t.matches(e)));return zt(i)?i.some((t=>t)):i}(e,t.selectors)))}function ht(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:v.create(R(t.options.bounce.horizontal),R(t.options.bounce.vertical))}}function ct(t,e){const{x:i,y:s}=t.velocity.sub(e.velocity),[o,n]=[t.position,e.position],{dx:a,dy:r}=T(n,o);if(i*a+s*r<0)return;const h=-Math.atan2(r,a),c=t.mass,l=e.mass,d=t.velocity.rotate(h),u=e.velocity.rotate(h),p=A(d,u,c,l),f=A(u,d,c,l),m=p.rotate(-h),v=f.rotate(-h);t.velocity.x=m.x*t.factor.x,t.velocity.y=m.y*t.factor.y,e.velocity.x=v.x*e.factor.x,e.velocity.y=v.y*e.factor.y}function lt(t,e){const i=it(t.getPosition(),t.getRadius()),s=q({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:R(t.options.bounce.horizontal)});s.bounced&&(void 0!==s.velocity&&(t.velocity.x=s.velocity),void 0!==s.position&&(t.position.x=s.position));const o=q({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:R(t.options.bounce.vertical)});o.bounced&&(void 0!==o.velocity&&(t.velocity.y=o.velocity),void 0!==o.position&&(t.position.y=o.position))}function dt(t,e){return zt(t)?t.map(((t,i)=>e(t,i))):e(t,0)}function ut(t,e,i){return zt(t)?K(t,e,i):t}function pt(t,e){return zt(t)?t.find(((t,i)=>e(t,i))):e(t,0)?t:void 0}function ft(t,e){const i=t.value,s=t.animation,o={delayTime:1e3*O(s.delay),enable:s.enable,value:O(t.value)*e,max:S(i)*e,min:k(i)*e,loops:0,maxLoops:O(s.count),time:0};if(s.enable){switch(o.decay=1-O(s.decay),s.mode){case"increase":o.status="increasing";break;case"decrease":o.status="decreasing";break;case"random":o.status=x()>=.5?"increasing":"decreasing"}const t="auto"===s.mode;switch(s.startValue){case"min":o.value=o.min,t&&(o.status="increasing");break;case"max":o.value=o.max,t&&(o.status="decreasing");break;default:o.value=P(o),t&&(o.status=x()>=.5?"increasing":"decreasing")}}return o.initialValue=o.value,o}function mt(t,e){if(!("percent"===t.mode)){const{mode:e,...i}=t;return i}return"x"in t?{x:t.x/100*e.width,y:t.y/100*e.height}:{width:t.width/100*e.width,height:t.height/100*e.height}}function vt(t,e){return mt(t,e)}function gt(t,e){return mt(t,e)}function yt(t){return"boolean"==typeof t}function _t(t){return"string"==typeof t}function bt(t){return"number"==typeof t}function wt(t){return"function"==typeof t}function xt(t){return"object"==typeof t&&null!==t}function zt(t){return Array.isArray(t)}const Mt="random",Pt="mid",Ot=new Map;function kt(t){Ot.set(t.key,t)}function St(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function Ct(t){for(const[,e]of Ot)if(t.startsWith(e.stringPrefix))return e.parseString(t);const e=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,((t,e,i,s,o)=>e+e+i+i+s+s+(void 0!==o?o+o:""))),i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(e);return i?{a:void 0!==i[4]?parseInt(i[4],16)/255:1,b:parseInt(i[3],16),g:parseInt(i[2],16),r:parseInt(i[1],16)}:void 0}function Rt(t,e,i=!0){if(!t)return;const s=_t(t)?{value:t}:t;if(_t(s.value))return Tt(s.value,e,i);if(zt(s.value))return Rt({value:K(s.value,e,i)});for(const[,t]of Ot){const e=t.handleRangeColor(s);if(e)return e}}function Tt(t,e,i=!0){if(!t)return;const s=_t(t)?{value:t}:t;if(_t(s.value))return s.value===Mt?Bt():Lt(s.value);if(zt(s.value))return Tt({value:K(s.value,e,i)});for(const[,t]of Ot){const e=t.handleColor(s);if(e)return e}}function It(t,e,i=!0){const s=Tt(t,e,i);return s?Dt(s):void 0}function Et(t,e,i=!0){const s=Rt(t,e,i);return s?Dt(s):void 0}function Dt(t){const e=t.r/255,i=t.g/255,s=t.b/255,o=Math.max(e,i,s),n=Math.min(e,i,s),a={h:0,l:(o+n)/2,s:0};return o!==n&&(a.s=a.l<.5?(o-n)/(o+n):(o-n)/(2-o-n),a.h=e===o?(i-s)/(o-n):a.h=i===o?2+(s-e)/(o-n):4+(e-i)/(o-n)),a.l*=100,a.s*=100,a.h*=60,a.h<0&&(a.h+=360),a.h>=360&&(a.h-=360),a}function At(t){return Ct(t)?.a}function Lt(t){return Ct(t)}function Ft(t){const e={b:0,g:0,r:0},i={h:t.h/360,l:t.l/100,s:t.s/100};if(i.s){const t=i.l<.5?i.l*(1+i.s):i.l+i.s-i.l*i.s,s=2*i.l-t;e.r=St(s,t,i.h+1/3),e.g=St(s,t,i.h),e.b=St(s,t,i.h-1/3)}else e.r=e.g=e.b=i.l;return e.r=Math.floor(255*e.r),e.g=Math.floor(255*e.g),e.b=Math.floor(255*e.b),e}function Vt(t){const e=Ft(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}function Bt(t){const e=t??0;return{b:Math.floor(P(C(e,256))),g:Math.floor(P(C(e,256))),r:Math.floor(P(C(e,256)))}}function Ht(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??1})`}function Wt(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??1})`}function Ut(t,e,i,s){let o=t,n=e;return void 0===o.r&&(o=Ft(t)),void 0===n.r&&(n=Ft(e)),{b:M(o.b,n.b,i,s),g:M(o.g,n.g,i,s),r:M(o.r,n.r,i,s)}}function $t(t,e,i){if(i===Mt)return Bt();if(i!==Pt)return i;{const i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return Ut(i,s,t.getRadius(),e.getRadius());{const t=i??s;if(t)return Ft(t)}}}function Gt(t,e,i){const s=_t(t)?t:t.value;return s===Mt?i?Rt({value:s}):e?Mt:Pt:s===Pt?Pt:Rt({value:s})}function qt(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function Xt(t,e,i){const s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(Yt(s.h,e.h,i),Yt(s.s,e.s,i),Yt(s.l,e.l,i)),s}function Yt(t,e,i){t.enable=e.enable,t.enable?(t.velocity=O(e.speed)/100*i,t.decay=1-O(e.decay),t.status="increasing",t.loops=0,t.maxLoops=O(e.count),t.time=0,t.delayTime=1e3*O(e.delay),e.sync||(t.velocity*=x(),t.value*=x()),t.initialValue=t.value):t.velocity=0}function jt(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function Nt(t,e,i,s){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.lineTo(s.x,s.y),t.closePath()}function Zt(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(0,0,e.width,e.height)}function Qt(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,0,0,e.width,e.height),t.globalAlpha=1)}function Jt(t,e){t.clearRect(0,0,e.width,e.height)}function Kt(t){const{container:e,context:i,particle:s,delta:o,colorStyles:n,backgroundMask:a,composite:r,radius:h,opacity:c,shadow:l,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:0),f=Math.sin(p),m=Math.cos(p),v={a:m*(d.a??1),b:f*(d.b??1),c:-f*(d.c??1),d:m*(d.d??1)};i.setTransform(v.a,v.b,v.c,v.d,u.x,u.y),i.beginPath(),a&&(i.globalCompositeOperation=r);const g=s.shadowColor;l.enable&&g&&(i.shadowBlur=l.blur,i.shadowColor=Ht(g),i.shadowOffsetX=l.offset.x,i.shadowOffsetY=l.offset.y),n.fill&&(i.fillStyle=n.fill);const y=s.strokeWidth??0;i.lineWidth=y,n.stroke&&(i.strokeStyle=n.stroke),te(e,i,s,h,c,o),y>0&&i.stroke(),s.close&&i.closePath(),s.fill&&i.fill(),ee(e,i,s,h,c,o),i.globalCompositeOperation="source-over",i.setTransform(1,0,0,1,0,0)}function te(t,e,i,s,o,n){if(!i.shape)return;const a=t.drawers.get(i.shape);a&&a.draw(e,i,s,o,n,t.retina.pixelRatio)}function ee(t,e,i,s,o,n){if(!i.shape)return;const a=t.drawers.get(i.shape);a&&a.afterEffect&&a.afterEffect(e,i,s,o,n,t.retina.pixelRatio)}function ie(t,e,i){e.draw&&e.draw(t,i)}function se(t,e,i,s){e.drawParticle&&e.drawParticle(t,i,s)}function oe(t,e,i){return{h:t.h,s:t.s,l:t.l+("darken"===e?-1:1)*i}}function ne(t,e,i){const s=e[i];void 0!==s&&(t[i]=(t[i]??1)*s)}class ae{constructor(t){this.container=t,this._applyPostDrawUpdaters=t=>{for(const e of this._postDrawUpdaters)e.afterDraw&&e.afterDraw(t)},this._applyPreDrawUpdaters=(t,e,i,s,o,n)=>{for(const a of this._preDrawUpdaters){if(a.getColorStyles){const{fill:n,stroke:r}=a.getColorStyles(e,t,i,s);n&&(o.fill=n),r&&(o.stroke=r)}if(a.getTransformValues){const t=a.getTransformValues(e);for(const e in t)ne(n,t,e)}a.beforeDraw&&a.beforeDraw(e)}},this._applyResizePlugins=()=>{for(const t of this._resizePlugins)t.resize&&t.resize()},this._getPluginParticleColors=t=>{let e,i;for(const s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=Et(s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=Et(s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=()=>{const t=this.container.actualOptions.backgroundMask.cover,e=Rt(t.color);if(e){const i={...e,a:t.opacity};this._coverColorStyle=Ht(i,i.a)}},this._initStyle=()=>{const t=this.element,e=this.container.actualOptions;if(t){this._fullScreen?(this._originalStyle=st({},t.style),this._setFullScreenStyle()):this._resetOriginalStyle();for(const i in e.style){if(!i||!e.style)continue;const s=e.style[i];s&&t.style.setProperty(i,s,"important")}}},this._initTrail=async()=>{const t=this.container.actualOptions,e=t.particles.move.trail,i=e.fill;if(e.enable)if(i.color){const e=Rt(i.color);if(!e)return;const s=t.particles.move.trail;this._trailFill={color:{...e},opacity:1/s.length}}else await new Promise(((t,s)=>{if(!i.image)return;const o=document.createElement("img");o.addEventListener("load",(()=>{this._trailFill={image:o,opacity:1/e.length},t()})),o.addEventListener("error",(t=>{s(t.error)})),o.src=i.image}))},this._paintBase=t=>{this.draw((e=>Zt(e,this.size,t)))},this._paintImage=(t,e)=>{this.draw((i=>Qt(i,this.size,t,e)))},this._repairStyle=()=>{const t=this.element;t&&(this._safeMutationObserver((t=>t.disconnect())),this._initStyle(),this.initBackground(),this._safeMutationObserver((e=>e.observe(t,{attributes:!0}))))},this._resetOriginalStyle=()=>{const t=this.element,e=this._originalStyle;if(!t||!e)return;const i=t.style;i.position=e.position,i.zIndex=e.zIndex,i.top=e.top,i.left=e.left,i.width=e.width,i.height=e.height},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{const t=this.element;if(!t)return;const e="important",i=t.style;i.setProperty("position","fixed",e),i.setProperty("z-index",this.container.actualOptions.fullScreen.zIndex.toString(10),e),i.setProperty("top","0",e),i.setProperty("left","0",e),i.setProperty("width","100%",e),i.setProperty("height","100%",e)},this.size={height:0,width:0},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){const t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>0&&i?i.color?this._paintBase(Ht(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):this.draw((t=>{Jt(t,this.size)}))}destroy(){if(this.stop(),this._generated){const t=this.element;t&&t.remove()}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){const e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;const i=t.getRadius();if(i<=0)return;const s=t.getFillColor(),o=t.getStrokeColor()??s;let[n,a]=this._getPluginParticleColors(t);n||(n=s),a||(a=o),(n||a)&&this.draw((s=>{const o=this.container,r=o.actualOptions,h=t.options.zIndex,c=(1-t.zIndexFactor)**h.opacityRate,l=t.bubble.opacity??t.opacity?.value??1,d=l*c,u=(t.strokeOpacity??l)*c,p={},f={fill:n?Wt(n,d):void 0};f.stroke=a?Wt(a,u):f.fill,this._applyPreDrawUpdaters(s,t,i,d,f,p),Kt({container:o,context:s,particle:t,delta:e,colorStyles:f,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*(1-t.zIndexFactor)**h.sizeRate,opacity:d,shadow:t.options.shadow,transform:p}),this._applyPostDrawUpdaters(t)}))}drawParticlePlugin(t,e,i){this.draw((s=>se(s,t,e,i)))}drawPlugin(t,e){this.draw((i=>ie(i,t,e)))}async init(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=N((t=>{for(const e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()})),this.resize(),this._initStyle(),this._initCover();try{await this._initTrail()}catch(t){G().error(t)}this.initBackground(),this._safeMutationObserver((t=>{this.element&&t.observe(this.element,{attributes:!0})})),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){const t=this.container.actualOptions.background,e=this.element;if(!e)return;const i=e.style;if(i){if(t.color){const e=Rt(t.color);i.backgroundColor=e?Ht(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){this._resizePlugins=[];for(const[,t]of this.container.plugins)t.resize&&this._resizePlugins.push(t),(t.particleFillColor||t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){this._preDrawUpdaters=[],this._postDrawUpdaters=[];for(const t of this.container.particles.updaters)t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles||t.getTransformValues||t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&i in t.dataset?"true"===t.dataset[i]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=st({},this.element.style),this.size.height=t.offsetHeight,this.size.width=t.offsetWidth,this._context=this.element.getContext("2d"),this._safeMutationObserver((t=>{this.element&&t.observe(this.element,{attributes:!0})})),this.container.retina.init(),this.initBackground()}paint(){const t=this.container.actualOptions;this.draw((e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(Jt(e,this.size),this._paintBase(this._coverColorStyle)):this._paintBase()}))}resize(){if(!this.element)return!1;const t=this.container,e=t.retina.pixelRatio,i=t.canvas.size,s=this.element.offsetWidth*e,o=this.element.offsetHeight*e;if(o===i.height&&s===i.width&&o===this.element.height&&s===this.element.width)return!1;const n={...i};return this.element.width=i.width=this.element.offsetWidth*e,this.element.height=i.height=this.element.offsetHeight*e,this.container.started&&(this.resizeFactor={width:i.width/n.width,height:i.height/n.height}),!0}stop(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=void 0,this.draw((t=>Jt(t,this.size)))}async windowResize(){if(!this.element||!this.resize())return;const t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function re(t,e,i,s,o){if(s){let s={passive:!0};yt(o)?s.capture=o:void 0!==o&&(s=o),t.addEventListener(e,i,s)}else{const s=o;t.removeEventListener(e,i,s)}}class he{constructor(t){this.container=t,this._doMouseTouchClick=t=>{const e=this.container,i=e.actualOptions;if(this._canPush){const t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=(new Date).getTime();dt(i.interactivity.events.onClick.mode,(t=>this.container.handleClickMode(t)))}"touchend"===t.type&&setTimeout((()=>this._mouseTouchFinish()),500)},this._handleThemeChange=t=>{const e=t,i=this.container,s=i.options,o=s.defaultThemes,n=e.matches?o.dark:o.light,a=s.themes.find((t=>t.name===n));a&&a.default.auto&&i.loadTheme(n)},this._handleVisibilityChange=()=>{const t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document&&document.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.getAnimationStatus()?t.play(!0):t.draw(!0)))},this._handleWindowResize=async()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout),this._resizeTimeout=setTimeout((async()=>{const t=this.container.canvas;t&&await t.windowResize()}),1e3*this.container.actualOptions.interactivity.events.resize.delay)},this._manageInteractivityListeners=(t,e)=>{const i=this._handlers,n=this.container,a=n.actualOptions,u=n.interactivity.element;if(!u)return;const p=u,f=n.canvas.element;f&&(f.style.pointerEvents=p===f?"initial":"none"),(a.interactivity.events.onHover.enable||a.interactivity.events.onClick.enable)&&(re(u,r,i.mouseMove,e),re(u,h,i.touchStart,e),re(u,l,i.touchMove,e),a.interactivity.events.onClick.enable?(re(u,c,i.touchEndClick,e),re(u,o,i.mouseUp,e),re(u,s,i.mouseDown,e)):re(u,c,i.touchEnd,e),re(u,t,i.mouseLeave,e),re(u,d,i.touchCancel,e))},this._manageListeners=t=>{const e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,o=i.canvas.element;let r=n;"window"===s?(i.interactivity.element=window,r=a):i.interactivity.element="parent"===s&&o?o.parentElement??o.parentNode:o,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(r,t),document&&re(document,p,e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{const e=this._handlers,i=j("(prefers-color-scheme: dark)");i&&(void 0===i.addEventListener?void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange)):re(i,"change",e.themeChange,t))},this._manageResize=t=>{const e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver)return void re(window,u,e.resize,t);const s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver((async t=>{t.find((t=>t.target===s))&&await this._handleWindowResize()})),this._resizeObserver.observe(s))},this._mouseDown=()=>{const{interactivity:t}=this.container;if(!t)return;const{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{const e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let o=!1;const n=s.position;if(n&&i.interactivity.events.onClick.enable){for(const[,t]of e.plugins)if(t.clickPositionValid&&(o=t.clickPositionValid(n),o))break;o||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{const t=this.container.interactivity;if(!t)return;const e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=n,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{const e=this.container,i=e.actualOptions,s=e.interactivity,o=e.canvas.element;if(!s||!s.element)return;let n;if(s.mouse.inside=!0,t.type.startsWith("pointer")){this._canPush=!0;const e=t;if(s.element===window){if(o){const t=o.getBoundingClientRect();n={x:e.clientX-t.left,y:e.clientY-t.top}}}else if("parent"===i.interactivity.detectsOn){const t=e.target,i=e.currentTarget;if(t&&i&&o){const s=t.getBoundingClientRect(),a=i.getBoundingClientRect(),r=o.getBoundingClientRect();n={x:e.offsetX+2*s.left-(a.left+r.left),y:e.offsetY+2*s.top-(a.top+r.top)}}else n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY}}else e.target===o&&(n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY})}else if(this._canPush="touchmove"!==t.type,o){const e=t,i=e.touches[e.touches.length-1],s=o.getBoundingClientRect();n={x:i.clientX-(s.left??0),y:i.clientY-(s.top??0)}}const a=e.retina.pixelRatio;n&&(n.x*=a,n.y*=a),s.mouse.position=n,s.status=r},this._touchEnd=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.set(t.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}class ce{constructor(){this.value=""}static create(t,e){const i=new ce;return i.load(t),void 0!==e&&(_t(e)||zt(e)?i.load({value:e}):i.load(e)),i}load(t){void 0!==t?.value&&(this.value=t.value)}}class le{constructor(){this.color=new ce,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){t&&(void 0!==t.color&&(this.color=ce.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0!==t.opacity&&(this.opacity=t.opacity))}}class de{constructor(){this.color=new ce,this.color.value="#fff",this.opacity=1}load(t){t&&(void 0!==t.color&&(this.color=ce.create(this.color,t.color)),void 0!==t.opacity&&(this.opacity=t.opacity))}}class ue{constructor(){this.composite="destination-out",this.cover=new de,this.enable=!1}load(t){if(t){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){const e=t.cover,i=_t(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class pe{constructor(){this.enable=!0,this.zIndex=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.zIndex&&(this.zIndex=t.zIndex))}}class fe{constructor(){this.enable=!1,this.mode=[]}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}class me{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type="circle"}get el(){return this.elementId}set el(t){this.elementId=t}get elementId(){return this.ids}set elementId(t){this.ids=t}get ids(){return dt(this.selectors,(t=>t.replace("#","")))}set ids(t){this.selectors=dt(t,(t=>`#${t}`))}load(t){if(!t)return;const e=t.ids??t.elementId??t.el;void 0!==e&&(this.ids=e),void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.type&&(this.type=t.type)}}class ve{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0!==t.smooth&&(this.smooth=t.smooth))}}class ge{constructor(){this.enable=!1,this.mode=[],this.parallax=new ve}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class ye{constructor(){this.delay=.5,this.enable=!0}load(t){void 0!==t&&(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.enable&&(this.enable=t.enable))}}class _e{constructor(){this.onClick=new fe,this.onDiv=new me,this.onHover=new ge,this.resize=new ye}get onclick(){return this.onClick}set onclick(t){this.onClick=t}get ondiv(){return this.onDiv}set ondiv(t){this.onDiv=t}get onhover(){return this.onHover}set onhover(t){this.onHover=t}load(t){if(!t)return;this.onClick.load(t.onClick??t.onclick);const e=t.onDiv??t.ondiv;void 0!==e&&(this.onDiv=dt(e,(t=>{const e=new me;return e.load(t),e}))),this.onHover.load(t.onHover??t.onhover),yt(t.resize)?this.resize.enable=t.resize:this.resize.load(t.resize)}}class be{constructor(t,e){this._engine=t,this._container=e}load(t){if(!t)return;if(!this._container)return;const e=this._engine.plugins.interactors.get(this._container);if(e)for(const i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class we{constructor(t,e){this.detectsOn="window",this.events=new _e,this.modes=new be(t,e)}get detect_on(){return this.detectsOn}set detect_on(t){this.detectsOn=t}load(t){if(!t)return;const e=t.detectsOn??t.detect_on;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class xe{load(t){t&&(t.position&&(this.position={x:t.position.x??50,y:t.position.y??50,mode:t.position.mode??"percent"}),t.options&&(this.options=st({},t.options)))}}class ze{constructor(){this.maxWidth=1/0,this.options={},this.mode="canvas"}load(t){t&&(void 0!==t.maxWidth&&(this.maxWidth=t.maxWidth),void 0!==t.mode&&("screen"===t.mode?this.mode="screen":this.mode="canvas"),void 0!==t.options&&(this.options=st({},t.options)))}}class Me{constructor(){this.auto=!1,this.mode="any",this.value=!1}load(t){t&&(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class Pe{constructor(){this.name="",this.default=new Me}load(t){t&&(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=st({},t.options)))}}class Oe{constructor(){this.count=0,this.enable=!1,this.offset=0,this.speed=1,this.delay=0,this.decay=0,this.sync=!0}load(t){t&&(void 0!==t.count&&(this.count=C(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(this.offset=C(t.offset)),void 0!==t.speed&&(this.speed=C(t.speed)),void 0!==t.decay&&(this.decay=C(t.decay)),void 0!==t.delay&&(this.delay=C(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class ke{constructor(){this.h=new Oe,this.s=new Oe,this.l=new Oe}load(t){t&&(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class Se extends ce{constructor(){super(),this.animation=new ke}static create(t,e){const i=new Se;return i.load(t),void 0!==e&&(_t(e)||zt(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),!t)return;const e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}class Ce{constructor(){this.speed=2}load(t){t&&void 0!==t.speed&&(this.speed=t.speed)}}class Re{constructor(){this.enable=!0,this.retries=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.retries&&(this.retries=t.retries))}}class Te{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){t&&(void 0!==t.count&&(this.count=C(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=C(t.speed)),void 0!==t.decay&&(this.decay=C(t.decay)),void 0!==t.delay&&(this.delay=C(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class Ie extends Te{constructor(){super(),this.mode="auto",this.startValue="random"}load(t){super.load(t),t&&(void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.startValue&&(this.startValue=t.startValue))}}class Ee{constructor(){this.enable=!1,this.minimumValue=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue))}}class De{constructor(){this.random=new Ee,this.value=0}load(t){t&&(yt(t.random)?this.random.enable=t.random:this.random.load(t.random),void 0!==t.value&&(this.value=C(t.value,this.random.enable?this.random.minimumValue:void 0)))}}class Ae extends De{constructor(){super(),this.animation=new Te}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(super.load(t),!t)return;const e=t.animation??t.anim;void 0!==e&&this.animation.load(e)}}class Le extends Ae{constructor(){super(),this.animation=new Ie}load(t){if(super.load(t),!t)return;void 0!==(t.animation??t.anim)&&(this.value=C(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class Fe extends De{constructor(){super(),this.random.minimumValue=.1,this.value=1}}class Ve{constructor(){this.horizontal=new Fe,this.vertical=new Fe}load(t){t&&(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class Be{constructor(){this.absorb=new Ce,this.bounce=new Ve,this.enable=!1,this.maxSpeed=50,this.mode="bounce",this.overlap=new Re}load(t){t&&(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=C(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class He{constructor(){this.offset=0,this.value=90}load(t){t&&(void 0!==t.offset&&(this.offset=C(t.offset)),void 0!==t.value&&(this.value=C(t.value)))}}class We{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}get rotateX(){return this.rotate.x}set rotateX(t){this.rotate.x=t}get rotateY(){return this.rotate.y}set rotateY(t){this.rotate.y=t}load(t){if(!t)return;void 0!==t.distance&&(this.distance=C(t.distance)),void 0!==t.enable&&(this.enable=t.enable);const e=t.rotate?.x??t.rotateX;void 0!==e&&(this.rotate.x=e);const i=t.rotate?.y??t.rotateY;void 0!==i&&(this.rotate.y=i)}}class Ue{constructor(){this.x=50,this.y=50,this.mode="percent",this.radius=0}load(t){t&&(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.radius&&(this.radius=t.radius))}}class $e{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=C(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=C(t.maxSpeed)))}}class Ge{constructor(){this.clamp=!0,this.delay=new De,this.enable=!1,this.options={}}load(t){t&&(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=st(this.options,t.options)))}}class qe{load(t){t&&(void 0!==t.color&&(this.color=ce.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image))}}class Xe{constructor(){this.enable=!1,this.length=10,this.fill=new qe}get fillColor(){return this.fill.color}set fillColor(t){this.fill.load({color:t})}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0===t.fill&&void 0===t.fillColor||this.fill.load(t.fill||{color:t.fillColor}),void 0!==t.length&&(this.length=t.length))}}class Ye{constructor(){this.default="out"}load(t){t&&(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class je{constructor(){this.acceleration=0,this.enable=!1}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=C(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=st({},t.position)))}}class Ne{constructor(){this.angle=new He,this.attract=new We,this.center=new Ue,this.decay=0,this.distance={},this.direction="none",this.drift=0,this.enable=!1,this.gravity=new $e,this.path=new Ge,this.outModes=new Ye,this.random=!1,this.size=!1,this.speed=2,this.spin=new je,this.straight=!1,this.trail=new Xe,this.vibrate=!1,this.warp=!1}get bounce(){return this.collisions}set bounce(t){this.collisions=t}get collisions(){return!1}set collisions(t){}get noise(){return this.path}set noise(t){this.path=t}get outMode(){return this.outModes.default}set outMode(t){this.outModes.default=t}get out_mode(){return this.outMode}set out_mode(t){this.outMode=t}load(t){if(!t)return;this.angle.load(bt(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=C(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=bt(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=C(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);const e=t.outModes??t.outMode??t.out_mode;void 0!==e&&(xt(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path??t.noise),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=C(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class Ze extends Ie{constructor(){super(),this.destroy="none",this.speed=2}get opacity_min(){return this.minimumValue}set opacity_min(t){this.minimumValue=t}load(t){void 0!==t?.opacity_min&&void 0===t.minimumValue&&(t.minimumValue=t.opacity_min),super.load(t),t&&void 0!==t.destroy&&(this.destroy=t.destroy)}}class Qe extends De{constructor(){super(),this.animation=new Ze,this.random.minimumValue=.1,this.value=1}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(!t)return;super.load(t);const e=t.animation??t.anim;void 0!==e&&(this.animation.load(e),this.value=C(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class Je{constructor(){this.enable=!1,this.width=1920,this.height=1080}get area(){return this.width}set area(t){this.width=t}get factor(){return this.height}set factor(t){this.height=t}get value_area(){return this.area}set value_area(t){this.area=t}load(t){if(!t)return;void 0!==t.enable&&(this.enable=t.enable);const e=t.width??t.area??t.value_area;void 0!==e&&(this.width=e);const i=t.height??t.factor;void 0!==i&&(this.height=i)}}class Ke{constructor(){this.density=new Je,this.limit=0,this.value=0}get max(){return this.limit}set max(t){this.limit=t}load(t){if(!t)return;this.density.load(t.density);const e=t.limit??t.max;void 0!==e&&(this.limit=e),void 0!==t.value&&(this.value=t.value)}}class ti{constructor(){this.blur=0,this.color=new ce,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=ce.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}const ei="character",ii="char",si="image",oi="images",ni="polygon",ai="star";class ri{constructor(){this.loadShape=(t,e,i,s)=>{if(!t)return;const o=zt(t),n=o?[]:{},a=o!==zt(this.options[e]),r=o!==zt(this.options[i]);a&&(this.options[e]=n),r&&s&&(this.options[i]=n),this.options[e]=st(this.options[e]??n,t),this.options[i]&&!s||(this.options[i]=st(this.options[i]??n,t))},this.close=!0,this.fill=!0,this.options={},this.type="circle"}get character(){return this.options[ei]??this.options[ii]}set character(t){this.options[ii]=this.options[ei]=t}get custom(){return this.options}set custom(t){this.options=t}get image(){return this.options[si]??this.options[oi]}set image(t){this.options[oi]=this.options[si]=t}get images(){return this.image}set images(t){this.image=t}get polygon(){return this.options[ni]??this.options[ai]}set polygon(t){this.options[ai]=this.options[ni]=t}get stroke(){return[]}set stroke(t){}load(t){if(!t)return;const e=t.options??t.custom;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=st(this.options[t]??{},i))}this.loadShape(t.character,ei,ii,!0),this.loadShape(t.polygon,ni,ai,!1),this.loadShape(t.image??t.images,si,oi,!0),void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class hi extends Ie{constructor(){super(),this.destroy="none",this.speed=5}get size_min(){return this.minimumValue}set size_min(t){this.minimumValue=t}load(t){void 0!==t?.size_min&&void 0===t.minimumValue&&(t.minimumValue=t.size_min),super.load(t),t&&void 0!==t.destroy&&(this.destroy=t.destroy)}}class ci extends De{constructor(){super(),this.animation=new hi,this.random.minimumValue=1,this.value=3}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(super.load(t),!t)return;const e=t.animation??t.anim;void 0!==e&&(this.animation.load(e),this.value=C(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class li{constructor(){this.width=0}load(t){t&&(void 0!==t.color&&(this.color=Se.create(this.color,t.color)),void 0!==t.width&&(this.width=C(t.width)),void 0!==t.opacity&&(this.opacity=C(t.opacity)))}}class di extends De{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),t&&(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0!==t.velocityRate&&(this.velocityRate=t.velocityRate))}}class ui{constructor(t,e){this._engine=t,this._container=e,this.bounce=new Ve,this.collisions=new Be,this.color=new Se,this.color.value="#fff",this.groups={},this.move=new Ne,this.number=new Ke,this.opacity=new Qe,this.reduceDuplicates=!1,this.shadow=new ti,this.shape=new ri,this.size=new ci,this.stroke=new li,this.zIndex=new di}load(t){if(!t)return;if(this.bounce.load(t.bounce),this.color.load(Se.create(this.color,t.color)),void 0!==t.groups)for(const e in t.groups){const i=t.groups[e];void 0!==i&&(this.groups[e]=st(this.groups[e]??{},i))}this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex);const e=t.move?.collisions??t.move?.bounce;void 0!==e&&(this.collisions.enable=e),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=st({},t.interactivity));const i=t.stroke??t.shape?.stroke;if(i&&(this.stroke=dt(i,(t=>{const e=new li;return e.load(t),e}))),this._container){const e=this._engine.plugins.updaters.get(this._container);if(e)for(const i of e)i.loadOptions&&i.loadOptions(this,t);const i=this._engine.plugins.interactors.get(this._container);if(i)for(const e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function pi(t,...e){for(const i of e)t.load(i)}function fi(t,e,...i){const s=new ui(t,e);return pi(s,...i),s}class mi{constructor(t,e){this._findDefaultTheme=t=>this.themes.find((e=>e.default.value&&e.default.mode===t))??this.themes.find((t=>t.default.value&&"any"===t.default.mode)),this._importPreset=t=>{this.load(this._engine.plugins.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new le,this.backgroundMask=new ue,this.defaultThemes={},this.delay=0,this.fullScreen=new pe,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new we(t,e),this.manualParticles=[],this.particles=fi(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}get backgroundMode(){return this.fullScreen}set backgroundMode(t){this.fullScreen.load(t)}get fps_limit(){return this.fpsLimit}set fps_limit(t){this.fpsLimit=t}get retina_detect(){return this.detectRetina}set retina_detect(t){this.detectRetina=t}load(t){if(!t)return;void 0!==t.preset&&dt(t.preset,(t=>this._importPreset(t))),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.delay&&(this.delay=C(t.delay));const e=t.detectRetina??t.retina_detect;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=C(t.duration));const i=t.fpsLimit??t.fps_limit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);const s=t.fullScreen??t.backgroundMode;yt(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map((t=>{const e=new xe;return e.load(t),e}))),this.particles.load(t.particles),this.style=st(this.style,t.style),this._engine.plugins.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);const o=this._engine.plugins.interactors.get(this._container);if(o)for(const e of o)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(const e of t.responsive){const t=new ze;t.load(e),this.responsive.push(t)}if(this.responsive.sort(((t,e)=>t.maxWidth-e.maxWidth)),void 0!==t.themes)for(const e of t.themes){const t=this.themes.find((t=>t.name===e.name));if(t)t.load(e);else{const t=new Pe;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme("dark")?.name,this.defaultThemes.light=this._findDefaultTheme("light")?.name}setResponsive(t,e,i){this.load(i);const s=this.responsive.find((i=>"screen"===i.mode&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t));return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){const e=this.themes.find((e=>e.name===t));e&&this.load(e.options)}else{const t=j("(prefers-color-scheme: dark)"),e=t&&t.matches,i=this._findDefaultTheme(e?"dark":"light");i&&this.load(i.options)}}}class vi{constructor(t,e){this.container=e,this._engine=t,this._interactors=t.plugins.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[]}async externalInteract(t){for(const e of this._externalInteractors)e.isEnabled()&&await e.interact(t)}handleClickMode(t){for(const e of this._externalInteractors)e.handleClickMode&&e.handleClickMode(t)}init(){this._externalInteractors=[],this._particleInteractors=[];for(const t of this._interactors){switch(t.type){case"external":this._externalInteractors.push(t);break;case"particles":this._particleInteractors.push(t)}t.init()}}async particlesInteract(t,e){for(const i of this._externalInteractors)i.clear(t,e);for(const i of this._particleInteractors)i.isEnabled(t)&&await i.interact(t,e)}async reset(t){for(const e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(const e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}const gi=t=>{if(!Z(t.outMode,t.checkModes))return;const e=2*t.radius;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)};class yi{constructor(t,e,i,s,o,n){this.container=i,this._calcPosition=(t,e,i,s=0)=>{for(const[,s]of t.plugins){const t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return m.create(t.x,t.y,i)}const o=B({size:t.canvas.size,position:e}),n=m.create(o.x,o.y,i),a=this.getRadius(),r=this.options.move.outModes,h=e=>{gi({outMode:e,checkModes:["bounce","bounce-horizontal"],coord:n.x,maxCoord:t.canvas.size.width,setCb:t=>n.x+=t,radius:a})},c=e=>{gi({outMode:e,checkModes:["bounce","bounce-vertical"],coord:n.y,maxCoord:t.canvas.size.height,setCb:t=>n.y+=t,radius:a})};return h(r.left??r.default),h(r.right??r.default),c(r.top??r.default),c(r.bottom??r.default),this._checkOverlap(n,s)?this._calcPosition(t,void 0,i,s+1):n},this._calculateVelocity=()=>{const t=D(this.direction).copy(),e=this.options.move;if("inside"===e.direction||"outside"===e.direction)return t;const i=Math.PI/180*O(e.angle.value),s=Math.PI/180*O(e.angle.offset),o={left:s-i/2,right:s+i/2};return e.straight||(t.angle+=P(C(o.left,o.right))),e.random&&"number"==typeof e.speed&&(t.length*=x()),t},this._checkOverlap=(t,e=0)=>{const i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;const o=i.overlap;if(o.enable)return!1;const n=o.retries;if(n>=0&&e>n)throw new Error(`${f} particle is overlapping and can't be placed`);return!!this.container.particles.find((e=>I(t,e.position)<s+e.getRadius()))},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;const e=this.roll.horizontal&&this.roll.vertical?2:1,i=this.roll.horizontal?Math.PI/2:0;return Math.floor(((this.roll.angle??0)+i)/(Math.PI/e))%2?this.backColor?this.backColor:this.roll.alter?oe(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{const e=this.container,i=O(this.options.zIndex.value);this.position=this._calcPosition(e,t,z(i,0,e.zLayers)),this.initialPosition=this.position.copy();const s=e.canvas.size;switch(this.moveCenter={...vt(this.options.move.center,s),radius:this.options.move.center.radius??0,mode:this.options.move.center.mode??"percent"},this.direction=E(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case"inside":this.outType="inside";break;case"outside":this.outType="outside"}this.offset=v.origin},this._loadShapeData=(t,e)=>{const i=t.options[this.shape];if(i)return st({close:t.close,fill:t.fill},ut(i,this.id,e))},this._engine=t,this.init(e,s,o,n)}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;const e=this.container,i=this.pathGenerator;for(const[,i]of e.plugins)i.particleDestroyed&&i.particleDestroyed(this,t);for(const i of e.particles.updaters)i.particleDestroyed&&i.particleDestroyed(this,t);i&&i.reset(this)}draw(t){const e=this.container;for(const[,i]of e.plugins)e.canvas.drawParticlePlugin(i,this,t);e.canvas.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??qt(this.color))}getMass(){return this.getRadius()**2*Math.PI/2}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??qt(this.strokeColor))}init(t,e,i,s){const o=this.container,n=this._engine;this.id=t,this.group=s,this.fill=!0,this.pathRotation=!1,this.close=!0,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType="normal",this.ignoresResizeRatio=!0;const a=o.retina.pixelRatio,r=o.actualOptions,h=fi(this._engine,o,r.particles),c=h.shape.type,{reduceDuplicates:l}=h;this.shape=ut(c,this.id,l);const d=h.shape;if(i&&i.shape&&i.shape.type){const t=ut(i.shape.type,this.id,l);t&&(this.shape=t,d.load(i.shape))}this.shapeData=this._loadShapeData(d,l),h.load(i);const u=this.shapeData;u&&h.load(u.particles);const p=new we(n,o);p.load(o.actualOptions.interactivity),p.load(h.interactivity),this.interactivity=p,this.fill=u?.fill??h.shape.fill,this.close=u?.close??h.shape.close,this.options=h;const f=this.options.move.path;this.pathDelay=1e3*R(f.delay),f.generator&&(this.pathGenerator=this._engine.plugins.getPathGenerator(f.generator),this.pathGenerator&&o.addPath(f.generator,this.pathGenerator)&&this.pathGenerator.init(o)),o.retina.initParticle(this),this.size=ft(this.options.size,a),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=1-O(this.options.move.decay);const m=o.particles;m.needsSort=m.needsSort||m.lastZIndex<this.position.z,m.lastZIndex=this.position.z,this.zIndexFactor=this.position.z/o.zLayers,this.sides=24;let v=o.drawers.get(this.shape);v||(v=this._engine.plugins.getShapeDrawer(this.shape),v&&o.drawers.set(this.shape,v)),v&&v.loadShape&&v.loadShape(this);const g=v?.getSidesCount;g&&(this.sides=g(this)),this.spawning=!1,this.shadowColor=Rt(this.options.shadow.color);for(const t of o.particles.updaters)t.init(this);for(const t of o.particles.movers)t.init&&t.init(this);v&&v.particleInit&&v.particleInit(o,this);for(const[,t]of o.plugins)t.particleCreated&&t.particleCreated(this)}isInsideCanvas(){const t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(const t of this.container.particles.updaters)t.reset&&t.reset(this)}}class _i{constructor(t,e){this.position=t,this.particle=e}}class bi{constructor(t,e){this.position={x:t,y:e}}}class wi extends bi{constructor(t,e,i,s){super(t,e),this.size={height:s,width:i}}contains(t){const e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){t instanceof xi&&t.intersects(this);const e=this.size.width,i=this.size.height,s=this.position,o=t.position,n=t instanceof wi?t.size:{width:0,height:0},a=n.width,r=n.height;return o.x<s.x+e&&o.x+a>s.x&&o.y<s.y+i&&o.y+r>s.y}}class xi extends bi{constructor(t,e,i){super(t,e),this.radius=i}contains(t){return I(t,this.position)<=this.radius}intersects(t){const e=this.position,i=t.position,s=Math.abs(i.x-e.x),o=Math.abs(i.y-e.y),n=this.radius;if(t instanceof xi){return n+t.radius>Math.sqrt(s**2+o**2)}if(t instanceof wi){const{width:e,height:i}=t.size;return Math.pow(s-e,2)+Math.pow(o-i,2)<=n**2||s<=n+e&&o<=n+i||s<=e||o<=i}return!1}}class zi{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{const{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:o}=this;for(let n=0;n<4;n++)this._subs.push(new zi(new wi(t+i/2*(n%2),e+s/2*(Math.round(n/2)-n%2),i/2,s/2),o));this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some((e=>e.insert(t)))))}query(t,e,i){const s=i||[];if(!t.intersects(this.rectangle))return[];for(const i of this._points)!t.contains(i.position)&&I(t.position,i.position)>i.particle.getRadius()&&(!e||e(i.particle))||s.push(i.particle);if(this._divided)for(const i of this._subs)i.query(t,e,s);return s}queryCircle(t,e,i){return this.query(new xi(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new wi(t.x,t.y,e.width,e.height),i)}}const Mi=t=>new wi(-t.width/4,-t.height/4,3*t.width/2,3*t.height/2);class Pi{constructor(t,e){this._applyDensity=(t,e,i)=>{if(!t.number.density?.enable)return;const s=t.number,o=this._initDensityFactor(s.density),n=s.value,a=s.limit>0?s.limit:n,r=Math.min(n,a)*o+e,h=Math.min(this.count,this.filter((t=>t.group===i)).length);this.limit=s.limit*o,h<r?this.push(Math.abs(r-h),void 0,t,i):h>r&&this.removeQuantity(h-r,i)},this._initDensityFactor=t=>{const e=this._container;if(!e.canvas.element||!t.enable)return 1;const i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.factor*s**2*t.area)},this._pushParticle=(t,e,i,s)=>{try{let o=this.pool.pop();o?o.init(this._nextId,t,e,i):o=new yi(this._engine,this._nextId,this._container,t,e,i);let n=!0;if(s&&(n=s(o)),!n)return;return this._array.push(o),this._zArray.push(o),this._nextId++,this._engine.dispatchEvent("particleAdded",{container:this._container,data:{particle:o}}),o}catch(t){return void G().warning(`${f} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{const s=this._array[t];if(!s||s.group!==e)return!1;s.destroy(i);const o=this._zArray.indexOf(s);return this._array.splice(t,1),this._zArray.splice(o,1),this.pool.push(s),this._engine.dispatchEvent("particleRemoved",{container:this._container,data:{particle:s}}),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this.pool=[],this.limit=0,this.needsSort=!1,this.lastZIndex=0,this._interactionManager=new vi(t,e);const i=e.canvas.size;this.quadTree=new zi(Mi(i),4),this.movers=this._engine.plugins.getMovers(e,!0),this.updaters=this._engine.plugins.getUpdaters(e,!0)}get count(){return this._array.length}addManualParticles(){const t=this._container,e=t.actualOptions;for(const i of e.manualParticles)this.addParticle(i.position?vt(i.position,t.canvas.size):void 0,i.options)}addParticle(t,e,i,s){const o=this._container.actualOptions.particles.number.limit;if(o>0){const t=this.count+1-o;t>0&&this.removeQuantity(t)}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[]}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}async draw(t){const e=this._container;e.canvas.clear(),await this.update(t);for(const[,i]of e.plugins)e.canvas.drawPlugin(i,t);for(const e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}handleClickMode(t){this._interactionManager.handleClickMode(t)}init(){const t=this._container,e=t.actualOptions;this.lastZIndex=0,this.needsSort=!1;let i=!1;this.updaters=this._engine.plugins.getUpdaters(t,!0),this._interactionManager.init();for(const[,e]of t.plugins)if(void 0!==e.particlesInitialization&&(i=e.particlesInitialization()),i)break;this._interactionManager.init();for(const[,e]of t.pathGenerators)e.init(t);if(this.addManualParticles(),!i){for(const t in e.particles.groups){const i=e.particles.groups[t];for(let s=this.count,o=0;o<i.number?.value&&s<e.particles.number.value;s++,o++)this.addParticle(void 0,i,t)}for(let t=this.count;t<e.particles.number.value;t++)this.addParticle()}}push(t,e,i,s){this.pushing=!0;for(let o=0;o<t;o++)this.addParticle(e?.position,i,s);this.pushing=!1}async redraw(){this.clear(),this.init(),await this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=1,i,s){if(t<0||t>this.count)return;let o=0;for(let n=t;o<e&&n<this.count;n++)this._removeParticle(n--,i,s)&&o++}removeQuantity(t,e){this.removeAt(0,t,e)}setDensity(){const t=this._container.actualOptions,e=t.particles.groups;for(const t in e)this._applyDensity(e[t],0,t);this._applyDensity(t.particles,t.manualParticles.length)}async update(t){const e=this._container,i=new Set;this.quadTree=new zi(Mi(e.canvas.size),4);for(const[,t]of e.pathGenerators)t.update();for(const[,i]of e.plugins)i.update&&i.update(t);for(const s of this._array){const o=e.canvas.resizeFactor;o&&!s.ignoresResizeRatio&&(s.position.x*=o.width,s.position.y*=o.height,s.initialPosition.x*=o.width,s.initialPosition.y*=o.height),s.ignoresResizeRatio=!1,await this._interactionManager.reset(s);for(const[,e]of this._container.plugins){if(s.destroyed)break;e.particleUpdate&&e.particleUpdate(s,t)}for(const e of this.movers)e.isEnabled(s)&&e.move(s,t);s.destroyed?i.add(s):this.quadTree.insert(new _i(s.getPosition(),s))}if(i.size){const t=t=>!i.has(t);this._array=this.filter(t),this._zArray=this._zArray.filter(t),this.pool.push(...i)}await this._interactionManager.externalInteract(t);for(const e of this._array){for(const i of this.updaters)i.update(e,t);e.destroyed||e.spawning||await this._interactionManager.particlesInteract(e,t)}if(delete e.canvas.resizeFactor,this.needsSort){const t=this._zArray;t.sort(((t,e)=>e.position.z-t.position.z||t.id-e.id)),this.lastZIndex=t[t.length-1].position.z,this.needsSort=!1}}}class Oi{constructor(t){this.container=t,this.pixelRatio=1,this.reduceFactor=1}init(){const t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||X()?1:window.devicePixelRatio,this.reduceFactor=1;const i=this.pixelRatio;if(t.canvas.element){const e=t.canvas.element;t.canvas.size.width=e.offsetWidth*i,t.canvas.size.height=e.offsetHeight*i}const s=e.particles,o=s.move;this.attractDistance=O(o.attract.distance)*i,this.maxSpeed=O(o.gravity.maxSpeed)*i,this.sizeAnimationSpeed=O(s.size.animation.speed)*i}initParticle(t){const e=t.options,i=this.pixelRatio,s=e.move,o=s.distance,n=t.retina;n.attractDistance=O(s.attract.distance)*i,n.moveDrift=O(s.drift)*i,n.moveSpeed=O(s.speed)*i,n.sizeAnimationSpeed=O(e.size.animation.speed)*i;const a=n.maxDistance;a.horizontal=void 0!==o.horizontal?o.horizontal*i:void 0,a.vertical=void 0!==o.vertical?o.vertical*i:void 0,n.maxSpeed=O(s.gravity.maxSpeed)*i}}function ki(t){return t&&!t.destroyed}function Si(t,e,...i){const s=new mi(t,e);return pi(s,...i),s}const Ci={generate:t=>t.velocity,init:()=>{},update:()=>{},reset:()=>{}};class Ri{constructor(t,e,i){this.id=e,this._intersectionManager=t=>{if(ki(this)&&this.actualOptions.pauseOnOutsideViewport)for(const e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play:this.pause)()},this._nextFrame=async t=>{try{if(!this.smooth&&void 0!==this.lastFrameTime&&t<this.lastFrameTime+1e3/this.fpsLimit)return void this.draw(!1);this.lastFrameTime??=t;const e=function(t,e=60,i=!1){return{value:t,factor:i?60/e:60*t/1e3}}(t-this.lastFrameTime,this.fpsLimit,this.smooth);if(this.addLifeTime(e.value),this.lastFrameTime=t,e.value>1e3)return void this.draw(!1);if(await this.particles.draw(e),!this.alive())return void this.destroy();this.getAnimationStatus()&&this.draw(!1)}catch(t){G().error(`${f} in animation loop`,t)}},this._engine=t,this.fpsLimit=120,this.smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this.lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new Oi(this),this.canvas=new ae(this),this.particles=new Pi(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.drawers=new Map,this._options=Si(this._engine,this),this.actualOptions=Si(this._engine,this),this._eventListeners=new he(this),"undefined"!=typeof IntersectionObserver&&IntersectionObserver&&(this._intersectionObserver=new IntersectionObserver((t=>this._intersectionManager(t)))),this._engine.dispatchEvent("containerBuilt",{container:this})}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!ki(this))return;const e=this.interactivity.element;if(!e)return;const i=(e,i,s)=>{if(!ki(this))return;const o=this.retina.pixelRatio,n={x:i.x*o,y:i.y*o},a=this.particles.quadTree.queryCircle(n,s*o);t(e,a)};let s=!1,o=!1;e.addEventListener("click",(t=>{if(!ki(this))return;const e=t,s={x:e.offsetX||e.clientX,y:e.offsetY||e.clientY};i(t,s,1)})),e.addEventListener("touchstart",(()=>{ki(this)&&(s=!0,o=!1)})),e.addEventListener("touchmove",(()=>{ki(this)&&(o=!0)})),e.addEventListener("touchend",(t=>{if(ki(this)){if(s&&!o){const e=t;let s=e.touches[e.touches.length-1];if(!s&&(s=e.changedTouches[e.changedTouches.length-1],!s))return;const o=this.canvas.element,n=o?o.getBoundingClientRect():void 0,a={x:s.clientX-(n?n.left:0),y:s.clientY-(n?n.top:0)};i(t,a,Math.max(s.radiusX,s.radiusY))}s=!1,o=!1}})),e.addEventListener("touchcancel",(()=>{ki(this)&&(s=!1,o=!1)}))}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!ki(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e??Ci),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}destroy(){if(!ki(this))return;this.stop(),this.particles.destroy(),this.canvas.destroy();for(const[,t]of this.drawers)t.destroy&&t.destroy(this);for(const t of this.drawers.keys())this.drawers.delete(t);this._engine.plugins.destroy(this),this.destroyed=!0;const t=this._engine.dom(),e=t.findIndex((t=>t===this));e>=0&&t.splice(e,1),this._engine.dispatchEvent("containerDestroyed",{container:this})}draw(t){if(!ki(this))return;let e=t;this._drawAnimationFrame=requestAnimationFrame((async t=>{e&&(this.lastFrameTime=void 0,e=!1),await this._nextFrame(t)}))}async export(t,e={}){for(const[,i]of this.plugins){if(!i.export)continue;const s=await i.export(t,e);if(s.supported)return s.blob}G().error(`${f} - Export plugin with type ${t} not found`)}getAnimationStatus(){return!this._paused&&!this.pageHidden&&ki(this)}handleClickMode(t){if(ki(this)){this.particles.handleClickMode(t);for(const[,e]of this.plugins)e.handleClickMode&&e.handleClickMode(t)}}async init(){if(!ki(this))return;const t=this._engine.plugins.getSupportedShapes();for(const e of t){const t=this._engine.plugins.getShapeDrawer(e);t&&this.drawers.set(e,t)}this._options=Si(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=Si(this._engine,this,this._options);const e=this._engine.plugins.getAvailablePlugins(this);for(const[t,i]of e)this.plugins.set(t,i);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize(),this.zLayers=this.actualOptions.zLayers,this._duration=1e3*O(this.actualOptions.duration),this._delay=1e3*O(this.actualOptions.delay),this._lifeTime=0,this.fpsLimit=this.actualOptions.fpsLimit>0?this.actualOptions.fpsLimit:120,this.smooth=this.actualOptions.smooth;for(const[,t]of this.drawers)t.init&&await t.init(this);for(const[,t]of this.plugins)t.init&&await t.init();this._engine.dispatchEvent("containerInit",{container:this}),this.particles.init(),this.particles.setDensity();for(const[,t]of this.plugins)t.particlesSetup&&t.particlesSetup();this._engine.dispatchEvent("particlesSetup",{container:this})}async loadTheme(t){ki(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(ki(this)&&(void 0!==this._drawAnimationFrame&&(cancelAnimationFrame(this._drawAnimationFrame),delete this._drawAnimationFrame),!this._paused)){for(const[,t]of this.plugins)t.pause&&t.pause();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent("containerPaused",{container:this})}}play(t){if(!ki(this))return;const e=this._paused||t;if(!this._firstStart||this.actualOptions.autoPlay){if(this._paused&&(this._paused=!1),e)for(const[,t]of this.plugins)t.play&&t.play();this._engine.dispatchEvent("containerPlay",{container:this}),this.draw(e||!1)}else this._firstStart=!1}async refresh(){if(ki(this))return this.stop(),this.start()}async reset(){if(ki(this))return this._initialSourceOptions=void 0,this._options=Si(this._engine,this),this.actualOptions=Si(this._engine,this,this._options),this.refresh()}setNoise(t,e,i){ki(this)&&this.setPath(t,e,i)}setPath(t,e,i){if(!t||!ki(this))return;const s={...Ci};if(wt(t))s.generate=t,e&&(s.init=e),i&&(s.update=i);else{const e=s;s.generate=t.generate||e.generate,s.init=t.init||e.init,s.update=t.update||e.update}this.addPath("default",s,!0)}async start(){ki(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise((t=>{this._delayTimeout=setTimeout((async()=>{this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element);for(const[,t]of this.plugins)t.start&&await t.start();this._engine.dispatchEvent("containerStarted",{container:this}),this.play(),t()}),this._delay)})))}stop(){if(ki(this)&&this.started){this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element);for(const[,t]of this.plugins)t.stop&&t.stop();for(const t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent("containerStopped",{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];const t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this.responsiveMaxWidth!==t&&(this.responsiveMaxWidth=t,!0)}}class Ti{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){const i=this._listeners.get(t);i&&i.forEach((t=>t(e)))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){const i=this._listeners.get(t);if(!i)return;const s=i.length,o=i.indexOf(e);o<0||(1===s?this._listeners.delete(t):i.splice(o,1))}}function Ii(t,e,i,s=!1){let o=e.get(t);return o&&!s||(o=[...i.values()].map((e=>e(t))),e.set(t,o)),o}class Ei{constructor(t){this._engine=t,this.plugins=[],this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.drawers=new Map,this.pathGenerators=new Map}addInteractor(t,e){this._initializers.interactors.set(t,e)}addParticleMover(t,e){this._initializers.movers.set(t,e)}addParticleUpdater(t,e){this._initializers.updaters.set(t,e)}addPathGenerator(t,e){!this.getPathGenerator(t)&&this.pathGenerators.set(t,e)}addPlugin(t){!this.getPlugin(t.id)&&this.plugins.push(t)}addPreset(t,e,i=!1){(i||!this.getPreset(t))&&this.presets.set(t,e)}addShapeDrawer(t,e){dt(t,(t=>{!this.getShapeDrawer(t)&&this.drawers.set(t,e)}))}destroy(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}getAvailablePlugins(t){const e=new Map;for(const i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,i.getPlugin(t));return e}getInteractors(t,e=!1){return Ii(t,this.interactors,this._initializers.interactors,e)}getMovers(t,e=!1){return Ii(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find((e=>e.id===t))}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.drawers.get(t)}getSupportedShapes(){return this.drawers.keys()}getUpdaters(t,e=!1){return Ii(t,this.updaters,this._initializers.updaters,e)}loadOptions(t,e){for(const i of this.plugins)i.loadOptions(t,e)}loadParticlesOptions(t,e,...i){const s=this.updaters.get(t);if(s)for(const t of s)t.loadOptions&&t.loadOptions(e,...i)}}class Di{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new Ti,this._initialized=!1,this.plugins=new Ei(this)}get configs(){const t={};for(const[e,i]of this._configs)t[e]=i;return t}get version(){return"2.12.0"}addConfig(t,e){_t(t)?e&&(e.name=t,this._configs.set(t,e)):this._configs.set(t.name??"default",t)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this.plugins.addInteractor(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this.plugins.addParticleMover(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this.plugins.addParticleUpdater(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.plugins.addPathGenerator(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.plugins.addPlugin(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){this.plugins.addPreset(t,e,i),await this.refresh(s)}async addShape(t,e,i,s,o,n=!0){let a,r,h,c,l=n;yt(i)?(l=i,r=void 0):r=i,yt(s)?(l=s,h=void 0):h=s,yt(o)?(l=o,c=void 0):c=o,a=wt(e)?{afterEffect:h,destroy:c,draw:e,init:r}:e,this.plugins.addShapeDrawer(t,a),await this.refresh(l)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this._domArray}domItem(t){const e=this.dom(),i=e[t];if(i&&!i.destroyed)return i;e.splice(t,1)}init(){this._initialized||(this._initialized=!0)}async load(t,e){return this.loadFromArray(t,e)}async loadFromArray(t,e,i){let s;return!function(t){return!!((e=t).id||e.element||e.url||e.options);var e}(t)?(s={},_t(t)?s.id=t:s.options=t,bt(e)?s.index=e:s.options=e??s.options,s.index=i??s.index):s=t,this._loadParams(s)}async loadJSON(t,e,i){let s,o;return bt(e)||void 0===e?s=t:(o=t,s=e),this._loadParams({id:o,url:s,index:i})}async refresh(t=!0){t&&this.dom().forEach((t=>t.refresh()))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}async set(t,e,i,s){const o={index:s};return _t(t)?o.id=t:o.element=t,e instanceof HTMLElement?o.element=e:o.options=e,bt(i)?o.index=i:o.options=i??o.options,this._loadParams(o)}async setJSON(t,e,i,s){const o={};return t instanceof HTMLElement?(o.element=t,o.url=e,o.index=i):(o.id=t,o.element=e,o.url=i,o.index=s),this._loadParams(o)}setOnClickHandler(t){const e=this.dom();if(!e.length)throw new Error(`${f} can only set click handlers after calling tsParticles.load()`);for(const i of e)i.addClickHandler(t)}async _loadParams(t){const e=t.id??`tsparticles${Math.floor(1e4*x())}`,{index:s,url:o}=t,n=o?await async function(t){const e=ut(t.url,t.index);if(!e)return t.fallback;const i=await fetch(e);return i.ok?i.json():(G().error(`${f} ${i.status} while retrieving config file`),t.fallback)}({fallback:t.options,url:o,index:s}):t.options;let a=t.element??document.getElementById(e);a||(a=document.createElement("div"),a.id=e,document.body.append(a));const r=ut(n,s),h=this.dom(),c=h.findIndex((t=>t.id===e));if(c>=0){const t=this.domItem(c);t&&!t.destroyed&&(t.destroy(),h.splice(c,1))}let l;if("canvas"===a.tagName.toLowerCase())l=a,l.dataset[i]="false";else{const t=a.getElementsByTagName("canvas");t.length?(l=t[0],l.dataset[i]="false"):(l=document.createElement("canvas"),l.dataset[i]="true",a.appendChild(l))}l.style.width||(l.style.width="100%"),l.style.height||(l.style.height="100%");const d=new Ri(this,e,r);return c>=0?h.splice(c,0,d):h.push(d),d.canvas.loadCanvas(l),await d.start(),d}}class Ai{constructor(){this.key="hsl",this.stringPrefix="hsl"}handleColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.s&&void 0!==e.l)return Ft(e)}handleRangeColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.l)return Ft({h:O(e.h),l:O(e.l),s:O(e.s)})}parseString(t){if(!t.startsWith("hsl"))return;const e=/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([\d.%]+)\s*)?\)/i.exec(t);return e?Vt({a:e.length>4?W(e[5]):1,h:parseInt(e[1],10),l:parseInt(e[3],10),s:parseInt(e[2],10)}):void 0}}class Li{constructor(){this.key="rgb",this.stringPrefix="rgb"}handleColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return e}handleRangeColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return{r:O(e.r),g:O(e.g),b:O(e.b)}}parseString(t){if(!t.startsWith(this.stringPrefix))return;const e=/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(,\s*([\d.%]+)\s*)?\)/i.exec(t);return e?{a:e.length>4?W(e[5]):1,b:parseInt(e[3],10),g:parseInt(e[2],10),r:parseInt(e[1],10)}:void 0}}class Fi{constructor(t){this.container=t,this.type="external"}}class Vi{constructor(t){this.container=t,this.type="particles"}}const Bi=function(){const t=new Li,e=new Ai;kt(t),kt(e);const i=new Di;return i.init(),i}();return X()||(window.tsParticles=Bi),e})()));