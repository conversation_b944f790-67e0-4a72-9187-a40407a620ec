"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LifeDuration = void 0;
const tsparticles_engine_1 = require("tsparticles-engine");
class LifeDuration extends tsparticles_engine_1.ValueWithRandom {
    constructor() {
        super();
        this.random.minimumValue = 0.0001;
        this.sync = false;
    }
    load(data) {
        if (!data) {
            return;
        }
        super.load(data);
        if (data.sync !== undefined) {
            this.sync = data.sync;
        }
    }
}
exports.LifeDuration = LifeDuration;
