import type { IColor, IHsl, IHsla, IRangeColor, IRgb, IRgba } from "../Core/Interfaces/Colors";
import type { HslAnimation } from "../Options/Classes/HslAnimation";
import type { IColorManager } from "../Core/Interfaces/IColorManager";
import type { IOptionsColor } from "../Options/Interfaces/IOptionsColor";
import type { IParticle } from "../Core/Interfaces/IParticle";
import type { IParticleHslAnimation } from "../Core/Interfaces/IParticleHslAnimation";
export declare function addColorManager(manager: IColorManager): void;
export declare function rangeColorToRgb(input?: string | IRangeColor, index?: number, useIndex?: boolean): IRgb | undefined;
export declare function colorToRgb(input?: string | IColor, index?: number, useIndex?: boolean): IRgb | undefined;
export declare function colorToHsl(color: string | IColor | undefined, index?: number, useIndex?: boolean): IHsl | undefined;
export declare function rangeColorToHsl(color: string | IRangeColor | undefined, index?: number, useIndex?: boolean): IHsl | undefined;
export declare function rgbToHsl(color: IRgb): IHsl;
export declare function stringToAlpha(input: string): number | undefined;
export declare function stringToRgb(input: string): IRgb | undefined;
export declare function hslToRgb(hsl: IHsl): IRgb;
export declare function hslaToRgba(hsla: IHsla): IRgba;
export declare function getRandomRgbColor(min?: number): IRgb;
export declare function getStyleFromRgb(color: IRgb, opacity?: number): string;
export declare function getStyleFromHsl(color: IHsl, opacity?: number): string;
export declare function colorMix(color1: IRgb | IHsl, color2: IRgb | IHsl, size1: number, size2: number): IRgb;
export declare function getLinkColor(p1: IParticle, p2?: IParticle, linkColor?: string | IRgb): IRgb | undefined;
export declare function getLinkRandomColor(optColor: string | IOptionsColor, blink: boolean, consent: boolean): IRgb | string | undefined;
export declare function getHslFromAnimation(animation?: IParticleHslAnimation): IHsl | undefined;
export declare function getHslAnimationFromHsl(hsl: IHsl, animationOptions: HslAnimation | undefined, reduceFactor: number): IParticleHslAnimation;
