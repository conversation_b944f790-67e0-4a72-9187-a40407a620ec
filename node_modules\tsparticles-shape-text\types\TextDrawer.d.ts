import { type Container, type IShapeDrawer } from "tsparticles-engine";
import type { TextParticle } from "./TextParticle";
export declare const validTypes: string[];
export declare class TextDrawer implements IShapeDrawer {
    draw(context: CanvasRenderingContext2D, particle: TextParticle, radius: number, opacity: number): void;
    getSidesCount(): number;
    init(container: Container): Promise<void>;
    particleInit(container: Container, particle: TextParticle): void;
}
