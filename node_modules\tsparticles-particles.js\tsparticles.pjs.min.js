/*! For license information please see tsparticles.pjs.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var o="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var r in o)("object"==typeof exports?exports:e)[r]=o[r]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},o={};function r(e){var n=o[e];if(void 0!==n)return n.exports;var i=o[e]={exports:{}};return t[e](i,i.exports,r),i.exports}r.d=(e,t)=>{for(var o in t)r.o(t,o)&&!r.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n={};return(()=>{r.r(n),r.d(n,{initPjs:()=>o});var e=r(961);class t{static init(o){const r=new t,n=o.selector;if(!n)throw new Error("No selector provided");const i=document.querySelector(n);if(!i)throw new Error("No element found for selector");return e.tsParticles.set(n.replace(".","").replace("!",""),i,{fullScreen:{enable:!1},particles:{color:{value:o.color??"!000000"},links:{color:"random",distance:o.minDistance??120,enable:o.connectParticles??!1},move:{enable:!0,speed:o.speed??.5},number:{value:o.maxParticles??100},size:{value:{min:1,max:o.sizeVariations??3}}},responsive:o.responsive?.map((e=>({maxWidth:e.breakpoint,options:{particles:{color:{value:e.options?.color},links:{distance:e.options?.minDistance,enable:e.options?.connectParticles},number:{value:o.maxParticles},move:{enable:!0,speed:e.options?.speed},size:{value:e.options?.sizeVariations}}}})))}).then((e=>{r._container=e})),r}destroy(){const e=this._container;e&&e.destroy()}pauseAnimation(){const e=this._container;e&&e.pause()}resumeAnimation(){const e=this._container;e&&e.play()}}const o=e=>{const{particlesJS:o,pJSDom:r}=(e=>{const t=(t,o)=>e.load(t,o);return t.load=(t,o,r)=>{e.loadJSON(t,o).then((e=>{e&&r(e)})).catch((()=>{r(void 0)}))},t.setOnClickHandler=t=>{e.setOnClickHandler(t)},{particlesJS:t,pJSDom:e.dom()}})(e);return window.particlesJS=o,window.pJSDom=r,window.Particles=t,{particlesJS:o,pJSDom:r,Particles:t}}})(),n})()));