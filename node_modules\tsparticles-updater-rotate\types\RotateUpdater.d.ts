import { type Container, type IDelta, type IParticleUpdater, type IParticleValueAnimation, type IParticlesOptions, type Particle, type ParticlesOptions, type RecursivePartial } from "tsparticles-engine";
import type { IRotate } from "./Options/Interfaces/IRotate";
import { Rotate } from "./Options/Classes/Rotate";
type RotateParticle = Particle & {
    options: RotateParticlesOptions;
    rotate?: IParticleValueAnimation<number>;
};
type IRotateParticlesOptions = IParticlesOptions & {
    rotate?: IRotate;
};
type RotateParticlesOptions = ParticlesOptions & {
    rotate?: Rotate;
};
export declare class RotateUpdater implements IParticleUpdater {
    private readonly container;
    constructor(container: Container);
    init(particle: RotateParticle): void;
    isEnabled(particle: RotateParticle): boolean;
    loadOptions(options: RotateParticlesOptions, ...sources: (RecursivePartial<IRotateParticlesOptions> | undefined)[]): void;
    update(particle: RotateParticle, delta: IDelta): void;
}
export {};
