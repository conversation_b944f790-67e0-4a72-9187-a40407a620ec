/*! For license information please see tsparticles.shape.line.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var o=t();for(var n in o)("object"==typeof exports?exports:e)[n]=o[n]}}(this,(()=>(()=>{"use strict";var e={d:(t,o)=>{for(var n in o)e.o(o,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:o[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{loadLineShape:()=>n});class o{draw(e,t,o){const n=t.shapeData;e.moveTo(-o/2,0),e.lineTo(o/2,0),e.lineCap=n?.cap??"butt"}getSidesCount(){return 1}}async function n(e,t=!0){await e.addShape("line",new o,t)}return t})()));