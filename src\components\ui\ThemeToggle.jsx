import { motion } from 'framer-motion'
import { Sun, Moon } from 'lucide-react'
import { useTheme } from '../../contexts/ThemeContext'

const ThemeToggle = ({ size = 'md', className = '' }) => {
  const { theme, toggleTheme, isDark } = useTheme()

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  }

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  }

  return (
    <motion.button
      onClick={toggleTheme}
      className={`
        relative ${sizeClasses[size]} rounded-full p-2
        bg-gradient-to-r from-blue-500 to-purple-500
        dark:from-purple-600 dark:to-blue-600
        shadow-lg hover:shadow-xl
        transition-all duration-300
        ${className}
      `}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
      initial={false}
    >
      <motion.div
        className="absolute inset-0 rounded-full bg-white dark:bg-gray-800"
        initial={false}
        animate={{
          scale: isDark ? 0.8 : 0.9,
          opacity: isDark ? 0.1 : 0.2
        }}
        transition={{ duration: 0.3 }}
      />
      
      <motion.div
        className="relative z-10 flex items-center justify-center"
        initial={false}
        animate={{ rotate: isDark ? 180 : 0 }}
        transition={{ duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
      >
        {isDark ? (
          <Moon className={`${iconSizes[size]} text-white`} />
        ) : (
          <Sun className={`${iconSizes[size]} text-white`} />
        )}
      </motion.div>

      {/* Animated background glow */}
      <motion.div
        className="absolute inset-0 rounded-full"
        initial={false}
        animate={{
          background: isDark 
            ? 'radial-gradient(circle, rgba(147, 51, 234, 0.3) 0%, transparent 70%)'
            : 'radial-gradient(circle, rgba(59, 130, 246, 0.3) 0%, transparent 70%)'
        }}
        transition={{ duration: 0.3 }}
      />
    </motion.button>
  )
}

export default ThemeToggle
