import type { IOptionLoader, RecursivePartial } from "tsparticles-engine";
import type { ILife } from "../Interfaces/ILife";
import { LifeDelay } from "./LifeDelay";
import { LifeDuration } from "./LifeDuration";
export declare class Life implements ILife, IOptionLoader<ILife> {
    count: number;
    delay: LifeDelay;
    duration: LifeDuration;
    constructor();
    load(data?: RecursivePartial<ILife>): void;
}
