/*! For license information please see tsparticles.move.base.min.js.LICENSE.txt */
!function(e,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var i="object"==typeof exports?t(require("tsparticles-engine")):t(e.window);for(var n in i)("object"==typeof exports?exports:e)[n]=i[n]}}(this,(e=>(()=>{"use strict";var t={961:t=>{t.exports=e}},i={};function n(e){var o=i[e];if(void 0!==o)return o.exports;var a=i[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};return(()=>{n.r(o),n.d(o,{loadBaseMover:()=>a});var e=n(961);function t(t,i,n,o,a,s){!function(t,i){const n=t.options,o=n.move.path;if(!o.enable)return;if(t.lastPathTime<=t.pathDelay)return void(t.lastPathTime+=i.value);const a=t.pathGenerator?.generate(t,i);a&&t.velocity.addTo(a);o.clamp&&(t.velocity.x=(0,e.clamp)(t.velocity.x,-1,1),t.velocity.y=(0,e.clamp)(t.velocity.y,-1,1));t.lastPathTime-=t.pathDelay}(t,s);const c=t.gravity,r=c?.enable&&c.inverse?-1:1;a&&n&&(t.velocity.x+=a*s.factor/(60*n)),c?.enable&&n&&(t.velocity.y+=r*(c.acceleration*s.factor)/(60*n));const l=t.moveDecay;t.velocity.multTo(l);const p=t.velocity.mult(n);c?.enable&&o>0&&(!c.inverse&&p.y>=0&&p.y>=o||c.inverse&&p.y<=0&&p.y<=-o)&&(p.y=r*o,n&&(t.velocity.y=p.y/n));const y=t.options.zIndex,d=(1-t.zIndexFactor)**y.velocityRate;p.multTo(d);const{position:v}=t;v.addTo(p),i.vibrate&&(v.x+=Math.sin(v.x*Math.cos(v.y)),v.y+=Math.cos(v.y*Math.sin(v.x)))}class i{constructor(){this._initSpin=t=>{const i=t.container,n=t.options.move.spin;if(!n.enable)return;const o=n.position??{x:50,y:50},a={x:o.x/100*i.canvas.size.width,y:o.y/100*i.canvas.size.height},s=t.getPosition(),c=(0,e.getDistance)(s,a),r=(0,e.getRangeValue)(n.acceleration);t.retina.spinAcceleration=r*i.retina.pixelRatio,t.spin={center:a,direction:t.velocity.x>=0?"clockwise":"counter-clockwise",angle:t.velocity.angle,radius:c,acceleration:t.retina.spinAcceleration}}}init(t){const i=t.options.move.gravity;t.gravity={enable:i.enable,acceleration:(0,e.getRangeValue)(i.acceleration),inverse:i.inverse},this._initSpin(t)}isEnabled(e){return!e.destroyed&&e.options.move.enable}move(i,n){const o=i.options,a=o.move;if(!a.enable)return;const s=i.container,c=s.retina.pixelRatio,r=function(e){return e.slow.inRange?e.slow.factor:1}(i),l=(i.retina.moveSpeed??=(0,e.getRangeValue)(a.speed)*c)*s.retina.reduceFactor,p=i.retina.moveDrift??=(0,e.getRangeValue)(i.options.move.drift)*c,y=(0,e.getRangeMax)(o.size.value)*c,d=l*(a.size?i.getRadius()/y:1)*r*(n.factor||1)/2,v=i.retina.maxSpeed??s.retina.maxSpeed;a.spin.enable?function(e,t){const i=e.container;if(!e.spin)return;const n={x:"clockwise"===e.spin.direction?Math.cos:Math.sin,y:"clockwise"===e.spin.direction?Math.sin:Math.cos};e.position.x=e.spin.center.x+e.spin.radius*n.x(e.spin.angle),e.position.y=e.spin.center.y+e.spin.radius*n.y(e.spin.angle),e.spin.radius+=e.spin.acceleration;const o=Math.max(i.canvas.size.width,i.canvas.size.height);e.spin.radius>o/2?(e.spin.radius=o/2,e.spin.acceleration*=-1):e.spin.radius<0&&(e.spin.radius=0,e.spin.acceleration*=-1),e.spin.angle+=t/100*(1-e.spin.radius/o)}(i,d):t(i,a,d,v,p,n),function(t){const i=t.initialPosition,{dx:n,dy:o}=(0,e.getDistances)(i,t.position),a=Math.abs(n),s=Math.abs(o),{maxDistance:c}=t.retina,r=c.horizontal,l=c.vertical;if(r||l)if((r&&a>=r||l&&s>=l)&&!t.misplaced)t.misplaced=!!r&&a>r||!!l&&s>l,r&&(t.velocity.x=t.velocity.y/2-t.velocity.x),l&&(t.velocity.y=t.velocity.x/2-t.velocity.y);else if((!r||a<r)&&(!l||s<l)&&t.misplaced)t.misplaced=!1;else if(t.misplaced){const n=t.position,o=t.velocity;r&&(n.x<i.x&&o.x<0||n.x>i.x&&o.x>0)&&(o.x*=-(0,e.getRandom)()),l&&(n.y<i.y&&o.y<0||n.y>i.y&&o.y>0)&&(o.y*=-(0,e.getRandom)())}}(i)}}async function a(e,t=!0){await e.addMover("base",(()=>new i),t)}})(),o})()));