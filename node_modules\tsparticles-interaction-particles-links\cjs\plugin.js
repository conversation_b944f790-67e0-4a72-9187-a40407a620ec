"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadLinksPlugin = void 0;
const LinkInstance_1 = require("./LinkInstance");
class LinksPlugin {
    constructor() {
        this.id = "links";
    }
    getPlugin(container) {
        return new LinkInstance_1.LinkInstance(container);
    }
    loadOptions() {
    }
    needsPlugin() {
        return true;
    }
}
async function loadLinksPlugin(engine, refresh = true) {
    const plugin = new LinksPlugin();
    await engine.addPlugin(plugin, refresh);
}
exports.loadLinksPlugin = loadLinksPlugin;
