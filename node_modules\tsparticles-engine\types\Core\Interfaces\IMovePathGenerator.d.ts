import type { Container } from "../Container";
import type { <PERSON><PERSON><PERSON> } from "./IDelta";
import type { Particle } from "../Particle";
import type { Vector } from "../Utils/Vector";
export interface IMovePathGenerator {
    generate: (particle: Particle, delta: ID<PERSON>ta) => Vector;
    init: (container: Container) => void;
    reset: (particle: Particle) => void;
    update: () => void;
}
