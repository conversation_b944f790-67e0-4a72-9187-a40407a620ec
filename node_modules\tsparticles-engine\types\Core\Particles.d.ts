import type { ClickMode } from "../Enums/Modes/ClickMode";
import type { Container } from "./Container";
import type { Engine } from "./Engine";
import type { ICoordinates } from "./Interfaces/ICoordinates";
import type { IDelta } from "./Interfaces/IDelta";
import type { IMouseData } from "./Interfaces/IMouseData";
import type { IParticlesOptions } from "../Options/Interfaces/Particles/IParticlesOptions";
import { Particle } from "./Particle";
import { QuadTree } from "./Utils/QuadTree";
import type { RecursivePartial } from "../Types/RecursivePartial";
export declare class Particles {
    lastZIndex: number;
    limit: number;
    movers: import("..").IParticleMover[];
    needsSort: boolean;
    pool: Particle[];
    pushing?: boolean;
    quadTree: QuadTree;
    updaters: import("..").IParticleUpdater[];
    private _array;
    private readonly _container;
    private readonly _engine;
    private readonly _interactionManager;
    private _nextId;
    private _zArray;
    constructor(engine: Engine, container: Container);
    get count(): number;
    addManualParticles(): void;
    addParticle(position?: ICoordinates, overrideOptions?: RecursivePartial<IParticlesOptions>, group?: string, initializer?: (particle: Particle) => boolean): Particle | undefined;
    clear(): void;
    destroy(): void;
    draw(delta: IDelta): Promise<void>;
    filter(condition: (particle: Particle) => boolean): Particle[];
    find(condition: (particle: Particle) => boolean): Particle | undefined;
    handleClickMode(mode: ClickMode | string): void;
    init(): void;
    push(nb: number, mouse?: IMouseData, overrideOptions?: RecursivePartial<IParticlesOptions>, group?: string): void;
    redraw(): Promise<void>;
    remove(particle: Particle, group?: string, override?: boolean): void;
    removeAt(index: number, quantity?: number, group?: string, override?: boolean): void;
    removeQuantity(quantity: number, group?: string): void;
    setDensity(): void;
    update(delta: IDelta): Promise<void>;
    private readonly _applyDensity;
    private readonly _initDensityFactor;
    private readonly _pushParticle;
    private readonly _removeParticle;
}
