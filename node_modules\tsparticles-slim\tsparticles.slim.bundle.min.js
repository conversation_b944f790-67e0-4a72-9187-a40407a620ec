/*! For license information please see tsparticles.slim.bundle.min.js.LICENSE.txt */
!function(t,e){if("object"==typeof exports&&"object"==typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{var i=e();for(var s in i)("object"==typeof exports?exports:t)[s]=i[s]}}(this,(()=>(()=>{"use strict";var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r:t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}},e={};t.r(e),t.d(e,{AnimatableColor:()=>Se,AnimationOptions:()=>Re,AnimationValueWithRandom:()=>Le,Background:()=>he,BackgroundMask:()=>ue,BackgroundMaskCover:()=>de,Circle:()=>_i,ClickEvent:()=>fe,Collisions:()=>qe,CollisionsAbsorb:()=>Oe,CollisionsOverlap:()=>Te,ColorAnimation:()=>Ce,DivEvent:()=>ve,Events:()=>be,ExternalInteractorBase:()=>Bi,FullScreen:()=>pe,HoverEvent:()=>ge,HslAnimation:()=>Pe,HslColorManager:()=>Li,Interactivity:()=>xe,ManualParticle:()=>_e,Modes:()=>we,Move:()=>Ye,MoveAngle:()=>Ve,MoveAttract:()=>He,MoveCenter:()=>We,MoveGravity:()=>Ue,MovePath:()=>$e,MoveTrail:()=>je,Opacity:()=>Ze,OpacityAnimation:()=>Je,Options:()=>vi,OptionsColor:()=>le,OutModes:()=>Ne,Parallax:()=>me,ParticlesBounce:()=>Fe,ParticlesBounceFactor:()=>Be,ParticlesDensity:()=>Qe,ParticlesInteractorBase:()=>Fi,ParticlesNumber:()=>Ke,ParticlesOptions:()=>ui,Point:()=>bi,Range:()=>wi,RangedAnimationOptions:()=>Ie,RangedAnimationValueWithRandom:()=>Ae,Rectangle:()=>xi,ResizeEvent:()=>ye,Responsive:()=>ke,RgbColorManager:()=>Ai,Shadow:()=>ti,Shape:()=>ri,Size:()=>li,SizeAnimation:()=>ci,Spin:()=>Xe,Stroke:()=>hi,Theme:()=>ze,ThemeDefault:()=>Me,ValueWithRandom:()=>Ee,Vector:()=>m,Vector3d:()=>v,ZIndex:()=>di,addColorManager:()=>Pt,addEasing:()=>b,alterHsl:()=>oe,areBoundsInside:()=>et,arrayRandomIndex:()=>Q,calcExactPositionOrRandomFromSize:()=>q,calcExactPositionOrRandomFromSizeRanged:()=>V,calcPositionFromSize:()=>A,calcPositionOrRandomFromSize:()=>B,calcPositionOrRandomFromSizeRanged:()=>F,calculateBounds:()=>it,circleBounce:()=>lt,circleBounceDataFromParticle:()=>ct,clamp:()=>k,clear:()=>Qt,collisionVelocity:()=>L,colorMix:()=>Wt,colorToHsl:()=>It,colorToRgb:()=>Rt,deepExtend:()=>st,divMode:()=>rt,divModeExecute:()=>nt,drawLine:()=>Xt,drawParticle:()=>Kt,drawParticlePlugin:()=>se,drawPlugin:()=>ie,drawShape:()=>te,drawShapeAfterEffect:()=>ee,drawTriangle:()=>Yt,errorPrefix:()=>f,executeOnSingleOrMultiple:()=>dt,findItemFromSingleOrMultiple:()=>pt,generatedAttribute:()=>i,getDistance:()=>I,getDistances:()=>R,getEasing:()=>w,getHslAnimationFromHsl:()=>jt,getHslFromAnimation:()=>Gt,getLinkColor:()=>Ut,getLinkRandomColor:()=>$t,getLogger:()=>$,getParticleBaseVelocity:()=>E,getParticleDirectionAngle:()=>D,getPosition:()=>mt,getRandom:()=>_,getRandomRgbColor:()=>qt,getRangeMax:()=>S,getRangeMin:()=>P,getRangeValue:()=>C,getSize:()=>gt,getStyleFromHsl:()=>Ht,getStyleFromRgb:()=>Vt,getValue:()=>T,hasMatchMedia:()=>N,hslToRgb:()=>Bt,hslaToRgba:()=>Ft,initParticleNumericAnimationValue:()=>ft,isArray:()=>kt,isBoolean:()=>yt,isDivModeEnabled:()=>ot,isFunction:()=>xt,isInArray:()=>J,isNumber:()=>wt,isObject:()=>_t,isPointInside:()=>tt,isSsr:()=>j,isString:()=>bt,itemFromArray:()=>K,itemFromSingleOrMultiple:()=>ut,loadFont:()=>Z,loadOptions:()=>pi,loadParticlesOptions:()=>fi,loadSlim:()=>Po,mix:()=>M,mouseDownEvent:()=>s,mouseLeaveEvent:()=>n,mouseMoveEvent:()=>r,mouseOutEvent:()=>a,mouseUpEvent:()=>o,paintBase:()=>Jt,paintImage:()=>Zt,parseAlpha:()=>H,randomInRange:()=>z,rangeColorToHsl:()=>Dt,rangeColorToRgb:()=>Tt,rectBounce:()=>ht,resizeEvent:()=>u,rgbToHsl:()=>Et,safeMatchMedia:()=>X,safeMutationObserver:()=>Y,setLogger:()=>U,setRandom:()=>x,setRangeValue:()=>O,singleDivModeExecute:()=>at,stringToAlpha:()=>Lt,stringToRgb:()=>At,touchCancelEvent:()=>d,touchEndEvent:()=>l,touchMoveEvent:()=>h,touchStartEvent:()=>c,tsParticles:()=>qi,visibilityChangeEvent:()=>p});const i="generated",s="pointerdown",o="pointerup",n="pointerleave",a="pointerout",r="pointermove",c="touchstart",l="touchend",h="touchmove",d="touchcancel",u="resize",p="visibilitychange",f="tsParticles - Error";class v{constructor(t,e,i){if(this._updateFromAngle=(t,e)=>{this.x=Math.cos(t)*e,this.y=Math.sin(t)*e},!wt(t)&&t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:0}else{if(void 0===t||void 0===e)throw new Error(`${f} Vector3d not initialized correctly`);this.x=t,this.y=e,this.z=i??0}}static get origin(){return v.create(0,0,0)}get angle(){return Math.atan2(this.y,this.x)}set angle(t){this._updateFromAngle(t,this.length)}get length(){return Math.sqrt(this.getLengthSq())}set length(t){this._updateFromAngle(this.angle,t)}static clone(t){return v.create(t.x,t.y,t.z)}static create(t,e,i){return new v(t,e,i)}add(t){return v.create(this.x+t.x,this.y+t.y,this.z+t.z)}addTo(t){this.x+=t.x,this.y+=t.y,this.z+=t.z}copy(){return v.clone(this)}distanceTo(t){return this.sub(t).length}distanceToSq(t){return this.sub(t).getLengthSq()}div(t){return v.create(this.x/t,this.y/t,this.z/t)}divTo(t){this.x/=t,this.y/=t,this.z/=t}getLengthSq(){return this.x**2+this.y**2}mult(t){return v.create(this.x*t,this.y*t,this.z*t)}multTo(t){this.x*=t,this.y*=t,this.z*=t}normalize(){const t=this.length;0!=t&&this.multTo(1/t)}rotate(t){return v.create(this.x*Math.cos(t)-this.y*Math.sin(t),this.x*Math.sin(t)+this.y*Math.cos(t),0)}setTo(t){this.x=t.x,this.y=t.y;const e=t;this.z=e.z?e.z:0}sub(t){return v.create(this.x-t.x,this.y-t.y,this.z-t.z)}subFrom(t){this.x-=t.x,this.y-=t.y,this.z-=t.z}}class m extends v{constructor(t,e){super(t,e,0)}static get origin(){return m.create(0,0)}static clone(t){return m.create(t.x,t.y)}static create(t,e){return new m(t,e)}}let g=Math.random;const y=new Map;function b(t,e){y.get(t)||y.set(t,e)}function w(t){return y.get(t)||(t=>t)}function x(t=Math.random){g=t}function _(){return k(g(),0,1-1e-16)}function k(t,e,i){return Math.min(Math.max(t,e),i)}function M(t,e,i,s){return Math.floor((t*i+e*s)/(i+s))}function z(t){const e=S(t);let i=P(t);return e===i&&(i=0),_()*(e-i)+i}function C(t){return wt(t)?t:z(t)}function P(t){return wt(t)?t:t.min}function S(t){return wt(t)?t:t.max}function O(t,e){if(t===e||void 0===e&&wt(t))return t;const i=P(t),s=S(t);return void 0!==e?{min:Math.min(i,e),max:Math.max(s,e)}:O(i,s)}function T(t){const e=t.random,{enable:i,minimumValue:s}=yt(e)?{enable:e,minimumValue:0}:e;return C(i?O(t.value,s):t.value)}function R(t,e){const i=t.x-e.x,s=t.y-e.y;return{dx:i,dy:s,distance:Math.sqrt(i**2+s**2)}}function I(t,e){return R(t,e).distance}function D(t,e,i){if(wt(t))return t*Math.PI/180;switch(t){case"top":return-Math.PI/2;case"top-right":return-Math.PI/4;case"right":return 0;case"bottom-right":return Math.PI/4;case"bottom":return Math.PI/2;case"bottom-left":return 3*Math.PI/4;case"left":return Math.PI;case"top-left":return-3*Math.PI/4;case"inside":return Math.atan2(i.y-e.y,i.x-e.x);case"outside":return Math.atan2(e.y-i.y,e.x-i.x);default:return _()*Math.PI*2}}function E(t){const e=m.origin;return e.length=1,e.angle=t,e}function L(t,e,i,s){return m.create(t.x*(i-s)/(i+s)+2*e.x*s/(i+s),t.y)}function A(t){return t.position&&void 0!==t.position.x&&void 0!==t.position.y?{x:t.position.x*t.size.width/100,y:t.position.y*t.size.height/100}:void 0}function B(t){return{x:(t.position?.x??100*_())*t.size.width/100,y:(t.position?.y??100*_())*t.size.height/100}}function F(t){const e={x:void 0!==t.position?.x?C(t.position.x):void 0,y:void 0!==t.position?.y?C(t.position.y):void 0};return B({size:t.size,position:e})}function q(t){return{x:t.position?.x??_()*t.size.width,y:t.position?.y??_()*t.size.height}}function V(t){const e={x:void 0!==t.position?.x?C(t.position.x):void 0,y:void 0!==t.position?.y?C(t.position.y):void 0};return q({size:t.size,position:e})}function H(t){return t?t.endsWith("%")?parseFloat(t)/100:parseFloat(t):1}const W={debug:console.debug,error:console.error,info:console.info,log:console.log,verbose:console.log,warning:console.warn};function U(t){W.debug=t.debug||W.debug,W.error=t.error||W.error,W.info=t.info||W.info,W.log=t.log||W.log,W.verbose=t.verbose||W.verbose,W.warning=t.warning||W.warning}function $(){return W}function G(t){const e={bounced:!1},{pSide:i,pOtherSide:s,rectSide:o,rectOtherSide:n,velocity:a,factor:r}=t;return s.min<n.min||s.min>n.max||s.max<n.min||s.max>n.max||(i.max>=o.min&&i.max<=(o.max+o.min)/2&&a>0||i.min<=o.max&&i.min>(o.max+o.min)/2&&a<0)&&(e.velocity=a*-r,e.bounced=!0),e}function j(){return"undefined"==typeof window||!window||void 0===window.document||!window.document}function N(){return!j()&&"undefined"!=typeof matchMedia}function X(t){if(N())return matchMedia(t)}function Y(t){if(!j()&&"undefined"!=typeof MutationObserver)return new MutationObserver(t)}function J(t,e){return t===e||kt(e)&&e.indexOf(t)>-1}async function Z(t,e){try{await document.fonts.load(`${e??"400"} 36px '${t??"Verdana"}'`)}catch{}}function Q(t){return Math.floor(_()*t.length)}function K(t,e,i=!0){return t[void 0!==e&&i?e%t.length:Q(t)]}function tt(t,e,i,s,o){return et(it(t,s??0),e,i,o)}function et(t,e,i,s){let o=!0;return s&&"bottom"!==s||(o=t.top<e.height+i.x),!o||s&&"left"!==s||(o=t.right>i.x),!o||s&&"right"!==s||(o=t.left<e.width+i.y),!o||s&&"top"!==s||(o=t.bottom>i.y),o}function it(t,e){return{bottom:t.y+e,left:t.x-e,right:t.x+e,top:t.y-e}}function st(t,...e){for(const i of e){if(null==i)continue;if(!_t(i)){t=i;continue}const e=Array.isArray(i);!e||!_t(t)&&t&&Array.isArray(t)?e||!_t(t)&&t&&!Array.isArray(t)||(t={}):t=[];for(const e in i){if("__proto__"===e)continue;const s=i[e],o=t;o[e]=_t(s)&&Array.isArray(s)?s.map((t=>st(o[e],t))):st(o[e],s)}}return t}function ot(t,e){return!!pt(e,(e=>e.enable&&J(t,e.mode)))}function nt(t,e,i){dt(e,(e=>{const s=e.mode;e.enable&&J(t,s)&&at(e,i)}))}function at(t,e){dt(t.selectors,(i=>{e(i,t)}))}function rt(t,e){if(e&&t)return pt(t,(t=>function(t,e){const i=dt(e,(e=>t.matches(e)));return kt(i)?i.some((t=>t)):i}(e,t.selectors)))}function ct(t){return{position:t.getPosition(),radius:t.getRadius(),mass:t.getMass(),velocity:t.velocity,factor:m.create(T(t.options.bounce.horizontal),T(t.options.bounce.vertical))}}function lt(t,e){const{x:i,y:s}=t.velocity.sub(e.velocity),[o,n]=[t.position,e.position],{dx:a,dy:r}=R(n,o);if(i*a+s*r<0)return;const c=-Math.atan2(r,a),l=t.mass,h=e.mass,d=t.velocity.rotate(c),u=e.velocity.rotate(c),p=L(d,u,l,h),f=L(u,d,l,h),v=p.rotate(-c),m=f.rotate(-c);t.velocity.x=v.x*t.factor.x,t.velocity.y=v.y*t.factor.y,e.velocity.x=m.x*e.factor.x,e.velocity.y=m.y*e.factor.y}function ht(t,e){const i=it(t.getPosition(),t.getRadius()),s=G({pSide:{min:i.left,max:i.right},pOtherSide:{min:i.top,max:i.bottom},rectSide:{min:e.left,max:e.right},rectOtherSide:{min:e.top,max:e.bottom},velocity:t.velocity.x,factor:T(t.options.bounce.horizontal)});s.bounced&&(void 0!==s.velocity&&(t.velocity.x=s.velocity),void 0!==s.position&&(t.position.x=s.position));const o=G({pSide:{min:i.top,max:i.bottom},pOtherSide:{min:i.left,max:i.right},rectSide:{min:e.top,max:e.bottom},rectOtherSide:{min:e.left,max:e.right},velocity:t.velocity.y,factor:T(t.options.bounce.vertical)});o.bounced&&(void 0!==o.velocity&&(t.velocity.y=o.velocity),void 0!==o.position&&(t.position.y=o.position))}function dt(t,e){return kt(t)?t.map(((t,i)=>e(t,i))):e(t,0)}function ut(t,e,i){return kt(t)?K(t,e,i):t}function pt(t,e){return kt(t)?t.find(((t,i)=>e(t,i))):e(t,0)?t:void 0}function ft(t,e){const i=t.value,s=t.animation,o={delayTime:1e3*C(s.delay),enable:s.enable,value:C(t.value)*e,max:S(i)*e,min:P(i)*e,loops:0,maxLoops:C(s.count),time:0};if(s.enable){switch(o.decay=1-C(s.decay),s.mode){case"increase":o.status="increasing";break;case"decrease":o.status="decreasing";break;case"random":o.status=_()>=.5?"increasing":"decreasing"}const t="auto"===s.mode;switch(s.startValue){case"min":o.value=o.min,t&&(o.status="increasing");break;case"max":o.value=o.max,t&&(o.status="decreasing");break;default:o.value=z(o),t&&(o.status=_()>=.5?"increasing":"decreasing")}}return o.initialValue=o.value,o}function vt(t,e){if(!("percent"===t.mode)){const{mode:e,...i}=t;return i}return"x"in t?{x:t.x/100*e.width,y:t.y/100*e.height}:{width:t.width/100*e.width,height:t.height/100*e.height}}function mt(t,e){return vt(t,e)}function gt(t,e){return vt(t,e)}function yt(t){return"boolean"==typeof t}function bt(t){return"string"==typeof t}function wt(t){return"number"==typeof t}function xt(t){return"function"==typeof t}function _t(t){return"object"==typeof t&&null!==t}function kt(t){return Array.isArray(t)}const Mt="random",zt="mid",Ct=new Map;function Pt(t){Ct.set(t.key,t)}function St(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function Ot(t){for(const[,e]of Ct)if(t.startsWith(e.stringPrefix))return e.parseString(t);const e=t.replace(/^#?([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,((t,e,i,s,o)=>e+e+i+i+s+s+(void 0!==o?o+o:""))),i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i.exec(e);return i?{a:void 0!==i[4]?parseInt(i[4],16)/255:1,b:parseInt(i[3],16),g:parseInt(i[2],16),r:parseInt(i[1],16)}:void 0}function Tt(t,e,i=!0){if(!t)return;const s=bt(t)?{value:t}:t;if(bt(s.value))return Rt(s.value,e,i);if(kt(s.value))return Tt({value:K(s.value,e,i)});for(const[,t]of Ct){const e=t.handleRangeColor(s);if(e)return e}}function Rt(t,e,i=!0){if(!t)return;const s=bt(t)?{value:t}:t;if(bt(s.value))return s.value===Mt?qt():At(s.value);if(kt(s.value))return Rt({value:K(s.value,e,i)});for(const[,t]of Ct){const e=t.handleColor(s);if(e)return e}}function It(t,e,i=!0){const s=Rt(t,e,i);return s?Et(s):void 0}function Dt(t,e,i=!0){const s=Tt(t,e,i);return s?Et(s):void 0}function Et(t){const e=t.r/255,i=t.g/255,s=t.b/255,o=Math.max(e,i,s),n=Math.min(e,i,s),a={h:0,l:(o+n)/2,s:0};return o!==n&&(a.s=a.l<.5?(o-n)/(o+n):(o-n)/(2-o-n),a.h=e===o?(i-s)/(o-n):a.h=i===o?2+(s-e)/(o-n):4+(e-i)/(o-n)),a.l*=100,a.s*=100,a.h*=60,a.h<0&&(a.h+=360),a.h>=360&&(a.h-=360),a}function Lt(t){return Ot(t)?.a}function At(t){return Ot(t)}function Bt(t){const e={b:0,g:0,r:0},i={h:t.h/360,l:t.l/100,s:t.s/100};if(i.s){const t=i.l<.5?i.l*(1+i.s):i.l+i.s-i.l*i.s,s=2*i.l-t;e.r=St(s,t,i.h+1/3),e.g=St(s,t,i.h),e.b=St(s,t,i.h-1/3)}else e.r=e.g=e.b=i.l;return e.r=Math.floor(255*e.r),e.g=Math.floor(255*e.g),e.b=Math.floor(255*e.b),e}function Ft(t){const e=Bt(t);return{a:t.a,b:e.b,g:e.g,r:e.r}}function qt(t){const e=t??0;return{b:Math.floor(z(O(e,256))),g:Math.floor(z(O(e,256))),r:Math.floor(z(O(e,256)))}}function Vt(t,e){return`rgba(${t.r}, ${t.g}, ${t.b}, ${e??1})`}function Ht(t,e){return`hsla(${t.h}, ${t.s}%, ${t.l}%, ${e??1})`}function Wt(t,e,i,s){let o=t,n=e;return void 0===o.r&&(o=Bt(t)),void 0===n.r&&(n=Bt(e)),{b:M(o.b,n.b,i,s),g:M(o.g,n.g,i,s),r:M(o.r,n.r,i,s)}}function Ut(t,e,i){if(i===Mt)return qt();if(i!==zt)return i;{const i=t.getFillColor()??t.getStrokeColor(),s=e?.getFillColor()??e?.getStrokeColor();if(i&&s&&e)return Wt(i,s,t.getRadius(),e.getRadius());{const t=i??s;if(t)return Bt(t)}}}function $t(t,e,i){const s=bt(t)?t:t.value;return s===Mt?i?Tt({value:s}):e?Mt:zt:s===zt?zt:Tt({value:s})}function Gt(t){return void 0!==t?{h:t.h.value,s:t.s.value,l:t.l.value}:void 0}function jt(t,e,i){const s={h:{enable:!1,value:t.h},s:{enable:!1,value:t.s},l:{enable:!1,value:t.l}};return e&&(Nt(s.h,e.h,i),Nt(s.s,e.s,i),Nt(s.l,e.l,i)),s}function Nt(t,e,i){t.enable=e.enable,t.enable?(t.velocity=C(e.speed)/100*i,t.decay=1-C(e.decay),t.status="increasing",t.loops=0,t.maxLoops=C(e.count),t.time=0,t.delayTime=1e3*C(e.delay),e.sync||(t.velocity*=_(),t.value*=_()),t.initialValue=t.value):t.velocity=0}function Xt(t,e,i){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.closePath()}function Yt(t,e,i,s){t.beginPath(),t.moveTo(e.x,e.y),t.lineTo(i.x,i.y),t.lineTo(s.x,s.y),t.closePath()}function Jt(t,e,i){t.fillStyle=i??"rgba(0,0,0,0)",t.fillRect(0,0,e.width,e.height)}function Zt(t,e,i,s){i&&(t.globalAlpha=s,t.drawImage(i,0,0,e.width,e.height),t.globalAlpha=1)}function Qt(t,e){t.clearRect(0,0,e.width,e.height)}function Kt(t){const{container:e,context:i,particle:s,delta:o,colorStyles:n,backgroundMask:a,composite:r,radius:c,opacity:l,shadow:h,transform:d}=t,u=s.getPosition(),p=s.rotation+(s.pathRotation?s.velocity.angle:0),f=Math.sin(p),v=Math.cos(p),m={a:v*(d.a??1),b:f*(d.b??1),c:-f*(d.c??1),d:v*(d.d??1)};i.setTransform(m.a,m.b,m.c,m.d,u.x,u.y),i.beginPath(),a&&(i.globalCompositeOperation=r);const g=s.shadowColor;h.enable&&g&&(i.shadowBlur=h.blur,i.shadowColor=Vt(g),i.shadowOffsetX=h.offset.x,i.shadowOffsetY=h.offset.y),n.fill&&(i.fillStyle=n.fill);const y=s.strokeWidth??0;i.lineWidth=y,n.stroke&&(i.strokeStyle=n.stroke),te(e,i,s,c,l,o),y>0&&i.stroke(),s.close&&i.closePath(),s.fill&&i.fill(),ee(e,i,s,c,l,o),i.globalCompositeOperation="source-over",i.setTransform(1,0,0,1,0,0)}function te(t,e,i,s,o,n){if(!i.shape)return;const a=t.drawers.get(i.shape);a&&a.draw(e,i,s,o,n,t.retina.pixelRatio)}function ee(t,e,i,s,o,n){if(!i.shape)return;const a=t.drawers.get(i.shape);a&&a.afterEffect&&a.afterEffect(e,i,s,o,n,t.retina.pixelRatio)}function ie(t,e,i){e.draw&&e.draw(t,i)}function se(t,e,i,s){e.drawParticle&&e.drawParticle(t,i,s)}function oe(t,e,i){return{h:t.h,s:t.s,l:t.l+("darken"===e?-1:1)*i}}function ne(t,e,i){const s=e[i];void 0!==s&&(t[i]=(t[i]??1)*s)}class ae{constructor(t){this.container=t,this._applyPostDrawUpdaters=t=>{for(const e of this._postDrawUpdaters)e.afterDraw&&e.afterDraw(t)},this._applyPreDrawUpdaters=(t,e,i,s,o,n)=>{for(const a of this._preDrawUpdaters){if(a.getColorStyles){const{fill:n,stroke:r}=a.getColorStyles(e,t,i,s);n&&(o.fill=n),r&&(o.stroke=r)}if(a.getTransformValues){const t=a.getTransformValues(e);for(const e in t)ne(n,t,e)}a.beforeDraw&&a.beforeDraw(e)}},this._applyResizePlugins=()=>{for(const t of this._resizePlugins)t.resize&&t.resize()},this._getPluginParticleColors=t=>{let e,i;for(const s of this._colorPlugins)if(!e&&s.particleFillColor&&(e=Dt(s.particleFillColor(t))),!i&&s.particleStrokeColor&&(i=Dt(s.particleStrokeColor(t))),e&&i)break;return[e,i]},this._initCover=()=>{const t=this.container.actualOptions.backgroundMask.cover,e=Tt(t.color);if(e){const i={...e,a:t.opacity};this._coverColorStyle=Vt(i,i.a)}},this._initStyle=()=>{const t=this.element,e=this.container.actualOptions;if(t){this._fullScreen?(this._originalStyle=st({},t.style),this._setFullScreenStyle()):this._resetOriginalStyle();for(const i in e.style){if(!i||!e.style)continue;const s=e.style[i];s&&t.style.setProperty(i,s,"important")}}},this._initTrail=async()=>{const t=this.container.actualOptions,e=t.particles.move.trail,i=e.fill;if(e.enable)if(i.color){const e=Tt(i.color);if(!e)return;const s=t.particles.move.trail;this._trailFill={color:{...e},opacity:1/s.length}}else await new Promise(((t,s)=>{if(!i.image)return;const o=document.createElement("img");o.addEventListener("load",(()=>{this._trailFill={image:o,opacity:1/e.length},t()})),o.addEventListener("error",(t=>{s(t.error)})),o.src=i.image}))},this._paintBase=t=>{this.draw((e=>Jt(e,this.size,t)))},this._paintImage=(t,e)=>{this.draw((i=>Zt(i,this.size,t,e)))},this._repairStyle=()=>{const t=this.element;t&&(this._safeMutationObserver((t=>t.disconnect())),this._initStyle(),this.initBackground(),this._safeMutationObserver((e=>e.observe(t,{attributes:!0}))))},this._resetOriginalStyle=()=>{const t=this.element,e=this._originalStyle;if(!t||!e)return;const i=t.style;i.position=e.position,i.zIndex=e.zIndex,i.top=e.top,i.left=e.left,i.width=e.width,i.height=e.height},this._safeMutationObserver=t=>{this._mutationObserver&&t(this._mutationObserver)},this._setFullScreenStyle=()=>{const t=this.element;if(!t)return;const e="important",i=t.style;i.setProperty("position","fixed",e),i.setProperty("z-index",this.container.actualOptions.fullScreen.zIndex.toString(10),e),i.setProperty("top","0",e),i.setProperty("left","0",e),i.setProperty("width","100%",e),i.setProperty("height","100%",e)},this.size={height:0,width:0},this._context=null,this._generated=!1,this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}get _fullScreen(){return this.container.actualOptions.fullScreen.enable}clear(){const t=this.container.actualOptions,e=t.particles.move.trail,i=this._trailFill;t.backgroundMask.enable?this.paint():e.enable&&e.length>0&&i?i.color?this._paintBase(Vt(i.color,i.opacity)):i.image&&this._paintImage(i.image,i.opacity):this.draw((t=>{Qt(t,this.size)}))}destroy(){if(this.stop(),this._generated){const t=this.element;t&&t.remove()}else this._resetOriginalStyle();this._preDrawUpdaters=[],this._postDrawUpdaters=[],this._resizePlugins=[],this._colorPlugins=[]}draw(t){const e=this._context;if(e)return t(e)}drawParticle(t,e){if(t.spawning||t.destroyed)return;const i=t.getRadius();if(i<=0)return;const s=t.getFillColor(),o=t.getStrokeColor()??s;let[n,a]=this._getPluginParticleColors(t);n||(n=s),a||(a=o),(n||a)&&this.draw((s=>{const o=this.container,r=o.actualOptions,c=t.options.zIndex,l=(1-t.zIndexFactor)**c.opacityRate,h=t.bubble.opacity??t.opacity?.value??1,d=h*l,u=(t.strokeOpacity??h)*l,p={},f={fill:n?Ht(n,d):void 0};f.stroke=a?Ht(a,u):f.fill,this._applyPreDrawUpdaters(s,t,i,d,f,p),Kt({container:o,context:s,particle:t,delta:e,colorStyles:f,backgroundMask:r.backgroundMask.enable,composite:r.backgroundMask.composite,radius:i*(1-t.zIndexFactor)**c.sizeRate,opacity:d,shadow:t.options.shadow,transform:p}),this._applyPostDrawUpdaters(t)}))}drawParticlePlugin(t,e,i){this.draw((s=>se(s,t,e,i)))}drawPlugin(t,e){this.draw((i=>ie(i,t,e)))}async init(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=Y((t=>{for(const e of t)"attributes"===e.type&&"style"===e.attributeName&&this._repairStyle()})),this.resize(),this._initStyle(),this._initCover();try{await this._initTrail()}catch(t){$().error(t)}this.initBackground(),this._safeMutationObserver((t=>{this.element&&t.observe(this.element,{attributes:!0})})),this.initUpdaters(),this.initPlugins(),this.paint()}initBackground(){const t=this.container.actualOptions.background,e=this.element;if(!e)return;const i=e.style;if(i){if(t.color){const e=Tt(t.color);i.backgroundColor=e?Vt(e,t.opacity):""}else i.backgroundColor="";i.backgroundImage=t.image||"",i.backgroundPosition=t.position||"",i.backgroundRepeat=t.repeat||"",i.backgroundSize=t.size||""}}initPlugins(){this._resizePlugins=[];for(const[,t]of this.container.plugins)t.resize&&this._resizePlugins.push(t),(t.particleFillColor||t.particleStrokeColor)&&this._colorPlugins.push(t)}initUpdaters(){this._preDrawUpdaters=[],this._postDrawUpdaters=[];for(const t of this.container.particles.updaters)t.afterDraw&&this._postDrawUpdaters.push(t),(t.getColorStyles||t.getTransformValues||t.beforeDraw)&&this._preDrawUpdaters.push(t)}loadCanvas(t){this._generated&&this.element&&this.element.remove(),this._generated=t.dataset&&i in t.dataset?"true"===t.dataset[i]:this._generated,this.element=t,this.element.ariaHidden="true",this._originalStyle=st({},this.element.style),this.size.height=t.offsetHeight,this.size.width=t.offsetWidth,this._context=this.element.getContext("2d"),this._safeMutationObserver((t=>{this.element&&t.observe(this.element,{attributes:!0})})),this.container.retina.init(),this.initBackground()}paint(){const t=this.container.actualOptions;this.draw((e=>{t.backgroundMask.enable&&t.backgroundMask.cover?(Qt(e,this.size),this._paintBase(this._coverColorStyle)):this._paintBase()}))}resize(){if(!this.element)return!1;const t=this.container,e=t.retina.pixelRatio,i=t.canvas.size,s=this.element.offsetWidth*e,o=this.element.offsetHeight*e;if(o===i.height&&s===i.width&&o===this.element.height&&s===this.element.width)return!1;const n={...i};return this.element.width=i.width=this.element.offsetWidth*e,this.element.height=i.height=this.element.offsetHeight*e,this.container.started&&(this.resizeFactor={width:i.width/n.width,height:i.height/n.height}),!0}stop(){this._safeMutationObserver((t=>t.disconnect())),this._mutationObserver=void 0,this.draw((t=>Qt(t,this.size)))}async windowResize(){if(!this.element||!this.resize())return;const t=this.container,e=t.updateActualOptions();t.particles.setDensity(),this._applyResizePlugins(),e&&await t.refresh()}}function re(t,e,i,s,o){if(s){let s={passive:!0};yt(o)?s.capture=o:void 0!==o&&(s=o),t.addEventListener(e,i,s)}else{const s=o;t.removeEventListener(e,i,s)}}class ce{constructor(t){this.container=t,this._doMouseTouchClick=t=>{const e=this.container,i=e.actualOptions;if(this._canPush){const t=e.interactivity.mouse,s=t.position;if(!s)return;t.clickPosition={...s},t.clickTime=(new Date).getTime();dt(i.interactivity.events.onClick.mode,(t=>this.container.handleClickMode(t)))}"touchend"===t.type&&setTimeout((()=>this._mouseTouchFinish()),500)},this._handleThemeChange=t=>{const e=t,i=this.container,s=i.options,o=s.defaultThemes,n=e.matches?o.dark:o.light,a=s.themes.find((t=>t.name===n));a&&a.default.auto&&i.loadTheme(n)},this._handleVisibilityChange=()=>{const t=this.container,e=t.actualOptions;this._mouseTouchFinish(),e.pauseOnBlur&&(document&&document.hidden?(t.pageHidden=!0,t.pause()):(t.pageHidden=!1,t.getAnimationStatus()?t.play(!0):t.draw(!0)))},this._handleWindowResize=async()=>{this._resizeTimeout&&(clearTimeout(this._resizeTimeout),delete this._resizeTimeout),this._resizeTimeout=setTimeout((async()=>{const t=this.container.canvas;t&&await t.windowResize()}),1e3*this.container.actualOptions.interactivity.events.resize.delay)},this._manageInteractivityListeners=(t,e)=>{const i=this._handlers,n=this.container,a=n.actualOptions,u=n.interactivity.element;if(!u)return;const p=u,f=n.canvas.element;f&&(f.style.pointerEvents=p===f?"initial":"none"),(a.interactivity.events.onHover.enable||a.interactivity.events.onClick.enable)&&(re(u,r,i.mouseMove,e),re(u,c,i.touchStart,e),re(u,h,i.touchMove,e),a.interactivity.events.onClick.enable?(re(u,l,i.touchEndClick,e),re(u,o,i.mouseUp,e),re(u,s,i.mouseDown,e)):re(u,l,i.touchEnd,e),re(u,t,i.mouseLeave,e),re(u,d,i.touchCancel,e))},this._manageListeners=t=>{const e=this._handlers,i=this.container,s=i.actualOptions.interactivity.detectsOn,o=i.canvas.element;let r=n;"window"===s?(i.interactivity.element=window,r=a):i.interactivity.element="parent"===s&&o?o.parentElement??o.parentNode:o,this._manageMediaMatch(t),this._manageResize(t),this._manageInteractivityListeners(r,t),document&&re(document,p,e.visibilityChange,t,!1)},this._manageMediaMatch=t=>{const e=this._handlers,i=X("(prefers-color-scheme: dark)");i&&(void 0===i.addEventListener?void 0!==i.addListener&&(t?i.addListener(e.oldThemeChange):i.removeListener(e.oldThemeChange)):re(i,"change",e.themeChange,t))},this._manageResize=t=>{const e=this._handlers,i=this.container;if(!i.actualOptions.interactivity.events.resize)return;if("undefined"==typeof ResizeObserver)return void re(window,u,e.resize,t);const s=i.canvas.element;this._resizeObserver&&!t?(s&&this._resizeObserver.unobserve(s),this._resizeObserver.disconnect(),delete this._resizeObserver):!this._resizeObserver&&t&&s&&(this._resizeObserver=new ResizeObserver((async t=>{t.find((t=>t.target===s))&&await this._handleWindowResize()})),this._resizeObserver.observe(s))},this._mouseDown=()=>{const{interactivity:t}=this.container;if(!t)return;const{mouse:e}=t;e.clicking=!0,e.downPosition=e.position},this._mouseTouchClick=t=>{const e=this.container,i=e.actualOptions,{mouse:s}=e.interactivity;s.inside=!0;let o=!1;const n=s.position;if(n&&i.interactivity.events.onClick.enable){for(const[,t]of e.plugins)if(t.clickPositionValid&&(o=t.clickPositionValid(n),o))break;o||this._doMouseTouchClick(t),s.clicking=!1}},this._mouseTouchFinish=()=>{const t=this.container.interactivity;if(!t)return;const e=t.mouse;delete e.position,delete e.clickPosition,delete e.downPosition,t.status=n,e.inside=!1,e.clicking=!1},this._mouseTouchMove=t=>{const e=this.container,i=e.actualOptions,s=e.interactivity,o=e.canvas.element;if(!s||!s.element)return;let n;if(s.mouse.inside=!0,t.type.startsWith("pointer")){this._canPush=!0;const e=t;if(s.element===window){if(o){const t=o.getBoundingClientRect();n={x:e.clientX-t.left,y:e.clientY-t.top}}}else if("parent"===i.interactivity.detectsOn){const t=e.target,i=e.currentTarget;if(t&&i&&o){const s=t.getBoundingClientRect(),a=i.getBoundingClientRect(),r=o.getBoundingClientRect();n={x:e.offsetX+2*s.left-(a.left+r.left),y:e.offsetY+2*s.top-(a.top+r.top)}}else n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY}}else e.target===o&&(n={x:e.offsetX??e.clientX,y:e.offsetY??e.clientY})}else if(this._canPush="touchmove"!==t.type,o){const e=t,i=e.touches[e.touches.length-1],s=o.getBoundingClientRect();n={x:i.clientX-(s.left??0),y:i.clientY-(s.top??0)}}const a=e.retina.pixelRatio;n&&(n.x*=a,n.y*=a),s.mouse.position=n,s.status=r},this._touchEnd=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchFinish()},this._touchEndClick=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.delete(t.identifier);this._mouseTouchClick(t)},this._touchStart=t=>{const e=t,i=Array.from(e.changedTouches);for(const t of i)this._touches.set(t.identifier,performance.now());this._mouseTouchMove(t)},this._canPush=!0,this._touches=new Map,this._handlers={mouseDown:()=>this._mouseDown(),mouseLeave:()=>this._mouseTouchFinish(),mouseMove:t=>this._mouseTouchMove(t),mouseUp:t=>this._mouseTouchClick(t),touchStart:t=>this._touchStart(t),touchMove:t=>this._mouseTouchMove(t),touchEnd:t=>this._touchEnd(t),touchCancel:t=>this._touchEnd(t),touchEndClick:t=>this._touchEndClick(t),visibilityChange:()=>this._handleVisibilityChange(),themeChange:t=>this._handleThemeChange(t),oldThemeChange:t=>this._handleThemeChange(t),resize:()=>{this._handleWindowResize()}}}addListeners(){this._manageListeners(!0)}removeListeners(){this._manageListeners(!1)}}class le{constructor(){this.value=""}static create(t,e){const i=new le;return i.load(t),void 0!==e&&(bt(e)||kt(e)?i.load({value:e}):i.load(e)),i}load(t){void 0!==t?.value&&(this.value=t.value)}}class he{constructor(){this.color=new le,this.color.value="",this.image="",this.position="",this.repeat="",this.size="",this.opacity=1}load(t){t&&(void 0!==t.color&&(this.color=le.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image),void 0!==t.position&&(this.position=t.position),void 0!==t.repeat&&(this.repeat=t.repeat),void 0!==t.size&&(this.size=t.size),void 0!==t.opacity&&(this.opacity=t.opacity))}}class de{constructor(){this.color=new le,this.color.value="#fff",this.opacity=1}load(t){t&&(void 0!==t.color&&(this.color=le.create(this.color,t.color)),void 0!==t.opacity&&(this.opacity=t.opacity))}}class ue{constructor(){this.composite="destination-out",this.cover=new de,this.enable=!1}load(t){if(t){if(void 0!==t.composite&&(this.composite=t.composite),void 0!==t.cover){const e=t.cover,i=bt(t.cover)?{color:t.cover}:t.cover;this.cover.load(void 0!==e.color?e:{color:i})}void 0!==t.enable&&(this.enable=t.enable)}}}class pe{constructor(){this.enable=!0,this.zIndex=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.zIndex&&(this.zIndex=t.zIndex))}}class fe{constructor(){this.enable=!1,this.mode=[]}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode))}}class ve{constructor(){this.selectors=[],this.enable=!1,this.mode=[],this.type="circle"}get el(){return this.elementId}set el(t){this.elementId=t}get elementId(){return this.ids}set elementId(t){this.ids=t}get ids(){return dt(this.selectors,(t=>t.replace("#","")))}set ids(t){this.selectors=dt(t,(t=>`#${t}`))}load(t){if(!t)return;const e=t.ids??t.elementId??t.el;void 0!==e&&(this.ids=e),void 0!==t.selectors&&(this.selectors=t.selectors),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.type&&(this.type=t.type)}}class me{constructor(){this.enable=!1,this.force=2,this.smooth=10}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.force&&(this.force=t.force),void 0!==t.smooth&&(this.smooth=t.smooth))}}class ge{constructor(){this.enable=!1,this.mode=[],this.parallax=new me}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.mode&&(this.mode=t.mode),this.parallax.load(t.parallax))}}class ye{constructor(){this.delay=.5,this.enable=!0}load(t){void 0!==t&&(void 0!==t.delay&&(this.delay=t.delay),void 0!==t.enable&&(this.enable=t.enable))}}class be{constructor(){this.onClick=new fe,this.onDiv=new ve,this.onHover=new ge,this.resize=new ye}get onclick(){return this.onClick}set onclick(t){this.onClick=t}get ondiv(){return this.onDiv}set ondiv(t){this.onDiv=t}get onhover(){return this.onHover}set onhover(t){this.onHover=t}load(t){if(!t)return;this.onClick.load(t.onClick??t.onclick);const e=t.onDiv??t.ondiv;void 0!==e&&(this.onDiv=dt(e,(t=>{const e=new ve;return e.load(t),e}))),this.onHover.load(t.onHover??t.onhover),yt(t.resize)?this.resize.enable=t.resize:this.resize.load(t.resize)}}class we{constructor(t,e){this._engine=t,this._container=e}load(t){if(!t)return;if(!this._container)return;const e=this._engine.plugins.interactors.get(this._container);if(e)for(const i of e)i.loadModeOptions&&i.loadModeOptions(this,t)}}class xe{constructor(t,e){this.detectsOn="window",this.events=new be,this.modes=new we(t,e)}get detect_on(){return this.detectsOn}set detect_on(t){this.detectsOn=t}load(t){if(!t)return;const e=t.detectsOn??t.detect_on;void 0!==e&&(this.detectsOn=e),this.events.load(t.events),this.modes.load(t.modes)}}class _e{load(t){t&&(t.position&&(this.position={x:t.position.x??50,y:t.position.y??50,mode:t.position.mode??"percent"}),t.options&&(this.options=st({},t.options)))}}class ke{constructor(){this.maxWidth=1/0,this.options={},this.mode="canvas"}load(t){t&&(void 0!==t.maxWidth&&(this.maxWidth=t.maxWidth),void 0!==t.mode&&("screen"===t.mode?this.mode="screen":this.mode="canvas"),void 0!==t.options&&(this.options=st({},t.options)))}}class Me{constructor(){this.auto=!1,this.mode="any",this.value=!1}load(t){t&&(void 0!==t.auto&&(this.auto=t.auto),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.value&&(this.value=t.value))}}class ze{constructor(){this.name="",this.default=new Me}load(t){t&&(void 0!==t.name&&(this.name=t.name),this.default.load(t.default),void 0!==t.options&&(this.options=st({},t.options)))}}class Ce{constructor(){this.count=0,this.enable=!1,this.offset=0,this.speed=1,this.delay=0,this.decay=0,this.sync=!0}load(t){t&&(void 0!==t.count&&(this.count=O(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(this.offset=O(t.offset)),void 0!==t.speed&&(this.speed=O(t.speed)),void 0!==t.decay&&(this.decay=O(t.decay)),void 0!==t.delay&&(this.delay=O(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class Pe{constructor(){this.h=new Ce,this.s=new Ce,this.l=new Ce}load(t){t&&(this.h.load(t.h),this.s.load(t.s),this.l.load(t.l))}}class Se extends le{constructor(){super(),this.animation=new Pe}static create(t,e){const i=new Se;return i.load(t),void 0!==e&&(bt(e)||kt(e)?i.load({value:e}):i.load(e)),i}load(t){if(super.load(t),!t)return;const e=t.animation;void 0!==e&&(void 0!==e.enable?this.animation.h.load(e):this.animation.load(t.animation))}}class Oe{constructor(){this.speed=2}load(t){t&&void 0!==t.speed&&(this.speed=t.speed)}}class Te{constructor(){this.enable=!0,this.retries=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.retries&&(this.retries=t.retries))}}class Re{constructor(){this.count=0,this.enable=!1,this.speed=1,this.decay=0,this.delay=0,this.sync=!1}load(t){t&&(void 0!==t.count&&(this.count=O(t.count)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=O(t.speed)),void 0!==t.decay&&(this.decay=O(t.decay)),void 0!==t.delay&&(this.delay=O(t.delay)),void 0!==t.sync&&(this.sync=t.sync))}}class Ie extends Re{constructor(){super(),this.mode="auto",this.startValue="random"}load(t){super.load(t),t&&(void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.startValue&&(this.startValue=t.startValue))}}class De{constructor(){this.enable=!1,this.minimumValue=0}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.minimumValue&&(this.minimumValue=t.minimumValue))}}class Ee{constructor(){this.random=new De,this.value=0}load(t){t&&(yt(t.random)?this.random.enable=t.random:this.random.load(t.random),void 0!==t.value&&(this.value=O(t.value,this.random.enable?this.random.minimumValue:void 0)))}}class Le extends Ee{constructor(){super(),this.animation=new Re}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(super.load(t),!t)return;const e=t.animation??t.anim;void 0!==e&&this.animation.load(e)}}class Ae extends Le{constructor(){super(),this.animation=new Ie}load(t){if(super.load(t),!t)return;void 0!==(t.animation??t.anim)&&(this.value=O(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class Be extends Ee{constructor(){super(),this.random.minimumValue=.1,this.value=1}}class Fe{constructor(){this.horizontal=new Be,this.vertical=new Be}load(t){t&&(this.horizontal.load(t.horizontal),this.vertical.load(t.vertical))}}class qe{constructor(){this.absorb=new Oe,this.bounce=new Fe,this.enable=!1,this.maxSpeed=50,this.mode="bounce",this.overlap=new Te}load(t){t&&(this.absorb.load(t.absorb),this.bounce.load(t.bounce),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.maxSpeed&&(this.maxSpeed=O(t.maxSpeed)),void 0!==t.mode&&(this.mode=t.mode),this.overlap.load(t.overlap))}}class Ve{constructor(){this.offset=0,this.value=90}load(t){t&&(void 0!==t.offset&&(this.offset=O(t.offset)),void 0!==t.value&&(this.value=O(t.value)))}}class He{constructor(){this.distance=200,this.enable=!1,this.rotate={x:3e3,y:3e3}}get rotateX(){return this.rotate.x}set rotateX(t){this.rotate.x=t}get rotateY(){return this.rotate.y}set rotateY(t){this.rotate.y=t}load(t){if(!t)return;void 0!==t.distance&&(this.distance=O(t.distance)),void 0!==t.enable&&(this.enable=t.enable);const e=t.rotate?.x??t.rotateX;void 0!==e&&(this.rotate.x=e);const i=t.rotate?.y??t.rotateY;void 0!==i&&(this.rotate.y=i)}}class We{constructor(){this.x=50,this.y=50,this.mode="percent",this.radius=0}load(t){t&&(void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.mode&&(this.mode=t.mode),void 0!==t.radius&&(this.radius=t.radius))}}class Ue{constructor(){this.acceleration=9.81,this.enable=!1,this.inverse=!1,this.maxSpeed=50}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=O(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.inverse&&(this.inverse=t.inverse),void 0!==t.maxSpeed&&(this.maxSpeed=O(t.maxSpeed)))}}class $e{constructor(){this.clamp=!0,this.delay=new Ee,this.enable=!1,this.options={}}load(t){t&&(void 0!==t.clamp&&(this.clamp=t.clamp),this.delay.load(t.delay),void 0!==t.enable&&(this.enable=t.enable),this.generator=t.generator,t.options&&(this.options=st(this.options,t.options)))}}class Ge{load(t){t&&(void 0!==t.color&&(this.color=le.create(this.color,t.color)),void 0!==t.image&&(this.image=t.image))}}class je{constructor(){this.enable=!1,this.length=10,this.fill=new Ge}get fillColor(){return this.fill.color}set fillColor(t){this.fill.load({color:t})}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0===t.fill&&void 0===t.fillColor||this.fill.load(t.fill||{color:t.fillColor}),void 0!==t.length&&(this.length=t.length))}}class Ne{constructor(){this.default="out"}load(t){t&&(void 0!==t.default&&(this.default=t.default),this.bottom=t.bottom??t.default,this.left=t.left??t.default,this.right=t.right??t.default,this.top=t.top??t.default)}}class Xe{constructor(){this.acceleration=0,this.enable=!1}load(t){t&&(void 0!==t.acceleration&&(this.acceleration=O(t.acceleration)),void 0!==t.enable&&(this.enable=t.enable),t.position&&(this.position=st({},t.position)))}}class Ye{constructor(){this.angle=new Ve,this.attract=new He,this.center=new We,this.decay=0,this.distance={},this.direction="none",this.drift=0,this.enable=!1,this.gravity=new Ue,this.path=new $e,this.outModes=new Ne,this.random=!1,this.size=!1,this.speed=2,this.spin=new Xe,this.straight=!1,this.trail=new je,this.vibrate=!1,this.warp=!1}get bounce(){return this.collisions}set bounce(t){this.collisions=t}get collisions(){return!1}set collisions(t){}get noise(){return this.path}set noise(t){this.path=t}get outMode(){return this.outModes.default}set outMode(t){this.outModes.default=t}get out_mode(){return this.outMode}set out_mode(t){this.outMode=t}load(t){if(!t)return;this.angle.load(wt(t.angle)?{value:t.angle}:t.angle),this.attract.load(t.attract),this.center.load(t.center),void 0!==t.decay&&(this.decay=O(t.decay)),void 0!==t.direction&&(this.direction=t.direction),void 0!==t.distance&&(this.distance=wt(t.distance)?{horizontal:t.distance,vertical:t.distance}:{...t.distance}),void 0!==t.drift&&(this.drift=O(t.drift)),void 0!==t.enable&&(this.enable=t.enable),this.gravity.load(t.gravity);const e=t.outModes??t.outMode??t.out_mode;void 0!==e&&(_t(e)?this.outModes.load(e):this.outModes.load({default:e})),this.path.load(t.path??t.noise),void 0!==t.random&&(this.random=t.random),void 0!==t.size&&(this.size=t.size),void 0!==t.speed&&(this.speed=O(t.speed)),this.spin.load(t.spin),void 0!==t.straight&&(this.straight=t.straight),this.trail.load(t.trail),void 0!==t.vibrate&&(this.vibrate=t.vibrate),void 0!==t.warp&&(this.warp=t.warp)}}class Je extends Ie{constructor(){super(),this.destroy="none",this.speed=2}get opacity_min(){return this.minimumValue}set opacity_min(t){this.minimumValue=t}load(t){void 0!==t?.opacity_min&&void 0===t.minimumValue&&(t.minimumValue=t.opacity_min),super.load(t),t&&void 0!==t.destroy&&(this.destroy=t.destroy)}}class Ze extends Ee{constructor(){super(),this.animation=new Je,this.random.minimumValue=.1,this.value=1}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(!t)return;super.load(t);const e=t.animation??t.anim;void 0!==e&&(this.animation.load(e),this.value=O(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class Qe{constructor(){this.enable=!1,this.width=1920,this.height=1080}get area(){return this.width}set area(t){this.width=t}get factor(){return this.height}set factor(t){this.height=t}get value_area(){return this.area}set value_area(t){this.area=t}load(t){if(!t)return;void 0!==t.enable&&(this.enable=t.enable);const e=t.width??t.area??t.value_area;void 0!==e&&(this.width=e);const i=t.height??t.factor;void 0!==i&&(this.height=i)}}class Ke{constructor(){this.density=new Qe,this.limit=0,this.value=0}get max(){return this.limit}set max(t){this.limit=t}load(t){if(!t)return;this.density.load(t.density);const e=t.limit??t.max;void 0!==e&&(this.limit=e),void 0!==t.value&&(this.value=t.value)}}class ti{constructor(){this.blur=0,this.color=new le,this.enable=!1,this.offset={x:0,y:0},this.color.value="#000"}load(t){t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=le.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.offset&&(void 0!==t.offset.x&&(this.offset.x=t.offset.x),void 0!==t.offset.y&&(this.offset.y=t.offset.y)))}}const ei="character",ii="char",si="image",oi="images",ni="polygon",ai="star";class ri{constructor(){this.loadShape=(t,e,i,s)=>{if(!t)return;const o=kt(t),n=o?[]:{},a=o!==kt(this.options[e]),r=o!==kt(this.options[i]);a&&(this.options[e]=n),r&&s&&(this.options[i]=n),this.options[e]=st(this.options[e]??n,t),this.options[i]&&!s||(this.options[i]=st(this.options[i]??n,t))},this.close=!0,this.fill=!0,this.options={},this.type="circle"}get character(){return this.options[ei]??this.options[ii]}set character(t){this.options[ii]=this.options[ei]=t}get custom(){return this.options}set custom(t){this.options=t}get image(){return this.options[si]??this.options[oi]}set image(t){this.options[oi]=this.options[si]=t}get images(){return this.image}set images(t){this.image=t}get polygon(){return this.options[ni]??this.options[ai]}set polygon(t){this.options[ai]=this.options[ni]=t}get stroke(){return[]}set stroke(t){}load(t){if(!t)return;const e=t.options??t.custom;if(void 0!==e)for(const t in e){const i=e[t];i&&(this.options[t]=st(this.options[t]??{},i))}this.loadShape(t.character,ei,ii,!0),this.loadShape(t.polygon,ni,ai,!1),this.loadShape(t.image??t.images,si,oi,!0),void 0!==t.close&&(this.close=t.close),void 0!==t.fill&&(this.fill=t.fill),void 0!==t.type&&(this.type=t.type)}}class ci extends Ie{constructor(){super(),this.destroy="none",this.speed=5}get size_min(){return this.minimumValue}set size_min(t){this.minimumValue=t}load(t){void 0!==t?.size_min&&void 0===t.minimumValue&&(t.minimumValue=t.size_min),super.load(t),t&&void 0!==t.destroy&&(this.destroy=t.destroy)}}class li extends Ee{constructor(){super(),this.animation=new ci,this.random.minimumValue=1,this.value=3}get anim(){return this.animation}set anim(t){this.animation=t}load(t){if(super.load(t),!t)return;const e=t.animation??t.anim;void 0!==e&&(this.animation.load(e),this.value=O(this.value,this.animation.enable?this.animation.minimumValue:void 0))}}class hi{constructor(){this.width=0}load(t){t&&(void 0!==t.color&&(this.color=Se.create(this.color,t.color)),void 0!==t.width&&(this.width=O(t.width)),void 0!==t.opacity&&(this.opacity=O(t.opacity)))}}class di extends Ee{constructor(){super(),this.opacityRate=1,this.sizeRate=1,this.velocityRate=1}load(t){super.load(t),t&&(void 0!==t.opacityRate&&(this.opacityRate=t.opacityRate),void 0!==t.sizeRate&&(this.sizeRate=t.sizeRate),void 0!==t.velocityRate&&(this.velocityRate=t.velocityRate))}}class ui{constructor(t,e){this._engine=t,this._container=e,this.bounce=new Fe,this.collisions=new qe,this.color=new Se,this.color.value="#fff",this.groups={},this.move=new Ye,this.number=new Ke,this.opacity=new Ze,this.reduceDuplicates=!1,this.shadow=new ti,this.shape=new ri,this.size=new li,this.stroke=new hi,this.zIndex=new di}load(t){if(!t)return;if(this.bounce.load(t.bounce),this.color.load(Se.create(this.color,t.color)),void 0!==t.groups)for(const e in t.groups){const i=t.groups[e];void 0!==i&&(this.groups[e]=st(this.groups[e]??{},i))}this.move.load(t.move),this.number.load(t.number),this.opacity.load(t.opacity),void 0!==t.reduceDuplicates&&(this.reduceDuplicates=t.reduceDuplicates),this.shape.load(t.shape),this.size.load(t.size),this.shadow.load(t.shadow),this.zIndex.load(t.zIndex);const e=t.move?.collisions??t.move?.bounce;void 0!==e&&(this.collisions.enable=e),this.collisions.load(t.collisions),void 0!==t.interactivity&&(this.interactivity=st({},t.interactivity));const i=t.stroke??t.shape?.stroke;if(i&&(this.stroke=dt(i,(t=>{const e=new hi;return e.load(t),e}))),this._container){const e=this._engine.plugins.updaters.get(this._container);if(e)for(const i of e)i.loadOptions&&i.loadOptions(this,t);const i=this._engine.plugins.interactors.get(this._container);if(i)for(const e of i)e.loadParticlesOptions&&e.loadParticlesOptions(this,t)}}}function pi(t,...e){for(const i of e)t.load(i)}function fi(t,e,...i){const s=new ui(t,e);return pi(s,...i),s}class vi{constructor(t,e){this._findDefaultTheme=t=>this.themes.find((e=>e.default.value&&e.default.mode===t))??this.themes.find((t=>t.default.value&&"any"===t.default.mode)),this._importPreset=t=>{this.load(this._engine.plugins.getPreset(t))},this._engine=t,this._container=e,this.autoPlay=!0,this.background=new he,this.backgroundMask=new ue,this.defaultThemes={},this.delay=0,this.fullScreen=new pe,this.detectRetina=!0,this.duration=0,this.fpsLimit=120,this.interactivity=new xe(t,e),this.manualParticles=[],this.particles=fi(this._engine,this._container),this.pauseOnBlur=!0,this.pauseOnOutsideViewport=!0,this.responsive=[],this.smooth=!1,this.style={},this.themes=[],this.zLayers=100}get backgroundMode(){return this.fullScreen}set backgroundMode(t){this.fullScreen.load(t)}get fps_limit(){return this.fpsLimit}set fps_limit(t){this.fpsLimit=t}get retina_detect(){return this.detectRetina}set retina_detect(t){this.detectRetina=t}load(t){if(!t)return;void 0!==t.preset&&dt(t.preset,(t=>this._importPreset(t))),void 0!==t.autoPlay&&(this.autoPlay=t.autoPlay),void 0!==t.delay&&(this.delay=O(t.delay));const e=t.detectRetina??t.retina_detect;void 0!==e&&(this.detectRetina=e),void 0!==t.duration&&(this.duration=O(t.duration));const i=t.fpsLimit??t.fps_limit;void 0!==i&&(this.fpsLimit=i),void 0!==t.pauseOnBlur&&(this.pauseOnBlur=t.pauseOnBlur),void 0!==t.pauseOnOutsideViewport&&(this.pauseOnOutsideViewport=t.pauseOnOutsideViewport),void 0!==t.zLayers&&(this.zLayers=t.zLayers),this.background.load(t.background);const s=t.fullScreen??t.backgroundMode;yt(s)?this.fullScreen.enable=s:this.fullScreen.load(s),this.backgroundMask.load(t.backgroundMask),this.interactivity.load(t.interactivity),t.manualParticles&&(this.manualParticles=t.manualParticles.map((t=>{const e=new _e;return e.load(t),e}))),this.particles.load(t.particles),this.style=st(this.style,t.style),this._engine.plugins.loadOptions(this,t),void 0!==t.smooth&&(this.smooth=t.smooth);const o=this._engine.plugins.interactors.get(this._container);if(o)for(const e of o)e.loadOptions&&e.loadOptions(this,t);if(void 0!==t.responsive)for(const e of t.responsive){const t=new ke;t.load(e),this.responsive.push(t)}if(this.responsive.sort(((t,e)=>t.maxWidth-e.maxWidth)),void 0!==t.themes)for(const e of t.themes){const t=this.themes.find((t=>t.name===e.name));if(t)t.load(e);else{const t=new ze;t.load(e),this.themes.push(t)}}this.defaultThemes.dark=this._findDefaultTheme("dark")?.name,this.defaultThemes.light=this._findDefaultTheme("light")?.name}setResponsive(t,e,i){this.load(i);const s=this.responsive.find((i=>"screen"===i.mode&&screen?i.maxWidth>screen.availWidth:i.maxWidth*e>t));return this.load(s?.options),s?.maxWidth}setTheme(t){if(t){const e=this.themes.find((e=>e.name===t));e&&this.load(e.options)}else{const t=X("(prefers-color-scheme: dark)"),e=t&&t.matches,i=this._findDefaultTheme(e?"dark":"light");i&&this.load(i.options)}}}class mi{constructor(t,e){this.container=e,this._engine=t,this._interactors=t.plugins.getInteractors(this.container,!0),this._externalInteractors=[],this._particleInteractors=[]}async externalInteract(t){for(const e of this._externalInteractors)e.isEnabled()&&await e.interact(t)}handleClickMode(t){for(const e of this._externalInteractors)e.handleClickMode&&e.handleClickMode(t)}init(){this._externalInteractors=[],this._particleInteractors=[];for(const t of this._interactors){switch(t.type){case"external":this._externalInteractors.push(t);break;case"particles":this._particleInteractors.push(t)}t.init()}}async particlesInteract(t,e){for(const i of this._externalInteractors)i.clear(t,e);for(const i of this._particleInteractors)i.isEnabled(t)&&await i.interact(t,e)}async reset(t){for(const e of this._externalInteractors)e.isEnabled()&&e.reset(t);for(const e of this._particleInteractors)e.isEnabled(t)&&e.reset(t)}}const gi=t=>{if(!J(t.outMode,t.checkModes))return;const e=2*t.radius;t.coord>t.maxCoord-e?t.setCb(-t.radius):t.coord<e&&t.setCb(t.radius)};class yi{constructor(t,e,i,s,o,n){this.container=i,this._calcPosition=(t,e,i,s=0)=>{for(const[,s]of t.plugins){const t=void 0!==s.particlePosition?s.particlePosition(e,this):void 0;if(t)return v.create(t.x,t.y,i)}const o=q({size:t.canvas.size,position:e}),n=v.create(o.x,o.y,i),a=this.getRadius(),r=this.options.move.outModes,c=e=>{gi({outMode:e,checkModes:["bounce","bounce-horizontal"],coord:n.x,maxCoord:t.canvas.size.width,setCb:t=>n.x+=t,radius:a})},l=e=>{gi({outMode:e,checkModes:["bounce","bounce-vertical"],coord:n.y,maxCoord:t.canvas.size.height,setCb:t=>n.y+=t,radius:a})};return c(r.left??r.default),c(r.right??r.default),l(r.top??r.default),l(r.bottom??r.default),this._checkOverlap(n,s)?this._calcPosition(t,void 0,i,s+1):n},this._calculateVelocity=()=>{const t=E(this.direction).copy(),e=this.options.move;if("inside"===e.direction||"outside"===e.direction)return t;const i=Math.PI/180*C(e.angle.value),s=Math.PI/180*C(e.angle.offset),o={left:s-i/2,right:s+i/2};return e.straight||(t.angle+=z(O(o.left,o.right))),e.random&&"number"==typeof e.speed&&(t.length*=_()),t},this._checkOverlap=(t,e=0)=>{const i=this.options.collisions,s=this.getRadius();if(!i.enable)return!1;const o=i.overlap;if(o.enable)return!1;const n=o.retries;if(n>=0&&e>n)throw new Error(`${f} particle is overlapping and can't be placed`);return!!this.container.particles.find((e=>I(t,e.position)<s+e.getRadius()))},this._getRollColor=t=>{if(!t||!this.roll||!this.backColor&&!this.roll.alter)return t;const e=this.roll.horizontal&&this.roll.vertical?2:1,i=this.roll.horizontal?Math.PI/2:0;return Math.floor(((this.roll.angle??0)+i)/(Math.PI/e))%2?this.backColor?this.backColor:this.roll.alter?oe(t,this.roll.alter.type,this.roll.alter.value):t:t},this._initPosition=t=>{const e=this.container,i=C(this.options.zIndex.value);this.position=this._calcPosition(e,t,k(i,0,e.zLayers)),this.initialPosition=this.position.copy();const s=e.canvas.size;switch(this.moveCenter={...mt(this.options.move.center,s),radius:this.options.move.center.radius??0,mode:this.options.move.center.mode??"percent"},this.direction=D(this.options.move.direction,this.position,this.moveCenter),this.options.move.direction){case"inside":this.outType="inside";break;case"outside":this.outType="outside"}this.offset=m.origin},this._loadShapeData=(t,e)=>{const i=t.options[this.shape];if(i)return st({close:t.close,fill:t.fill},ut(i,this.id,e))},this._engine=t,this.init(e,s,o,n)}destroy(t){if(this.unbreakable||this.destroyed)return;this.destroyed=!0,this.bubble.inRange=!1,this.slow.inRange=!1;const e=this.container,i=this.pathGenerator;for(const[,i]of e.plugins)i.particleDestroyed&&i.particleDestroyed(this,t);for(const i of e.particles.updaters)i.particleDestroyed&&i.particleDestroyed(this,t);i&&i.reset(this)}draw(t){const e=this.container;for(const[,i]of e.plugins)e.canvas.drawParticlePlugin(i,this,t);e.canvas.drawParticle(this,t)}getFillColor(){return this._getRollColor(this.bubble.color??Gt(this.color))}getMass(){return this.getRadius()**2*Math.PI/2}getPosition(){return{x:this.position.x+this.offset.x,y:this.position.y+this.offset.y,z:this.position.z}}getRadius(){return this.bubble.radius??this.size.value}getStrokeColor(){return this._getRollColor(this.bubble.color??Gt(this.strokeColor))}init(t,e,i,s){const o=this.container,n=this._engine;this.id=t,this.group=s,this.fill=!0,this.pathRotation=!1,this.close=!0,this.lastPathTime=0,this.destroyed=!1,this.unbreakable=!1,this.rotation=0,this.misplaced=!1,this.retina={maxDistance:{}},this.outType="normal",this.ignoresResizeRatio=!0;const a=o.retina.pixelRatio,r=o.actualOptions,c=fi(this._engine,o,r.particles),l=c.shape.type,{reduceDuplicates:h}=c;this.shape=ut(l,this.id,h);const d=c.shape;if(i&&i.shape&&i.shape.type){const t=ut(i.shape.type,this.id,h);t&&(this.shape=t,d.load(i.shape))}this.shapeData=this._loadShapeData(d,h),c.load(i);const u=this.shapeData;u&&c.load(u.particles);const p=new xe(n,o);p.load(o.actualOptions.interactivity),p.load(c.interactivity),this.interactivity=p,this.fill=u?.fill??c.shape.fill,this.close=u?.close??c.shape.close,this.options=c;const f=this.options.move.path;this.pathDelay=1e3*T(f.delay),f.generator&&(this.pathGenerator=this._engine.plugins.getPathGenerator(f.generator),this.pathGenerator&&o.addPath(f.generator,this.pathGenerator)&&this.pathGenerator.init(o)),o.retina.initParticle(this),this.size=ft(this.options.size,a),this.bubble={inRange:!1},this.slow={inRange:!1,factor:1},this._initPosition(e),this.initialVelocity=this._calculateVelocity(),this.velocity=this.initialVelocity.copy(),this.moveDecay=1-C(this.options.move.decay);const v=o.particles;v.needsSort=v.needsSort||v.lastZIndex<this.position.z,v.lastZIndex=this.position.z,this.zIndexFactor=this.position.z/o.zLayers,this.sides=24;let m=o.drawers.get(this.shape);m||(m=this._engine.plugins.getShapeDrawer(this.shape),m&&o.drawers.set(this.shape,m)),m&&m.loadShape&&m.loadShape(this);const g=m?.getSidesCount;g&&(this.sides=g(this)),this.spawning=!1,this.shadowColor=Tt(this.options.shadow.color);for(const t of o.particles.updaters)t.init(this);for(const t of o.particles.movers)t.init&&t.init(this);m&&m.particleInit&&m.particleInit(o,this);for(const[,t]of o.plugins)t.particleCreated&&t.particleCreated(this)}isInsideCanvas(){const t=this.getRadius(),e=this.container.canvas.size,i=this.position;return i.x>=-t&&i.y>=-t&&i.y<=e.height+t&&i.x<=e.width+t}isVisible(){return!this.destroyed&&!this.spawning&&this.isInsideCanvas()}reset(){for(const t of this.container.particles.updaters)t.reset&&t.reset(this)}}class bi{constructor(t,e){this.position=t,this.particle=e}}class wi{constructor(t,e){this.position={x:t,y:e}}}class xi extends wi{constructor(t,e,i,s){super(t,e),this.size={height:s,width:i}}contains(t){const e=this.size.width,i=this.size.height,s=this.position;return t.x>=s.x&&t.x<=s.x+e&&t.y>=s.y&&t.y<=s.y+i}intersects(t){t instanceof _i&&t.intersects(this);const e=this.size.width,i=this.size.height,s=this.position,o=t.position,n=t instanceof xi?t.size:{width:0,height:0},a=n.width,r=n.height;return o.x<s.x+e&&o.x+a>s.x&&o.y<s.y+i&&o.y+r>s.y}}class _i extends wi{constructor(t,e,i){super(t,e),this.radius=i}contains(t){return I(t,this.position)<=this.radius}intersects(t){const e=this.position,i=t.position,s=Math.abs(i.x-e.x),o=Math.abs(i.y-e.y),n=this.radius;if(t instanceof _i){return n+t.radius>Math.sqrt(s**2+o**2)}if(t instanceof xi){const{width:e,height:i}=t.size;return Math.pow(s-e,2)+Math.pow(o-i,2)<=n**2||s<=n+e&&o<=n+i||s<=e||o<=i}return!1}}class ki{constructor(t,e){this.rectangle=t,this.capacity=e,this._subdivide=()=>{const{x:t,y:e}=this.rectangle.position,{width:i,height:s}=this.rectangle.size,{capacity:o}=this;for(let n=0;n<4;n++)this._subs.push(new ki(new xi(t+i/2*(n%2),e+s/2*(Math.round(n/2)-n%2),i/2,s/2),o));this._divided=!0},this._points=[],this._divided=!1,this._subs=[]}insert(t){return!!this.rectangle.contains(t.position)&&(this._points.length<this.capacity?(this._points.push(t),!0):(this._divided||this._subdivide(),this._subs.some((e=>e.insert(t)))))}query(t,e,i){const s=i||[];if(!t.intersects(this.rectangle))return[];for(const i of this._points)!t.contains(i.position)&&I(t.position,i.position)>i.particle.getRadius()&&(!e||e(i.particle))||s.push(i.particle);if(this._divided)for(const i of this._subs)i.query(t,e,s);return s}queryCircle(t,e,i){return this.query(new _i(t.x,t.y,e),i)}queryRectangle(t,e,i){return this.query(new xi(t.x,t.y,e.width,e.height),i)}}const Mi=t=>new xi(-t.width/4,-t.height/4,3*t.width/2,3*t.height/2);class zi{constructor(t,e){this._applyDensity=(t,e,i)=>{if(!t.number.density?.enable)return;const s=t.number,o=this._initDensityFactor(s.density),n=s.value,a=s.limit>0?s.limit:n,r=Math.min(n,a)*o+e,c=Math.min(this.count,this.filter((t=>t.group===i)).length);this.limit=s.limit*o,c<r?this.push(Math.abs(r-c),void 0,t,i):c>r&&this.removeQuantity(c-r,i)},this._initDensityFactor=t=>{const e=this._container;if(!e.canvas.element||!t.enable)return 1;const i=e.canvas.element,s=e.retina.pixelRatio;return i.width*i.height/(t.factor*s**2*t.area)},this._pushParticle=(t,e,i,s)=>{try{let o=this.pool.pop();o?o.init(this._nextId,t,e,i):o=new yi(this._engine,this._nextId,this._container,t,e,i);let n=!0;if(s&&(n=s(o)),!n)return;return this._array.push(o),this._zArray.push(o),this._nextId++,this._engine.dispatchEvent("particleAdded",{container:this._container,data:{particle:o}}),o}catch(t){return void $().warning(`${f} adding particle: ${t}`)}},this._removeParticle=(t,e,i)=>{const s=this._array[t];if(!s||s.group!==e)return!1;s.destroy(i);const o=this._zArray.indexOf(s);return this._array.splice(t,1),this._zArray.splice(o,1),this.pool.push(s),this._engine.dispatchEvent("particleRemoved",{container:this._container,data:{particle:s}}),!0},this._engine=t,this._container=e,this._nextId=0,this._array=[],this._zArray=[],this.pool=[],this.limit=0,this.needsSort=!1,this.lastZIndex=0,this._interactionManager=new mi(t,e);const i=e.canvas.size;this.quadTree=new ki(Mi(i),4),this.movers=this._engine.plugins.getMovers(e,!0),this.updaters=this._engine.plugins.getUpdaters(e,!0)}get count(){return this._array.length}addManualParticles(){const t=this._container,e=t.actualOptions;for(const i of e.manualParticles)this.addParticle(i.position?mt(i.position,t.canvas.size):void 0,i.options)}addParticle(t,e,i,s){const o=this._container.actualOptions.particles.number.limit;if(o>0){const t=this.count+1-o;t>0&&this.removeQuantity(t)}return this._pushParticle(t,e,i,s)}clear(){this._array=[],this._zArray=[]}destroy(){this._array=[],this._zArray=[],this.movers=[],this.updaters=[]}async draw(t){const e=this._container;e.canvas.clear(),await this.update(t);for(const[,i]of e.plugins)e.canvas.drawPlugin(i,t);for(const e of this._zArray)e.draw(t)}filter(t){return this._array.filter(t)}find(t){return this._array.find(t)}handleClickMode(t){this._interactionManager.handleClickMode(t)}init(){const t=this._container,e=t.actualOptions;this.lastZIndex=0,this.needsSort=!1;let i=!1;this.updaters=this._engine.plugins.getUpdaters(t,!0),this._interactionManager.init();for(const[,e]of t.plugins)if(void 0!==e.particlesInitialization&&(i=e.particlesInitialization()),i)break;this._interactionManager.init();for(const[,e]of t.pathGenerators)e.init(t);if(this.addManualParticles(),!i){for(const t in e.particles.groups){const i=e.particles.groups[t];for(let s=this.count,o=0;o<i.number?.value&&s<e.particles.number.value;s++,o++)this.addParticle(void 0,i,t)}for(let t=this.count;t<e.particles.number.value;t++)this.addParticle()}}push(t,e,i,s){this.pushing=!0;for(let o=0;o<t;o++)this.addParticle(e?.position,i,s);this.pushing=!1}async redraw(){this.clear(),this.init(),await this.draw({value:0,factor:0})}remove(t,e,i){this.removeAt(this._array.indexOf(t),void 0,e,i)}removeAt(t,e=1,i,s){if(t<0||t>this.count)return;let o=0;for(let n=t;o<e&&n<this.count;n++)this._removeParticle(n--,i,s)&&o++}removeQuantity(t,e){this.removeAt(0,t,e)}setDensity(){const t=this._container.actualOptions,e=t.particles.groups;for(const t in e)this._applyDensity(e[t],0,t);this._applyDensity(t.particles,t.manualParticles.length)}async update(t){const e=this._container,i=new Set;this.quadTree=new ki(Mi(e.canvas.size),4);for(const[,t]of e.pathGenerators)t.update();for(const[,i]of e.plugins)i.update&&i.update(t);for(const s of this._array){const o=e.canvas.resizeFactor;o&&!s.ignoresResizeRatio&&(s.position.x*=o.width,s.position.y*=o.height,s.initialPosition.x*=o.width,s.initialPosition.y*=o.height),s.ignoresResizeRatio=!1,await this._interactionManager.reset(s);for(const[,e]of this._container.plugins){if(s.destroyed)break;e.particleUpdate&&e.particleUpdate(s,t)}for(const e of this.movers)e.isEnabled(s)&&e.move(s,t);s.destroyed?i.add(s):this.quadTree.insert(new bi(s.getPosition(),s))}if(i.size){const t=t=>!i.has(t);this._array=this.filter(t),this._zArray=this._zArray.filter(t),this.pool.push(...i)}await this._interactionManager.externalInteract(t);for(const e of this._array){for(const i of this.updaters)i.update(e,t);e.destroyed||e.spawning||await this._interactionManager.particlesInteract(e,t)}if(delete e.canvas.resizeFactor,this.needsSort){const t=this._zArray;t.sort(((t,e)=>e.position.z-t.position.z||t.id-e.id)),this.lastZIndex=t[t.length-1].position.z,this.needsSort=!1}}}class Ci{constructor(t){this.container=t,this.pixelRatio=1,this.reduceFactor=1}init(){const t=this.container,e=t.actualOptions;this.pixelRatio=!e.detectRetina||j()?1:window.devicePixelRatio,this.reduceFactor=1;const i=this.pixelRatio;if(t.canvas.element){const e=t.canvas.element;t.canvas.size.width=e.offsetWidth*i,t.canvas.size.height=e.offsetHeight*i}const s=e.particles,o=s.move;this.attractDistance=C(o.attract.distance)*i,this.maxSpeed=C(o.gravity.maxSpeed)*i,this.sizeAnimationSpeed=C(s.size.animation.speed)*i}initParticle(t){const e=t.options,i=this.pixelRatio,s=e.move,o=s.distance,n=t.retina;n.attractDistance=C(s.attract.distance)*i,n.moveDrift=C(s.drift)*i,n.moveSpeed=C(s.speed)*i,n.sizeAnimationSpeed=C(e.size.animation.speed)*i;const a=n.maxDistance;a.horizontal=void 0!==o.horizontal?o.horizontal*i:void 0,a.vertical=void 0!==o.vertical?o.vertical*i:void 0,n.maxSpeed=C(s.gravity.maxSpeed)*i}}function Pi(t){return t&&!t.destroyed}function Si(t,e,...i){const s=new vi(t,e);return pi(s,...i),s}const Oi={generate:t=>t.velocity,init:()=>{},update:()=>{},reset:()=>{}};class Ti{constructor(t,e,i){this.id=e,this._intersectionManager=t=>{if(Pi(this)&&this.actualOptions.pauseOnOutsideViewport)for(const e of t)e.target===this.interactivity.element&&(e.isIntersecting?this.play:this.pause)()},this._nextFrame=async t=>{try{if(!this.smooth&&void 0!==this.lastFrameTime&&t<this.lastFrameTime+1e3/this.fpsLimit)return void this.draw(!1);this.lastFrameTime??=t;const e=function(t,e=60,i=!1){return{value:t,factor:i?60/e:60*t/1e3}}(t-this.lastFrameTime,this.fpsLimit,this.smooth);if(this.addLifeTime(e.value),this.lastFrameTime=t,e.value>1e3)return void this.draw(!1);if(await this.particles.draw(e),!this.alive())return void this.destroy();this.getAnimationStatus()&&this.draw(!1)}catch(t){$().error(`${f} in animation loop`,t)}},this._engine=t,this.fpsLimit=120,this.smooth=!1,this._delay=0,this._duration=0,this._lifeTime=0,this._firstStart=!0,this.started=!1,this.destroyed=!1,this._paused=!0,this.lastFrameTime=0,this.zLayers=100,this.pageHidden=!1,this._sourceOptions=i,this._initialSourceOptions=i,this.retina=new Ci(this),this.canvas=new ae(this),this.particles=new zi(this._engine,this),this.pathGenerators=new Map,this.interactivity={mouse:{clicking:!1,inside:!1}},this.plugins=new Map,this.drawers=new Map,this._options=Si(this._engine,this),this.actualOptions=Si(this._engine,this),this._eventListeners=new ce(this),"undefined"!=typeof IntersectionObserver&&IntersectionObserver&&(this._intersectionObserver=new IntersectionObserver((t=>this._intersectionManager(t)))),this._engine.dispatchEvent("containerBuilt",{container:this})}get options(){return this._options}get sourceOptions(){return this._sourceOptions}addClickHandler(t){if(!Pi(this))return;const e=this.interactivity.element;if(!e)return;const i=(e,i,s)=>{if(!Pi(this))return;const o=this.retina.pixelRatio,n={x:i.x*o,y:i.y*o},a=this.particles.quadTree.queryCircle(n,s*o);t(e,a)};let s=!1,o=!1;e.addEventListener("click",(t=>{if(!Pi(this))return;const e=t,s={x:e.offsetX||e.clientX,y:e.offsetY||e.clientY};i(t,s,1)})),e.addEventListener("touchstart",(()=>{Pi(this)&&(s=!0,o=!1)})),e.addEventListener("touchmove",(()=>{Pi(this)&&(o=!0)})),e.addEventListener("touchend",(t=>{if(Pi(this)){if(s&&!o){const e=t;let s=e.touches[e.touches.length-1];if(!s&&(s=e.changedTouches[e.changedTouches.length-1],!s))return;const o=this.canvas.element,n=o?o.getBoundingClientRect():void 0,a={x:s.clientX-(n?n.left:0),y:s.clientY-(n?n.top:0)};i(t,a,Math.max(s.radiusX,s.radiusY))}s=!1,o=!1}})),e.addEventListener("touchcancel",(()=>{Pi(this)&&(s=!1,o=!1)}))}addLifeTime(t){this._lifeTime+=t}addPath(t,e,i=!1){return!(!Pi(this)||!i&&this.pathGenerators.has(t))&&(this.pathGenerators.set(t,e??Oi),!0)}alive(){return!this._duration||this._lifeTime<=this._duration}destroy(){if(!Pi(this))return;this.stop(),this.particles.destroy(),this.canvas.destroy();for(const[,t]of this.drawers)t.destroy&&t.destroy(this);for(const t of this.drawers.keys())this.drawers.delete(t);this._engine.plugins.destroy(this),this.destroyed=!0;const t=this._engine.dom(),e=t.findIndex((t=>t===this));e>=0&&t.splice(e,1),this._engine.dispatchEvent("containerDestroyed",{container:this})}draw(t){if(!Pi(this))return;let e=t;this._drawAnimationFrame=requestAnimationFrame((async t=>{e&&(this.lastFrameTime=void 0,e=!1),await this._nextFrame(t)}))}async export(t,e={}){for(const[,i]of this.plugins){if(!i.export)continue;const s=await i.export(t,e);if(s.supported)return s.blob}$().error(`${f} - Export plugin with type ${t} not found`)}getAnimationStatus(){return!this._paused&&!this.pageHidden&&Pi(this)}handleClickMode(t){if(Pi(this)){this.particles.handleClickMode(t);for(const[,e]of this.plugins)e.handleClickMode&&e.handleClickMode(t)}}async init(){if(!Pi(this))return;const t=this._engine.plugins.getSupportedShapes();for(const e of t){const t=this._engine.plugins.getShapeDrawer(e);t&&this.drawers.set(e,t)}this._options=Si(this._engine,this,this._initialSourceOptions,this.sourceOptions),this.actualOptions=Si(this._engine,this,this._options);const e=this._engine.plugins.getAvailablePlugins(this);for(const[t,i]of e)this.plugins.set(t,i);this.retina.init(),await this.canvas.init(),this.updateActualOptions(),this.canvas.initBackground(),this.canvas.resize(),this.zLayers=this.actualOptions.zLayers,this._duration=1e3*C(this.actualOptions.duration),this._delay=1e3*C(this.actualOptions.delay),this._lifeTime=0,this.fpsLimit=this.actualOptions.fpsLimit>0?this.actualOptions.fpsLimit:120,this.smooth=this.actualOptions.smooth;for(const[,t]of this.drawers)t.init&&await t.init(this);for(const[,t]of this.plugins)t.init&&await t.init();this._engine.dispatchEvent("containerInit",{container:this}),this.particles.init(),this.particles.setDensity();for(const[,t]of this.plugins)t.particlesSetup&&t.particlesSetup();this._engine.dispatchEvent("particlesSetup",{container:this})}async loadTheme(t){Pi(this)&&(this._currentTheme=t,await this.refresh())}pause(){if(Pi(this)&&(void 0!==this._drawAnimationFrame&&(cancelAnimationFrame(this._drawAnimationFrame),delete this._drawAnimationFrame),!this._paused)){for(const[,t]of this.plugins)t.pause&&t.pause();this.pageHidden||(this._paused=!0),this._engine.dispatchEvent("containerPaused",{container:this})}}play(t){if(!Pi(this))return;const e=this._paused||t;if(!this._firstStart||this.actualOptions.autoPlay){if(this._paused&&(this._paused=!1),e)for(const[,t]of this.plugins)t.play&&t.play();this._engine.dispatchEvent("containerPlay",{container:this}),this.draw(e||!1)}else this._firstStart=!1}async refresh(){if(Pi(this))return this.stop(),this.start()}async reset(){if(Pi(this))return this._initialSourceOptions=void 0,this._options=Si(this._engine,this),this.actualOptions=Si(this._engine,this,this._options),this.refresh()}setNoise(t,e,i){Pi(this)&&this.setPath(t,e,i)}setPath(t,e,i){if(!t||!Pi(this))return;const s={...Oi};if(xt(t))s.generate=t,e&&(s.init=e),i&&(s.update=i);else{const e=s;s.generate=t.generate||e.generate,s.init=t.init||e.init,s.update=t.update||e.update}this.addPath("default",s,!0)}async start(){Pi(this)&&!this.started&&(await this.init(),this.started=!0,await new Promise((t=>{this._delayTimeout=setTimeout((async()=>{this._eventListeners.addListeners(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.observe(this.interactivity.element);for(const[,t]of this.plugins)t.start&&await t.start();this._engine.dispatchEvent("containerStarted",{container:this}),this.play(),t()}),this._delay)})))}stop(){if(Pi(this)&&this.started){this._delayTimeout&&(clearTimeout(this._delayTimeout),delete this._delayTimeout),this._firstStart=!0,this.started=!1,this._eventListeners.removeListeners(),this.pause(),this.particles.clear(),this.canvas.stop(),this.interactivity.element instanceof HTMLElement&&this._intersectionObserver&&this._intersectionObserver.unobserve(this.interactivity.element);for(const[,t]of this.plugins)t.stop&&t.stop();for(const t of this.plugins.keys())this.plugins.delete(t);this._sourceOptions=this._options,this._engine.dispatchEvent("containerStopped",{container:this})}}updateActualOptions(){this.actualOptions.responsive=[];const t=this.actualOptions.setResponsive(this.canvas.size.width,this.retina.pixelRatio,this._options);return this.actualOptions.setTheme(this._currentTheme),this.responsiveMaxWidth!==t&&(this.responsiveMaxWidth=t,!0)}}class Ri{constructor(){this._listeners=new Map}addEventListener(t,e){this.removeEventListener(t,e);let i=this._listeners.get(t);i||(i=[],this._listeners.set(t,i)),i.push(e)}dispatchEvent(t,e){const i=this._listeners.get(t);i&&i.forEach((t=>t(e)))}hasEventListener(t){return!!this._listeners.get(t)}removeAllEventListeners(t){t?this._listeners.delete(t):this._listeners=new Map}removeEventListener(t,e){const i=this._listeners.get(t);if(!i)return;const s=i.length,o=i.indexOf(e);o<0||(1===s?this._listeners.delete(t):i.splice(o,1))}}function Ii(t,e,i,s=!1){let o=e.get(t);return o&&!s||(o=[...i.values()].map((e=>e(t))),e.set(t,o)),o}class Di{constructor(t){this._engine=t,this.plugins=[],this._initializers={interactors:new Map,movers:new Map,updaters:new Map},this.interactors=new Map,this.movers=new Map,this.updaters=new Map,this.presets=new Map,this.drawers=new Map,this.pathGenerators=new Map}addInteractor(t,e){this._initializers.interactors.set(t,e)}addParticleMover(t,e){this._initializers.movers.set(t,e)}addParticleUpdater(t,e){this._initializers.updaters.set(t,e)}addPathGenerator(t,e){!this.getPathGenerator(t)&&this.pathGenerators.set(t,e)}addPlugin(t){!this.getPlugin(t.id)&&this.plugins.push(t)}addPreset(t,e,i=!1){(i||!this.getPreset(t))&&this.presets.set(t,e)}addShapeDrawer(t,e){dt(t,(t=>{!this.getShapeDrawer(t)&&this.drawers.set(t,e)}))}destroy(t){this.updaters.delete(t),this.movers.delete(t),this.interactors.delete(t)}getAvailablePlugins(t){const e=new Map;for(const i of this.plugins)i.needsPlugin(t.actualOptions)&&e.set(i.id,i.getPlugin(t));return e}getInteractors(t,e=!1){return Ii(t,this.interactors,this._initializers.interactors,e)}getMovers(t,e=!1){return Ii(t,this.movers,this._initializers.movers,e)}getPathGenerator(t){return this.pathGenerators.get(t)}getPlugin(t){return this.plugins.find((e=>e.id===t))}getPreset(t){return this.presets.get(t)}getShapeDrawer(t){return this.drawers.get(t)}getSupportedShapes(){return this.drawers.keys()}getUpdaters(t,e=!1){return Ii(t,this.updaters,this._initializers.updaters,e)}loadOptions(t,e){for(const i of this.plugins)i.loadOptions(t,e)}loadParticlesOptions(t,e,...i){const s=this.updaters.get(t);if(s)for(const t of s)t.loadOptions&&t.loadOptions(e,...i)}}class Ei{constructor(){this._configs=new Map,this._domArray=[],this._eventDispatcher=new Ri,this._initialized=!1,this.plugins=new Di(this)}get configs(){const t={};for(const[e,i]of this._configs)t[e]=i;return t}get version(){return"2.12.0"}addConfig(t,e){bt(t)?e&&(e.name=t,this._configs.set(t,e)):this._configs.set(t.name??"default",t)}addEventListener(t,e){this._eventDispatcher.addEventListener(t,e)}async addInteractor(t,e,i=!0){this.plugins.addInteractor(t,e),await this.refresh(i)}async addMover(t,e,i=!0){this.plugins.addParticleMover(t,e),await this.refresh(i)}async addParticleUpdater(t,e,i=!0){this.plugins.addParticleUpdater(t,e),await this.refresh(i)}async addPathGenerator(t,e,i=!0){this.plugins.addPathGenerator(t,e),await this.refresh(i)}async addPlugin(t,e=!0){this.plugins.addPlugin(t),await this.refresh(e)}async addPreset(t,e,i=!1,s=!0){this.plugins.addPreset(t,e,i),await this.refresh(s)}async addShape(t,e,i,s,o,n=!0){let a,r,c,l,h=n;yt(i)?(h=i,r=void 0):r=i,yt(s)?(h=s,c=void 0):c=s,yt(o)?(h=o,l=void 0):l=o,a=xt(e)?{afterEffect:c,destroy:l,draw:e,init:r}:e,this.plugins.addShapeDrawer(t,a),await this.refresh(h)}dispatchEvent(t,e){this._eventDispatcher.dispatchEvent(t,e)}dom(){return this._domArray}domItem(t){const e=this.dom(),i=e[t];if(i&&!i.destroyed)return i;e.splice(t,1)}init(){this._initialized||(this._initialized=!0)}async load(t,e){return this.loadFromArray(t,e)}async loadFromArray(t,e,i){let s;return!function(t){return!!((e=t).id||e.element||e.url||e.options);var e}(t)?(s={},bt(t)?s.id=t:s.options=t,wt(e)?s.index=e:s.options=e??s.options,s.index=i??s.index):s=t,this._loadParams(s)}async loadJSON(t,e,i){let s,o;return wt(e)||void 0===e?s=t:(o=t,s=e),this._loadParams({id:o,url:s,index:i})}async refresh(t=!0){t&&this.dom().forEach((t=>t.refresh()))}removeEventListener(t,e){this._eventDispatcher.removeEventListener(t,e)}async set(t,e,i,s){const o={index:s};return bt(t)?o.id=t:o.element=t,e instanceof HTMLElement?o.element=e:o.options=e,wt(i)?o.index=i:o.options=i??o.options,this._loadParams(o)}async setJSON(t,e,i,s){const o={};return t instanceof HTMLElement?(o.element=t,o.url=e,o.index=i):(o.id=t,o.element=e,o.url=i,o.index=s),this._loadParams(o)}setOnClickHandler(t){const e=this.dom();if(!e.length)throw new Error(`${f} can only set click handlers after calling tsParticles.load()`);for(const i of e)i.addClickHandler(t)}async _loadParams(t){const e=t.id??`tsparticles${Math.floor(1e4*_())}`,{index:s,url:o}=t,n=o?await async function(t){const e=ut(t.url,t.index);if(!e)return t.fallback;const i=await fetch(e);return i.ok?i.json():($().error(`${f} ${i.status} while retrieving config file`),t.fallback)}({fallback:t.options,url:o,index:s}):t.options;let a=t.element??document.getElementById(e);a||(a=document.createElement("div"),a.id=e,document.body.append(a));const r=ut(n,s),c=this.dom(),l=c.findIndex((t=>t.id===e));if(l>=0){const t=this.domItem(l);t&&!t.destroyed&&(t.destroy(),c.splice(l,1))}let h;if("canvas"===a.tagName.toLowerCase())h=a,h.dataset[i]="false";else{const t=a.getElementsByTagName("canvas");t.length?(h=t[0],h.dataset[i]="false"):(h=document.createElement("canvas"),h.dataset[i]="true",a.appendChild(h))}h.style.width||(h.style.width="100%"),h.style.height||(h.style.height="100%");const d=new Ti(this,e,r);return l>=0?c.splice(l,0,d):c.push(d),d.canvas.loadCanvas(h),await d.start(),d}}class Li{constructor(){this.key="hsl",this.stringPrefix="hsl"}handleColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.s&&void 0!==e.l)return Bt(e)}handleRangeColor(t){const e=t.value.hsl??t.value;if(void 0!==e.h&&void 0!==e.l)return Bt({h:C(e.h),l:C(e.l),s:C(e.s)})}parseString(t){if(!t.startsWith("hsl"))return;const e=/hsla?\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([\d.%]+)\s*)?\)/i.exec(t);return e?Ft({a:e.length>4?H(e[5]):1,h:parseInt(e[1],10),l:parseInt(e[3],10),s:parseInt(e[2],10)}):void 0}}class Ai{constructor(){this.key="rgb",this.stringPrefix="rgb"}handleColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return e}handleRangeColor(t){const e=t.value.rgb??t.value;if(void 0!==e.r)return{r:C(e.r),g:C(e.g),b:C(e.b)}}parseString(t){if(!t.startsWith(this.stringPrefix))return;const e=/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(,\s*([\d.%]+)\s*)?\)/i.exec(t);return e?{a:e.length>4?H(e[5]):1,b:parseInt(e[3],10),g:parseInt(e[2],10),r:parseInt(e[1],10)}:void 0}}class Bi{constructor(t){this.container=t,this.type="external"}}class Fi{constructor(t){this.container=t,this.type="particles"}}const qi=function(){const t=new Ai,e=new Li;Pt(t),Pt(e);const i=new Ei;return i.init(),i}();j()||(window.tsParticles=qi);class Vi{static init(t){const e=new Vi,i=t.selector;if(!i)throw new Error("No selector provided");const s=document.querySelector(i);if(!s)throw new Error("No element found for selector");return qi.set(i.replace(".","").replace("!",""),s,{fullScreen:{enable:!1},particles:{color:{value:t.color??"!000000"},links:{color:"random",distance:t.minDistance??120,enable:t.connectParticles??!1},move:{enable:!0,speed:t.speed??.5},number:{value:t.maxParticles??100},size:{value:{min:1,max:t.sizeVariations??3}}},responsive:t.responsive?.map((e=>({maxWidth:e.breakpoint,options:{particles:{color:{value:e.options?.color},links:{distance:e.options?.minDistance,enable:e.options?.connectParticles},number:{value:t.maxParticles},move:{enable:!0,speed:e.options?.speed},size:{value:e.options?.sizeVariations}}}})))}).then((t=>{e._container=t})),e}destroy(){const t=this._container;t&&t.destroy()}pauseAnimation(){const t=this._container;t&&t.pause()}resumeAnimation(){const t=this._container;t&&t.play()}}const Hi=t=>{const{particlesJS:e,pJSDom:i}=(t=>{const e=(e,i)=>t.load(e,i);return e.load=(e,i,s)=>{t.loadJSON(e,i).then((t=>{t&&s(t)})).catch((()=>{s(void 0)}))},e.setOnClickHandler=e=>{t.setOnClickHandler(e)},{particlesJS:e,pJSDom:t.dom()}})(t);return window.particlesJS=e,window.pJSDom=i,window.Particles=Vi,{particlesJS:e,pJSDom:i,Particles:Vi}};function Wi(t,e,i,s,o,n){!function(t,e){const i=t.options,s=i.move.path;if(!s.enable)return;if(t.lastPathTime<=t.pathDelay)return void(t.lastPathTime+=e.value);const o=t.pathGenerator?.generate(t,e);o&&t.velocity.addTo(o);s.clamp&&(t.velocity.x=k(t.velocity.x,-1,1),t.velocity.y=k(t.velocity.y,-1,1));t.lastPathTime-=t.pathDelay}(t,n);const a=t.gravity,r=a?.enable&&a.inverse?-1:1;o&&i&&(t.velocity.x+=o*n.factor/(60*i)),a?.enable&&i&&(t.velocity.y+=r*(a.acceleration*n.factor)/(60*i));const c=t.moveDecay;t.velocity.multTo(c);const l=t.velocity.mult(i);a?.enable&&s>0&&(!a.inverse&&l.y>=0&&l.y>=s||a.inverse&&l.y<=0&&l.y<=-s)&&(l.y=r*s,i&&(t.velocity.y=l.y/i));const h=t.options.zIndex,d=(1-t.zIndexFactor)**h.velocityRate;l.multTo(d);const{position:u}=t;u.addTo(l),e.vibrate&&(u.x+=Math.sin(u.x*Math.cos(u.y)),u.y+=Math.cos(u.y*Math.sin(u.x)))}class Ui{constructor(){this._initSpin=t=>{const e=t.container,i=t.options.move.spin;if(!i.enable)return;const s=i.position??{x:50,y:50},o={x:s.x/100*e.canvas.size.width,y:s.y/100*e.canvas.size.height},n=I(t.getPosition(),o),a=C(i.acceleration);t.retina.spinAcceleration=a*e.retina.pixelRatio,t.spin={center:o,direction:t.velocity.x>=0?"clockwise":"counter-clockwise",angle:t.velocity.angle,radius:n,acceleration:t.retina.spinAcceleration}}}init(t){const e=t.options.move.gravity;t.gravity={enable:e.enable,acceleration:C(e.acceleration),inverse:e.inverse},this._initSpin(t)}isEnabled(t){return!t.destroyed&&t.options.move.enable}move(t,e){const i=t.options,s=i.move;if(!s.enable)return;const o=t.container,n=o.retina.pixelRatio,a=function(t){return t.slow.inRange?t.slow.factor:1}(t),r=(t.retina.moveSpeed??=C(s.speed)*n)*o.retina.reduceFactor,c=t.retina.moveDrift??=C(t.options.move.drift)*n,l=S(i.size.value)*n,h=r*(s.size?t.getRadius()/l:1)*a*(e.factor||1)/2,d=t.retina.maxSpeed??o.retina.maxSpeed;s.spin.enable?function(t,e){const i=t.container;if(!t.spin)return;const s={x:"clockwise"===t.spin.direction?Math.cos:Math.sin,y:"clockwise"===t.spin.direction?Math.sin:Math.cos};t.position.x=t.spin.center.x+t.spin.radius*s.x(t.spin.angle),t.position.y=t.spin.center.y+t.spin.radius*s.y(t.spin.angle),t.spin.radius+=t.spin.acceleration;const o=Math.max(i.canvas.size.width,i.canvas.size.height);t.spin.radius>o/2?(t.spin.radius=o/2,t.spin.acceleration*=-1):t.spin.radius<0&&(t.spin.radius=0,t.spin.acceleration*=-1),t.spin.angle+=e/100*(1-t.spin.radius/o)}(t,h):Wi(t,s,h,d,c,e),function(t){const e=t.initialPosition,{dx:i,dy:s}=R(e,t.position),o=Math.abs(i),n=Math.abs(s),{maxDistance:a}=t.retina,r=a.horizontal,c=a.vertical;if(r||c)if((r&&o>=r||c&&n>=c)&&!t.misplaced)t.misplaced=!!r&&o>r||!!c&&n>c,r&&(t.velocity.x=t.velocity.y/2-t.velocity.x),c&&(t.velocity.y=t.velocity.x/2-t.velocity.y);else if((!r||o<r)&&(!c||n<c)&&t.misplaced)t.misplaced=!1;else if(t.misplaced){const i=t.position,s=t.velocity;r&&(i.x<e.x&&s.x<0||i.x>e.x&&s.x>0)&&(s.x*=-_()),c&&(i.y<e.y&&s.y<0||i.y>e.y&&s.y>0)&&(s.y*=-_())}}(t)}}class $i{draw(t,e,i){e.circleRange||(e.circleRange={min:0,max:2*Math.PI});const s=e.circleRange;t.arc(0,0,i,s.min,s.max,!1)}getSidesCount(){return 12}particleInit(t,e){const i=e.shapeData,s=i?.angle??{max:360,min:0};e.circleRange=_t(s)?{min:s.min*Math.PI/180,max:s.max*Math.PI/180}:{min:0,max:s*Math.PI/180}}}function Gi(t,e,i,s,o){if(!e||!i.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=t.value),(e.delayTime??0)>0&&e.time<(e.delayTime??0))return;const n=z(i.offset),a=(e.velocity??0)*t.factor+3.6*n,r=e.decay??1;o&&"increasing"!==e.status?(e.value-=a,e.value<0&&(e.loops||(e.loops=0),e.loops++,e.status="increasing",e.value+=e.value)):(e.value+=a,e.value>s&&(e.loops||(e.loops=0),e.loops++,o&&(e.status="decreasing",e.value-=e.value%s))),e.velocity&&1!==r&&(e.velocity*=r),e.value>s&&(e.value%=s)}class ji{constructor(t){this.container=t}init(t){const e=Dt(t.options.color,t.id,t.options.reduceDuplicates);e&&(t.color=jt(e,t.options.color.animation,this.container.retina.reduceFactor))}isEnabled(t){const{h:e,s:i,l:s}=t.options.color.animation,{color:o}=t;return!t.destroyed&&!t.spawning&&(void 0!==o?.h.value&&e.enable||void 0!==o?.s.value&&i.enable||void 0!==o?.l.value&&s.enable)}update(t,e){!function(t,e){const{h:i,s,l:o}=t.options.color.animation,{color:n}=t;if(!n)return;const{h:a,s:r,l:c}=n;a&&Gi(e,a,i,360,!1),r&&Gi(e,r,s,100,!0),c&&Gi(e,c,o,100,!0)}(t,e)}}class Ni{constructor(t){this.container=t}init(t){const e=t.options.opacity;t.opacity=ft(e,1);const i=e.animation;i.enable&&(t.opacity.velocity=C(i.speed)/100*this.container.retina.reduceFactor,i.sync||(t.opacity.velocity*=_()))}isEnabled(t){return!t.destroyed&&!t.spawning&&!!t.opacity&&t.opacity.enable&&((t.opacity.maxLoops??0)<=0||(t.opacity.maxLoops??0)>0&&(t.opacity.loops??0)<(t.opacity.maxLoops??0))}reset(t){t.opacity&&(t.opacity.time=0,t.opacity.loops=0)}update(t,e){this.isEnabled(t)&&function(t,e){const i=t.opacity;if(t.destroyed||!i?.enable||(i.maxLoops??0)>0&&(i.loops??0)>(i.maxLoops??0))return;const s=i.min,o=i.max,n=i.decay??1;if(i.time||(i.time=0),(i.delayTime??0)>0&&i.time<(i.delayTime??0)&&(i.time+=e.value),!((i.delayTime??0)>0&&i.time<(i.delayTime??0))){switch(i.status){case"increasing":i.value>=o?(i.status="decreasing",i.loops||(i.loops=0),i.loops++):i.value+=(i.velocity??0)*e.factor;break;case"decreasing":i.value<=s?(i.status="increasing",i.loops||(i.loops=0),i.loops++):i.value-=(i.velocity??0)*e.factor}i.velocity&&1!==i.decay&&(i.velocity*=n),function(t,e,i,s){switch(t.options.opacity.animation.destroy){case"max":e>=s&&t.destroy();break;case"min":e<=i&&t.destroy()}}(t,i.value,s,o),t.destroyed||(i.value=k(i.value,s,o))}}(t,e)}}class Xi{constructor(t){this.container=t,this.modes=["bounce","bounce-vertical","bounce-horizontal","bounceVertical","bounceHorizontal","split"]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;let n=!1;for(const[,s]of o.plugins)if(void 0!==s.particleBounce&&(n=s.particleBounce(t,i,e)),n)break;if(n)return;const a=t.getPosition(),r=t.offset,c=t.getRadius(),l=it(a,c),h=o.canvas.size;!function(t){if("bounce"!==t.outMode&&"bounce-horizontal"!==t.outMode&&"bounceHorizontal"!==t.outMode&&"split"!==t.outMode||"left"!==t.direction&&"right"!==t.direction)return;t.bounds.right<0&&"left"===t.direction?t.particle.position.x=t.size+t.offset.x:t.bounds.left>t.canvasSize.width&&"right"===t.direction&&(t.particle.position.x=t.canvasSize.width-t.size-t.offset.x);const e=t.particle.velocity.x;let i=!1;if("right"===t.direction&&t.bounds.right>=t.canvasSize.width&&e>0||"left"===t.direction&&t.bounds.left<=0&&e<0){const e=T(t.particle.options.bounce.horizontal);t.particle.velocity.x*=-e,i=!0}if(!i)return;const s=t.offset.x+t.size;t.bounds.right>=t.canvasSize.width&&"right"===t.direction?t.particle.position.x=t.canvasSize.width-s:t.bounds.left<=0&&"left"===t.direction&&(t.particle.position.x=s),"split"===t.outMode&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:l,canvasSize:h,offset:r,size:c}),function(t){if("bounce"!==t.outMode&&"bounce-vertical"!==t.outMode&&"bounceVertical"!==t.outMode&&"split"!==t.outMode||"bottom"!==t.direction&&"top"!==t.direction)return;t.bounds.bottom<0&&"top"===t.direction?t.particle.position.y=t.size+t.offset.y:t.bounds.top>t.canvasSize.height&&"bottom"===t.direction&&(t.particle.position.y=t.canvasSize.height-t.size-t.offset.y);const e=t.particle.velocity.y;let i=!1;if("bottom"===t.direction&&t.bounds.bottom>=t.canvasSize.height&&e>0||"top"===t.direction&&t.bounds.top<=0&&e<0){const e=T(t.particle.options.bounce.vertical);t.particle.velocity.y*=-e,i=!0}if(!i)return;const s=t.offset.y+t.size;t.bounds.bottom>=t.canvasSize.height&&"bottom"===t.direction?t.particle.position.y=t.canvasSize.height-s:t.bounds.top<=0&&"top"===t.direction&&(t.particle.position.y=s),"split"===t.outMode&&t.particle.destroy()}({particle:t,outMode:s,direction:e,bounds:l,canvasSize:h,offset:r,size:c})}}class Yi{constructor(t){this.container=t,this.modes=["destroy"]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;switch(t.outType){case"normal":case"outside":if(tt(t.position,o.canvas.size,m.origin,t.getRadius(),e))return;break;case"inside":{const{dx:e,dy:i}=R(t.position,t.moveCenter),{x:s,y:o}=t.velocity;if(s<0&&e>t.moveCenter.radius||o<0&&i>t.moveCenter.radius||s>=0&&e<-t.moveCenter.radius||o>=0&&i<-t.moveCenter.radius)return;break}}o.particles.remove(t,void 0,!0)}}class Ji{constructor(t){this.container=t,this.modes=["none"]}update(t,e,i,s){if(!this.modes.includes(s))return;if(t.options.move.distance.horizontal&&("left"===e||"right"===e)||t.options.move.distance.vertical&&("top"===e||"bottom"===e))return;const o=t.options.move.gravity,n=this.container,a=n.canvas.size,r=t.getRadius();if(o.enable){const i=t.position;(!o.inverse&&i.y>a.height+r&&"bottom"===e||o.inverse&&i.y<-r&&"top"===e)&&n.particles.remove(t)}else{if(t.velocity.y>0&&t.position.y<=a.height+r||t.velocity.y<0&&t.position.y>=-r||t.velocity.x>0&&t.position.x<=a.width+r||t.velocity.x<0&&t.position.x>=-r)return;tt(t.position,n.canvas.size,m.origin,r,e)||n.particles.remove(t)}}}class Zi{constructor(t){this.container=t,this.modes=["out"]}update(t,e,i,s){if(!this.modes.includes(s))return;const o=this.container;switch(t.outType){case"inside":{const{x:e,y:i}=t.velocity,s=m.origin;s.length=t.moveCenter.radius,s.angle=t.velocity.angle+Math.PI,s.addTo(m.create(t.moveCenter));const{dx:n,dy:a}=R(t.position,s);if(e<=0&&n>=0||i<=0&&a>=0||e>=0&&n<=0||i>=0&&a<=0)return;t.position.x=Math.floor(z({min:0,max:o.canvas.size.width})),t.position.y=Math.floor(z({min:0,max:o.canvas.size.height}));const{dx:r,dy:c}=R(t.position,t.moveCenter);t.direction=Math.atan2(-c,-r),t.velocity.angle=t.direction;break}default:if(tt(t.position,o.canvas.size,m.origin,t.getRadius(),e))return;switch(t.outType){case"outside":{t.position.x=Math.floor(z({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.x,t.position.y=Math.floor(z({min:-t.moveCenter.radius,max:t.moveCenter.radius}))+t.moveCenter.y;const{dx:e,dy:i}=R(t.position,t.moveCenter);t.moveCenter.radius&&(t.direction=Math.atan2(i,e),t.velocity.angle=t.direction);break}case"normal":{const i=t.options.move.warp,s=o.canvas.size,n={bottom:s.height+t.getRadius()+t.offset.y,left:-t.getRadius()-t.offset.x,right:s.width+t.getRadius()+t.offset.x,top:-t.getRadius()-t.offset.y},a=t.getRadius(),r=it(t.position,a);"right"===e&&r.left>s.width+t.offset.x?(t.position.x=n.left,t.initialPosition.x=t.position.x,i||(t.position.y=_()*s.height,t.initialPosition.y=t.position.y)):"left"===e&&r.right<-t.offset.x&&(t.position.x=n.right,t.initialPosition.x=t.position.x,i||(t.position.y=_()*s.height,t.initialPosition.y=t.position.y)),"bottom"===e&&r.top>s.height+t.offset.y?(i||(t.position.x=_()*s.width,t.initialPosition.x=t.position.x),t.position.y=n.top,t.initialPosition.y=t.position.y):"top"===e&&r.bottom<-t.offset.y&&(i||(t.position.x=_()*s.width,t.initialPosition.x=t.position.x),t.position.y=n.bottom,t.initialPosition.y=t.position.y);break}}}}}class Qi{constructor(t){this.container=t,this._updateOutMode=(t,e,i,s)=>{for(const o of this.updaters)o.update(t,s,e,i)},this.updaters=[new Xi(t),new Yi(t),new Zi(t),new Ji(t)]}init(){}isEnabled(t){return!t.destroyed&&!t.spawning}update(t,e){const i=t.options.move.outModes;this._updateOutMode(t,e,i.bottom??i.default,"bottom"),this._updateOutMode(t,e,i.left??i.default,"left"),this._updateOutMode(t,e,i.right??i.default,"right"),this._updateOutMode(t,e,i.top??i.default,"top")}}class Ki{init(t){const e=t.container,i=t.options.size.animation;i.enable&&(t.size.velocity=(t.retina.sizeAnimationSpeed??e.retina.sizeAnimationSpeed)/100*e.retina.reduceFactor,i.sync||(t.size.velocity*=_()))}isEnabled(t){return!t.destroyed&&!t.spawning&&t.size.enable&&((t.size.maxLoops??0)<=0||(t.size.maxLoops??0)>0&&(t.size.loops??0)<(t.size.maxLoops??0))}reset(t){t.size.loops=0}update(t,e){this.isEnabled(t)&&function(t,e){const i=t.size;if(t.destroyed||!i||!i.enable||(i.maxLoops??0)>0&&(i.loops??0)>(i.maxLoops??0))return;const s=(i.velocity??0)*e.factor,o=i.min,n=i.max,a=i.decay??1;if(i.time||(i.time=0),(i.delayTime??0)>0&&i.time<(i.delayTime??0)&&(i.time+=e.value),!((i.delayTime??0)>0&&i.time<(i.delayTime??0))){switch(i.status){case"increasing":i.value>=n?(i.status="decreasing",i.loops||(i.loops=0),i.loops++):i.value+=s;break;case"decreasing":i.value<=o?(i.status="increasing",i.loops||(i.loops=0),i.loops++):i.value-=s}i.velocity&&1!==a&&(i.velocity*=a),function(t,e,i,s){switch(t.options.size.animation.destroy){case"max":e>=s&&t.destroy();break;case"min":e<=i&&t.destroy()}}(t,i.value,o,n),t.destroyed||(i.value=k(i.value,o,n))}}(t,e)}}async function ts(t,e=!0){await async function(t,e=!0){await t.addMover("base",(()=>new Ui),e)}(t,!1),await async function(t,e=!0){await t.addShape("circle",new $i,e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("color",(t=>new ji(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("opacity",(t=>new Ni(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("outModes",(t=>new Qi(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("size",(()=>new Ki),e)}(t,!1),await t.refresh(e)}class es{constructor(){this.distance=200,this.duration=.4,this.easing="ease-out-quad",this.factor=1,this.maxSpeed=50,this.speed=1}load(t){t&&(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed),void 0!==t.speed&&(this.speed=t.speed))}}class is extends Bi{constructor(t,e){super(e),this._clickAttract=()=>{const t=this.container;t.attract||(t.attract={particles:[]});const{attract:e}=t;if(e.finish||(e.count||(e.count=0),e.count++,e.count===t.particles.count&&(e.finish=!0)),e.clicking){const e=t.interactivity.mouse.clickPosition,i=t.retina.attractModeDistance;if(!i||i<0||!e)return;this._processAttract(e,i,new _i(e.x,e.y,i))}else!1===e.clicking&&(e.particles=[])},this._hoverAttract=()=>{const t=this.container,e=t.interactivity.mouse.position,i=t.retina.attractModeDistance;!i||i<0||!e||this._processAttract(e,i,new _i(e.x,e.y,i))},this._processAttract=(t,e,i)=>{const s=this.container,o=s.actualOptions.interactivity.modes.attract;if(!o)return;const n=s.particles.quadTree.query(i,(t=>this.isEnabled(t)));for(const i of n){const{dx:s,dy:n,distance:a}=R(i.position,t),r=o.speed*o.factor,c=k(w(o.easing)(1-a/e)*r,0,o.maxSpeed),l=m.create(0===a?r:s/a*c,0===a?r:n/a*c);i.position.subFrom(l)}},this._engine=t,e.attract||(e.attract={particles:[]}),this.handleClickMode=t=>{const i=this.container.actualOptions.interactivity.modes.attract;if(i&&"attract"===t){e.attract||(e.attract={particles:[]}),e.attract.clicking=!0,e.attract.count=0;for(const t of e.attract.particles)this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);e.attract.particles=[],e.attract.finish=!1,setTimeout((()=>{e.destroyed||(e.attract||(e.attract={particles:[]}),e.attract.clicking=!1)}),1e3*i.duration)}}}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.attract;e&&(t.retina.attractModeDistance=e.distance*t.retina.pixelRatio)}async interact(){const t=this.container,e=t.actualOptions,i=t.interactivity.status===r,s=e.interactivity.events,o=s.onHover.enable,n=s.onHover.mode,a=s.onClick.enable,c=s.onClick.mode;i&&o&&J("attract",n)?this._hoverAttract():a&&J("attract",c)&&this._clickAttract()}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events;if(!(s.position&&o.onHover.enable||s.clickPosition&&o.onClick.enable))return!1;const n=o.onHover.mode,a=o.onClick.mode;return J("attract",n)||J("attract",a)}loadModeOptions(t,...e){t.attract||(t.attract=new es);for(const i of e)t.attract.load(i?.attract)}reset(){}}class ss{constructor(){this.distance=200}load(t){t&&void 0!==t.distance&&(this.distance=t.distance)}}class os extends Bi{constructor(t){super(t),this._processBounce=(t,e,i)=>{const s=this.container.particles.quadTree.query(i,(t=>this.isEnabled(t)));for(const o of s)i instanceof _i?lt(ct(o),{position:t,radius:e,mass:e**2*Math.PI/2,velocity:m.origin,factor:m.origin}):i instanceof xi&&ht(o,it(t,e))},this._processMouseBounce=()=>{const t=this.container,e=10*t.retina.pixelRatio,i=t.interactivity.mouse.position,s=t.retina.bounceModeDistance;!s||s<0||!i||this._processBounce(i,s,new _i(i.x,i.y,s+e))},this._singleSelectorBounce=(t,e)=>{const i=this.container,s=document.querySelectorAll(t);s.length&&s.forEach((t=>{const s=t,o=i.retina.pixelRatio,n={x:(s.offsetLeft+s.offsetWidth/2)*o,y:(s.offsetTop+s.offsetHeight/2)*o},a=s.offsetWidth/2*o,r=10*o,c="circle"===e.type?new _i(n.x,n.y,a+r):new xi(s.offsetLeft*o-r,s.offsetTop*o-r,s.offsetWidth*o+2*r,s.offsetHeight*o+2*r);this._processBounce(n,a,c)}))}}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.bounce;e&&(t.retina.bounceModeDistance=e.distance*t.retina.pixelRatio)}async interact(){const t=this.container,e=t.actualOptions.interactivity.events,i=t.interactivity.status===r,s=e.onHover.enable,o=e.onHover.mode,n=e.onDiv;i&&s&&J("bounce",o)?this._processMouseBounce():nt("bounce",n,((t,e)=>this._singleSelectorBounce(t,e)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,n=o.onDiv;return s.position&&o.onHover.enable&&J("bounce",o.onHover.mode)||ot("bounce",n)}loadModeOptions(t,...e){t.bounce||(t.bounce=new ss);for(const i of e)t.bounce.load(i?.bounce)}reset(){}}class ns{constructor(){this.distance=200,this.duration=.4,this.mix=!1}load(t){if(t){if(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.mix&&(this.mix=t.mix),void 0!==t.opacity&&(this.opacity=t.opacity),void 0!==t.color){const e=kt(this.color)?void 0:this.color;this.color=dt(t.color,(t=>le.create(e,t)))}void 0!==t.size&&(this.size=t.size)}}}class as extends ns{constructor(){super(),this.selectors=[]}get ids(){return dt(this.selectors,(t=>t.replace("#","")))}set ids(t){this.selectors=dt(t,(t=>`#${t}`))}load(t){super.load(t),t&&(void 0!==t.ids&&(this.ids=t.ids),void 0!==t.selectors&&(this.selectors=t.selectors))}}class rs extends ns{load(t){super.load(t),t&&(this.divs=dt(t.divs,(t=>{const e=new as;return e.load(t),e})))}}function cs(t,e,i,s){if(e>=i){return k(t+(e-i)*s,t,e)}if(e<i){return k(t-(i-e)*s,e,t)}}class ls extends Bi{constructor(t){super(t),this._clickBubble=()=>{const t=this.container,e=t.actualOptions,i=t.interactivity.mouse.clickPosition,s=e.interactivity.modes.bubble;if(!s||!i)return;t.bubble||(t.bubble={});const o=t.retina.bubbleModeDistance;if(!o||o<0)return;const n=t.particles.quadTree.queryCircle(i,o,(t=>this.isEnabled(t))),{bubble:a}=t;for(const e of n){if(!a.clicking)continue;e.bubble.inRange=!a.durationEnd;const n=I(e.getPosition(),i),r=((new Date).getTime()-(t.interactivity.mouse.clickTime||0))/1e3;r>s.duration&&(a.durationEnd=!0),r>2*s.duration&&(a.clicking=!1,a.durationEnd=!1);const c={bubbleObj:{optValue:t.retina.bubbleModeSize,value:e.bubble.radius},particlesObj:{optValue:S(e.options.size.value)*t.retina.pixelRatio,value:e.size.value},type:"size"};this._process(e,n,r,c);const l={bubbleObj:{optValue:s.opacity,value:e.bubble.opacity},particlesObj:{optValue:S(e.options.opacity.value),value:e.opacity?.value??1},type:"opacity"};this._process(e,n,r,l),!a.durationEnd&&n<=o?this._hoverBubbleColor(e,n):delete e.bubble.color}},this._hoverBubble=()=>{const t=this.container,e=t.interactivity.mouse.position,i=t.retina.bubbleModeDistance;if(!i||i<0||void 0===e)return;const s=t.particles.quadTree.queryCircle(e,i,(t=>this.isEnabled(t)));for(const o of s){o.bubble.inRange=!0;const s=I(o.getPosition(),e),a=1-s/i;s<=i?a>=0&&t.interactivity.status===r&&(this._hoverBubbleSize(o,a),this._hoverBubbleOpacity(o,a),this._hoverBubbleColor(o,a)):this.reset(o),t.interactivity.status===n&&this.reset(o)}},this._hoverBubbleColor=(t,e,i)=>{const s=this.container.actualOptions,o=i??s.interactivity.modes.bubble;if(o){if(!t.bubble.finalColor){const e=o.color;if(!e)return;const i=ut(e);t.bubble.finalColor=Dt(i)}if(t.bubble.finalColor)if(o.mix){t.bubble.color=void 0;const i=t.getFillColor();t.bubble.color=i?Et(Wt(i,t.bubble.finalColor,1-e,e)):t.bubble.finalColor}else t.bubble.color=t.bubble.finalColor}},this._hoverBubbleOpacity=(t,e,i)=>{const s=this.container.actualOptions,o=i?.opacity??s.interactivity.modes.bubble?.opacity;if(!o)return;const n=t.options.opacity.value,a=cs(t.opacity?.value??1,o,S(n),e);void 0!==a&&(t.bubble.opacity=a)},this._hoverBubbleSize=(t,e,i)=>{const s=this.container,o=i?.size?i.size*s.retina.pixelRatio:s.retina.bubbleModeSize;if(void 0===o)return;const n=S(t.options.size.value)*s.retina.pixelRatio,a=cs(t.size.value,o,n,e);void 0!==a&&(t.bubble.radius=a)},this._process=(t,e,i,s)=>{const o=this.container,n=s.bubbleObj.optValue,a=o.actualOptions.interactivity.modes.bubble;if(!a||void 0===n)return;const r=a.duration,c=o.retina.bubbleModeDistance,l=s.particlesObj.optValue,h=s.bubbleObj.value,d=s.particlesObj.value||0,u=s.type;if(c&&!(c<0)&&n!==l)if(o.bubble||(o.bubble={}),o.bubble.durationEnd)h&&("size"===u&&delete t.bubble.radius,"opacity"===u&&delete t.bubble.opacity);else if(e<=c){if((h??d)!==n){const e=d-i*(d-n)/r;"size"===u&&(t.bubble.radius=e),"opacity"===u&&(t.bubble.opacity=e)}}else"size"===u&&delete t.bubble.radius,"opacity"===u&&delete t.bubble.opacity},this._singleSelectorHover=(t,e,i)=>{const s=this.container,o=document.querySelectorAll(e),n=s.actualOptions.interactivity.modes.bubble;n&&o.length&&o.forEach((e=>{const o=e,a=s.retina.pixelRatio,r={x:(o.offsetLeft+o.offsetWidth/2)*a,y:(o.offsetTop+o.offsetHeight/2)*a},c=o.offsetWidth/2*a,l="circle"===i.type?new _i(r.x,r.y,c):new xi(o.offsetLeft*a,o.offsetTop*a,o.offsetWidth*a,o.offsetHeight*a),h=s.particles.quadTree.query(l,(t=>this.isEnabled(t)));for(const e of h){if(!l.contains(e.getPosition()))continue;e.bubble.inRange=!0;const i=rt(n.divs,o);e.bubble.div&&e.bubble.div===o||(this.clear(e,t,!0),e.bubble.div=o),this._hoverBubbleSize(e,1,i),this._hoverBubbleOpacity(e,1,i),this._hoverBubbleColor(e,1,i)}}))},t.bubble||(t.bubble={}),this.handleClickMode=e=>{"bubble"===e&&(t.bubble||(t.bubble={}),t.bubble.clicking=!0)}}clear(t,e,i){t.bubble.inRange&&!i||(delete t.bubble.div,delete t.bubble.opacity,delete t.bubble.radius,delete t.bubble.color)}init(){const t=this.container,e=t.actualOptions.interactivity.modes.bubble;e&&(t.retina.bubbleModeDistance=e.distance*t.retina.pixelRatio,void 0!==e.size&&(t.retina.bubbleModeSize=e.size*t.retina.pixelRatio))}async interact(t){const e=this.container.actualOptions.interactivity.events,i=e.onHover,s=e.onClick,o=i.enable,n=i.mode,a=s.enable,r=s.mode,c=e.onDiv;o&&J("bubble",n)?this._hoverBubble():a&&J("bubble",r)?this._clickBubble():nt("bubble",c,((e,i)=>this._singleSelectorHover(t,e,i)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,{onClick:n,onDiv:a,onHover:r}=o,c=ot("bubble",a);return!!(c||r.enable&&s.position||n.enable&&s.clickPosition)&&(J("bubble",r.mode)||J("bubble",n.mode)||c)}loadModeOptions(t,...e){t.bubble||(t.bubble=new rs);for(const i of e)t.bubble.load(i?.bubble)}reset(t){t.bubble.inRange=!1}}class hs{constructor(){this.opacity=.5}load(t){t&&void 0!==t.opacity&&(this.opacity=t.opacity)}}class ds{constructor(){this.distance=80,this.links=new hs,this.radius=60}get lineLinked(){return this.links}set lineLinked(t){this.links=t}get line_linked(){return this.links}set line_linked(t){this.links=t}load(t){t&&(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links??t.lineLinked??t.line_linked),void 0!==t.radius&&(this.radius=t.radius))}}function us(t,e,i,s){const o=t.actualOptions.interactivity.modes.connect;if(o)return function(t,e,i,s){const o=Math.floor(i.getRadius()/e.getRadius()),n=e.getFillColor(),a=i.getFillColor();if(!n||!a)return;const r=e.getPosition(),c=i.getPosition(),l=Wt(n,a,e.getRadius(),i.getRadius()),h=t.createLinearGradient(r.x,r.y,c.x,c.y);return h.addColorStop(0,Ht(n,s)),h.addColorStop(o>1?1:o,Vt(l,s)),h.addColorStop(1,Ht(a,s)),h}(e,i,s,o.links.opacity)}function ps(t,e,i){t.canvas.draw((s=>{const o=us(t,s,e,i);if(!o)return;const n=e.getPosition(),a=i.getPosition();!function(t,e,i,s,o){Xt(t,s,o),t.lineWidth=e,t.strokeStyle=i,t.stroke()}(s,e.retina.linksWidth??0,o,n,a)}))}class fs extends Bi{constructor(t){super(t)}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.connect;e&&(t.retina.connectModeDistance=e.distance*t.retina.pixelRatio,t.retina.connectModeRadius=e.radius*t.retina.pixelRatio)}async interact(){const t=this.container;if(t.actualOptions.interactivity.events.onHover.enable&&"pointermove"===t.interactivity.status){const e=t.interactivity.mouse.position;if(!t.retina.connectModeDistance||t.retina.connectModeDistance<0||!t.retina.connectModeRadius||t.retina.connectModeRadius<0||!e)return;const i=Math.abs(t.retina.connectModeRadius),s=t.particles.quadTree.queryCircle(e,i,(t=>this.isEnabled(t)));let o=0;for(const e of s){const i=e.getPosition();for(const n of s.slice(o+1)){const s=n.getPosition(),o=Math.abs(t.retina.connectModeDistance),a=Math.abs(i.x-s.x),r=Math.abs(i.y-s.y);a<o&&r<o&&ps(t,e,n)}++o}}}isEnabled(t){const e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return!(!s.onHover.enable||!i.position)&&J("connect",s.onHover.mode)}loadModeOptions(t,...e){t.connect||(t.connect=new ds);for(const i of e)t.connect.load(i?.connect)}reset(){}}class vs{constructor(){this.blink=!1,this.consent=!1,this.opacity=1}load(t){t&&(void 0!==t.blink&&(this.blink=t.blink),void 0!==t.color&&(this.color=le.create(this.color,t.color)),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.opacity&&(this.opacity=t.opacity))}}class ms{constructor(){this.distance=100,this.links=new vs}get lineLinked(){return this.links}set lineLinked(t){this.links=t}get line_linked(){return this.links}set line_linked(t){this.links=t}load(t){t&&(void 0!==t.distance&&(this.distance=t.distance),this.links.load(t.links??t.lineLinked??t.line_linked))}}function gs(t,e,i,s,o){t.canvas.draw((t=>{const n=e.getPosition();!function(t,e,i,s,o,n){Xt(t,i,s),t.strokeStyle=Vt(o,n),t.lineWidth=e,t.stroke()}(t,e.retina.linksWidth??0,n,o,i,s)}))}class ys extends Bi{constructor(t){super(t)}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.grab;e&&(t.retina.grabModeDistance=e.distance*t.retina.pixelRatio)}async interact(){const t=this.container,e=t.actualOptions.interactivity;if(!e.modes.grab||!e.events.onHover.enable||t.interactivity.status!==r)return;const i=t.interactivity.mouse.position;if(!i)return;const s=t.retina.grabModeDistance;if(!s||s<0)return;const o=t.particles.quadTree.queryCircle(i,s,(t=>this.isEnabled(t)));for(const n of o){const o=I(n.getPosition(),i);if(o>s)continue;const a=e.modes.grab.links,r=a.opacity,c=r-o*r/s;if(c<=0)continue;const l=a.color??n.options.links?.color;if(!t.particles.grabLineColor&&l){const i=e.modes.grab.links;t.particles.grabLineColor=$t(l,i.blink,i.consent)}const h=Ut(n,void 0,t.particles.grabLineColor);h&&gs(t,n,h,c,i)}}isEnabled(t){const e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return s.onHover.enable&&!!i.position&&J("grab",s.onHover.mode)}loadModeOptions(t,...e){t.grab||(t.grab=new ms);for(const i of e)t.grab.load(i?.grab)}reset(){}}class bs extends Bi{constructor(t){super(t),this.handleClickMode=t=>{if("pause"!==t)return;const e=this.container;e.getAnimationStatus()?e.pause():e.play()}}clear(){}init(){}async interact(){}isEnabled(){return!0}reset(){}}class ws{constructor(){this.default=!0,this.groups=[],this.quantity=4}get particles_nb(){return this.quantity}set particles_nb(t){this.quantity=O(t)}load(t){if(!t)return;void 0!==t.default&&(this.default=t.default),void 0!==t.groups&&(this.groups=t.groups.map((t=>t))),this.groups.length||(this.default=!0);const e=t.quantity??t.particles_nb;void 0!==e&&(this.quantity=O(e))}}class xs extends Bi{constructor(t){super(t),this.handleClickMode=t=>{if("push"!==t)return;const e=this.container,i=e.actualOptions.interactivity.modes.push;if(!i)return;const s=C(i.quantity);if(s<=0)return;const o=K([void 0,...i.groups]),n=void 0!==o?e.actualOptions.particles.groups[o]:void 0;e.particles.push(s,e.interactivity.mouse,n,o)}}clear(){}init(){}async interact(){}isEnabled(){return!0}loadModeOptions(t,...e){t.push||(t.push=new ws);for(const i of e)t.push.load(i?.push)}reset(){}}class _s{constructor(){this.quantity=2}get particles_nb(){return this.quantity}set particles_nb(t){this.quantity=O(t)}load(t){if(!t)return;const e=t.quantity??t.particles_nb;void 0!==e&&(this.quantity=O(e))}}class ks extends Bi{constructor(t){super(t),this.handleClickMode=t=>{const e=this.container,i=e.actualOptions;if(!i.interactivity.modes.remove||"remove"!==t)return;const s=C(i.interactivity.modes.remove.quantity);e.particles.removeQuantity(s)}}clear(){}init(){}async interact(){}isEnabled(){return!0}loadModeOptions(t,...e){t.remove||(t.remove=new _s);for(const i of e)t.remove.load(i?.remove)}reset(){}}class Ms{constructor(){this.distance=200,this.duration=.4,this.factor=100,this.speed=1,this.maxSpeed=50,this.easing="ease-out-quad"}load(t){t&&(void 0!==t.distance&&(this.distance=t.distance),void 0!==t.duration&&(this.duration=t.duration),void 0!==t.easing&&(this.easing=t.easing),void 0!==t.factor&&(this.factor=t.factor),void 0!==t.speed&&(this.speed=t.speed),void 0!==t.maxSpeed&&(this.maxSpeed=t.maxSpeed))}}class zs extends Ms{constructor(){super(),this.selectors=[]}get ids(){return dt(this.selectors,(t=>t.replace("#","")))}set ids(t){this.selectors=dt(t,(t=>`#${t}`))}load(t){super.load(t),t&&(void 0!==t.ids&&(this.ids=t.ids),void 0!==t.selectors&&(this.selectors=t.selectors))}}class Cs extends Ms{load(t){super.load(t),t&&(this.divs=dt(t.divs,(t=>{const e=new zs;return e.load(t),e})))}}class Ps extends Bi{constructor(t,e){super(e),this._clickRepulse=()=>{const t=this.container,e=t.actualOptions.interactivity.modes.repulse;if(!e)return;const i=t.repulse||{particles:[]};if(i.finish||(i.count||(i.count=0),i.count++,i.count===t.particles.count&&(i.finish=!0)),i.clicking){const s=t.retina.repulseModeDistance;if(!s||s<0)return;const o=Math.pow(s/6,3),n=t.interactivity.mouse.clickPosition;if(void 0===n)return;const a=new _i(n.x,n.y,o),r=t.particles.quadTree.query(a,(t=>this.isEnabled(t)));for(const t of r){const{dx:s,dy:a,distance:r}=R(n,t.position),c=r**2,l=-o*e.speed/c;if(c<=o){i.particles.push(t);const e=m.create(s,a);e.length=l,t.velocity.setTo(e)}}}else if(!1===i.clicking){for(const t of i.particles)t.velocity.setTo(t.initialVelocity);i.particles=[]}},this._hoverRepulse=()=>{const t=this.container,e=t.interactivity.mouse.position,i=t.retina.repulseModeDistance;!i||i<0||!e||this._processRepulse(e,i,new _i(e.x,e.y,i))},this._processRepulse=(t,e,i,s)=>{const o=this.container,n=o.particles.quadTree.query(i,(t=>this.isEnabled(t))),a=o.actualOptions.interactivity.modes.repulse;if(a)for(const i of n){const{dx:o,dy:n,distance:r}=R(i.position,t),c=(s?.speed??a.speed)*a.factor,l=k(w(a.easing)(1-r/e)*c,0,a.maxSpeed),h=m.create(0===r?c:o/r*l,0===r?c:n/r*l);i.position.addTo(h)}},this._singleSelectorRepulse=(t,e)=>{const i=this.container,s=i.actualOptions.interactivity.modes.repulse;if(!s)return;const o=document.querySelectorAll(t);o.length&&o.forEach((t=>{const o=t,n=i.retina.pixelRatio,a={x:(o.offsetLeft+o.offsetWidth/2)*n,y:(o.offsetTop+o.offsetHeight/2)*n},r=o.offsetWidth/2*n,c="circle"===e.type?new _i(a.x,a.y,r):new xi(o.offsetLeft*n,o.offsetTop*n,o.offsetWidth*n,o.offsetHeight*n),l=rt(s.divs,o);this._processRepulse(a,r,c,l)}))},this._engine=t,e.repulse||(e.repulse={particles:[]}),this.handleClickMode=t=>{const i=this.container.actualOptions.interactivity.modes.repulse;if(!i||"repulse"!==t)return;e.repulse||(e.repulse={particles:[]});const s=e.repulse;s.clicking=!0,s.count=0;for(const t of e.repulse.particles)this.isEnabled(t)&&t.velocity.setTo(t.initialVelocity);s.particles=[],s.finish=!1,setTimeout((()=>{e.destroyed||(s.clicking=!1)}),1e3*i.duration)}}clear(){}init(){const t=this.container,e=t.actualOptions.interactivity.modes.repulse;e&&(t.retina.repulseModeDistance=e.distance*t.retina.pixelRatio)}async interact(){const t=this.container,e=t.actualOptions,i=t.interactivity.status===r,s=e.interactivity.events,o=s.onHover,n=o.enable,a=o.mode,c=s.onClick,l=c.enable,h=c.mode,d=s.onDiv;i&&n&&J("repulse",a)?this._hoverRepulse():l&&J("repulse",h)?this._clickRepulse():nt("repulse",d,((t,e)=>this._singleSelectorRepulse(t,e)))}isEnabled(t){const e=this.container,i=e.actualOptions,s=e.interactivity.mouse,o=(t?.interactivity??i.interactivity).events,n=o.onDiv,a=o.onHover,r=o.onClick,c=ot("repulse",n);if(!(c||a.enable&&s.position||r.enable&&s.clickPosition))return!1;const l=a.mode,h=r.mode;return J("repulse",l)||J("repulse",h)||c}loadModeOptions(t,...e){t.repulse||(t.repulse=new Cs);for(const i of e)t.repulse.load(i?.repulse)}reset(){}}class Ss{constructor(){this.factor=3,this.radius=200}load(t){t&&(void 0!==t.factor&&(this.factor=t.factor),void 0!==t.radius&&(this.radius=t.radius))}}class Os extends Bi{constructor(t){super(t)}clear(t,e,i){t.slow.inRange&&!i||(t.slow.factor=1)}init(){const t=this.container,e=t.actualOptions.interactivity.modes.slow;e&&(t.retina.slowModeRadius=e.radius*t.retina.pixelRatio)}async interact(){}isEnabled(t){const e=this.container,i=e.interactivity.mouse,s=(t?.interactivity??e.actualOptions.interactivity).events;return s.onHover.enable&&!!i.position&&J("slow",s.onHover.mode)}loadModeOptions(t,...e){t.slow||(t.slow=new Ss);for(const i of e)t.slow.load(i?.slow)}reset(t){t.slow.inRange=!1;const e=this.container,i=e.actualOptions,s=e.interactivity.mouse.position,o=e.retina.slowModeRadius,n=i.interactivity.modes.slow;if(!n||!o||o<0||!s)return;const a=I(s,t.getPosition()),r=a/o,c=n.factor,{slow:l}=t;a>o||(l.inRange=!0,l.factor=r/c)}}const Ts=[0,4,2,1],Rs=[8,8,4,2];class Is{constructor(t){this.pos=0,this.data=new Uint8ClampedArray(t)}getString(t){const e=this.data.slice(this.pos,this.pos+t);return this.pos+=e.length,e.reduce(((t,e)=>t+String.fromCharCode(e)),"")}nextByte(){return this.data[this.pos++]}nextTwoBytes(){return this.pos+=2,this.data[this.pos-2]+(this.data[this.pos-1]<<8)}readSubBlocks(){let t="",e=0;do{e=this.data[this.pos++];for(let i=e;--i>=0;t+=String.fromCharCode(this.data[this.pos++]));}while(0!==e);return t}readSubBlocksBin(){let t=0,e=0;for(let i=0;0!==(t=this.data[this.pos+i]);i+=t+1)e+=t;const i=new Uint8Array(e);for(let e=0;0!==(t=this.data[this.pos++]);)for(let s=t;--s>=0;i[e++]=this.data[this.pos++]);return i}skipSubBlocks(){for(;0!==this.data[this.pos];this.pos+=this.data[this.pos]+1);this.pos++}}function Ds(t,e){const i=[];for(let s=0;s<e;s++)i.push({r:t.data[t.pos],g:t.data[t.pos+1],b:t.data[t.pos+2]}),t.pos+=3;return i}async function Es(t,e,i,s,o,n){switch(t.nextByte()){case 59:return!0;case 44:await async function(t,e,i,s,o,n){const a=e.frames[s(!0)];a.left=t.nextTwoBytes(),a.top=t.nextTwoBytes(),a.width=t.nextTwoBytes(),a.height=t.nextTwoBytes();const r=t.nextByte(),c=128==(128&r),l=64==(64&r);a.sortFlag=32==(32&r),a.reserved=(24&r)>>>3;const h=1<<1+(7&r);c&&(a.localColorTable=Ds(t,h));const d=t=>{const{r:s,g:n,b:r}=(c?a.localColorTable:e.globalColorTable)[t];return{r:s,g:n,b:r,a:t===o(null)?i?~~((s+n+r)/3):0:255}},u=(()=>{try{return new ImageData(a.width,a.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==u)throw new EvalError("GIF frame size is to large");const p=t.nextByte(),f=t.readSubBlocksBin(),v=1<<p,m=(t,e)=>{const i=t>>>3,s=7&t;return(f[i]+(f[i+1]<<8)+(f[i+2]<<16)&(1<<e)-1<<s)>>>s};if(l){for(let i=0,o=p+1,r=0,c=[[0]],l=0;l<4;l++){if(Ts[l]<a.height)for(let t=0,e=0;;){const s=i;if(i=m(r,o),r+=o+1,i===v){o=p+1,c.length=v+2;for(let t=0;t<c.length;t++)c[t]=t<v?[t]:[]}else{i>=c.length?c.push(c[s].concat(c[s][0])):s!==v&&c.push(c[s].concat(c[i][0]));for(let s=0;s<c[i].length;s++){const{r:o,g:n,b:r,a:h}=d(c[i][s]);u.data.set([o,n,r,h],Ts[l]*a.width+Rs[l]*e+t%(4*a.width)),t+=4}c.length===1<<o&&o<12&&o++}if(t===4*a.width*(e+1)&&(e++,Ts[l]+Rs[l]*e>=a.height))break}n?.(t.pos/(t.data.length-1),s(!1)+1,u,{x:a.left,y:a.top},{width:e.width,height:e.height})}a.image=u,a.bitmap=await createImageBitmap(u)}else{for(let t=0,e=p+1,i=0,s=[[0]],o=-4;;){const n=t;if(t=m(i,e),i+=e,t===v){e=p+1,s.length=v+2;for(let t=0;t<s.length;t++)s[t]=t<v?[t]:[]}else{if(t===v+1)break;t>=s.length?s.push(s[n].concat(s[n][0])):n!==v&&s.push(s[n].concat(s[t][0]));for(let e=0;e<s[t].length;e++){const{r:i,g:n,b:a,a:r}=d(s[t][e]);u.data.set([i,n,a,r],o+=4)}s.length>=1<<e&&e<12&&e++}}a.image=u,a.bitmap=await createImageBitmap(u),n?.((t.pos+1)/t.data.length,s(!1)+1,a.image,{x:a.left,y:a.top},{width:e.width,height:e.height})}}(t,e,i,s,o,n);break;case 33:await async function(t,e,i,s){switch(t.nextByte()){case 249:{const o=e.frames[i(!1)];t.pos++;const n=t.nextByte();o.GCreserved=(224&n)>>>5,o.disposalMethod=(28&n)>>>2,o.userInputDelayFlag=2==(2&n);const a=1==(1&n);o.delayTime=10*t.nextTwoBytes();const r=t.nextByte();a&&s(r),t.pos++;break}case 255:{t.pos++;const i={identifier:t.getString(8),authenticationCode:t.getString(3),data:t.readSubBlocksBin()};e.applicationExtensions.push(i);break}case 254:e.comments.push([i(!1),t.readSubBlocks()]);break;case 1:if(0===e.globalColorTable.length)throw new EvalError("plain text extension without global color table");t.pos++,e.frames[i(!1)].plainTextData={left:t.nextTwoBytes(),top:t.nextTwoBytes(),width:t.nextTwoBytes(),height:t.nextTwoBytes(),charSize:{width:t.nextTwoBytes(),height:t.nextTwoBytes()},foregroundColor:t.nextByte(),backgroundColor:t.nextByte(),text:t.readSubBlocks()};break;default:t.skipSubBlocks()}}(t,e,s,o);break;default:throw new EvalError("undefined block found")}return!1}const Ls=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d.]+%?\))|currentcolor/gi;async function As(t){return new Promise((e=>{t.loading=!0;const i=new Image;t.element=i,i.addEventListener("load",(()=>{t.loading=!1,e()})),i.addEventListener("error",(()=>{t.element=void 0,t.error=!0,t.loading=!1,$().error(`${f} loading image: ${t.source}`),e()})),i.src=t.source}))}async function Bs(t){if("gif"===t.type){t.loading=!0;try{t.gifData=await async function(t,e,i){i||(i=!1);const s=await fetch(t);if(!s.ok&&404===s.status)throw new EvalError("file not found");const o=await s.arrayBuffer(),n={width:0,height:0,totalTime:0,colorRes:0,pixelAspectRatio:0,frames:[],sortFlag:!1,globalColorTable:[],backgroundImage:new ImageData(1,1,{colorSpace:"srgb"}),comments:[],applicationExtensions:[]},a=new Is(new Uint8ClampedArray(o));if("GIF89a"!==a.getString(6))throw new Error("not a supported GIF file");n.width=a.nextTwoBytes(),n.height=a.nextTwoBytes();const r=a.nextByte(),c=128==(128&r);n.colorRes=(112&r)>>>4,n.sortFlag=8==(8&r);const l=1<<1+(7&r),h=a.nextByte();n.pixelAspectRatio=a.nextByte(),0!==n.pixelAspectRatio&&(n.pixelAspectRatio=(n.pixelAspectRatio+15)/64),c&&(n.globalColorTable=Ds(a,l));const d=(()=>{try{return new ImageData(n.width,n.height,{colorSpace:"srgb"})}catch(t){if(t instanceof DOMException&&"IndexSizeError"===t.name)return null;throw t}})();if(null==d)throw new Error("GIF frame size is to large");const{r:u,g:p,b:f}=n.globalColorTable[h];d.data.set(c?[u,p,f,255]:[0,0,0,0]);for(let t=4;t<d.data.length;t*=2)d.data.copyWithin(t,0,t);n.backgroundImage=d;let v=-1,m=!0,g=-1;const y=t=>(t&&(m=!0),v),b=t=>(null!=t&&(g=t),g);try{do{m&&(n.frames.push({left:0,top:0,width:0,height:0,disposalMethod:0,image:new ImageData(1,1,{colorSpace:"srgb"}),plainTextData:null,userInputDelayFlag:!1,delayTime:0,sortFlag:!1,localColorTable:[],reserved:0,GCreserved:0}),v++,g=-1,m=!1)}while(!await Es(a,n,i,y,b,e));n.frames.length--;for(const t of n.frames){if(t.userInputDelayFlag&&0===t.delayTime){n.totalTime=1/0;break}n.totalTime+=t.delayTime}return n}catch(t){if(t instanceof EvalError)throw new Error(`error while parsing frame ${v} "${t.message}"`);throw t}}(t.source),t.gifLoopCount=function(t){for(const e of t.applicationExtensions)if(e.identifier+e.authenticationCode==="NETSCAPE2.0")return e.data[1]+(e.data[2]<<8);return NaN}(t.gifData)??0,0===t.gifLoopCount&&(t.gifLoopCount=1/0)}catch{t.error=!0}t.loading=!1}else await As(t)}async function Fs(t){if("svg"!==t.type)return void await As(t);t.loading=!0;const e=await fetch(t.source);e.ok?t.svgData=await e.text():($().error(`${f} Image not found`),t.error=!0),t.loading=!1}function qs(t,e,i,s){const o=function(t,e,i){const{svgData:s}=t;if(!s)return"";const o=Ht(e,i);if(s.includes("fill"))return s.replace(Ls,(()=>o));const n=s.indexOf(">");return`${s.substring(0,n)} fill="${o}"${s.substring(n)}`}(t,i,s.opacity?.value??1),n={color:i,gif:e.gif,data:{...t,svgData:o},loaded:!1,ratio:e.width/e.height,replaceColor:e.replaceColor??e.replace_color,source:e.src};return new Promise((e=>{const i=new Blob([o],{type:"image/svg+xml"}),s=URL||window.URL||window.webkitURL||window,a=s.createObjectURL(i),r=new Image;r.addEventListener("load",(()=>{n.loaded=!0,n.element=r,e(n),s.revokeObjectURL(a)})),r.addEventListener("error",(async()=>{s.revokeObjectURL(a);const i={...t,error:!1,loading:!0};await As(i),n.loaded=!0,n.element=i.element,e(n)})),r.src=a}))}class Vs{constructor(t){this.loadImageShape=async t=>{if(!this._engine.loadImage)throw new Error(`${f} image shape not initialized`);await this._engine.loadImage({gif:t.gif,name:t.name,replaceColor:t.replaceColor??t.replace_color??!1,src:t.src})},this._engine=t}addImage(t){this._engine.images||(this._engine.images=[]),this._engine.images.push(t)}draw(t,e,i,s,o){const n=e.image,a=n?.element;if(n){if(t.globalAlpha=s,n.gif&&n.gifData){const s=new OffscreenCanvas(n.gifData.width,n.gifData.height),a=s.getContext("2d");if(!a)throw new Error("could not create offscreen canvas context");a.imageSmoothingQuality="low",a.imageSmoothingEnabled=!1,a.clearRect(0,0,s.width,s.height),void 0===e.gifLoopCount&&(e.gifLoopCount=n.gifLoopCount??0);let r=e.gifFrame??0;const c={x:.5*-n.gifData.width,y:.5*-n.gifData.height},l=n.gifData.frames[r];if(void 0===e.gifTime&&(e.gifTime=0),!l.bitmap)return;switch(t.scale(i/n.gifData.width,i/n.gifData.height),l.disposalMethod){case 4:case 5:case 6:case 7:case 0:a.drawImage(l.bitmap,l.left,l.top),t.drawImage(s,c.x,c.y),a.clearRect(0,0,s.width,s.height);break;case 1:a.drawImage(l.bitmap,l.left,l.top),t.drawImage(s,c.x,c.y);break;case 2:a.drawImage(l.bitmap,l.left,l.top),t.drawImage(s,c.x,c.y),a.clearRect(0,0,s.width,s.height),0===n.gifData.globalColorTable.length?a.putImageData(n.gifData.frames[0].image,c.x+l.left,c.y+l.top):a.putImageData(n.gifData.backgroundImage,c.x,c.y);break;case 3:{const e=a.getImageData(0,0,s.width,s.height);a.drawImage(l.bitmap,l.left,l.top),t.drawImage(s,c.x,c.y),a.clearRect(0,0,s.width,s.height),a.putImageData(e,0,0)}}if(e.gifTime+=o.value,e.gifTime>l.delayTime){if(e.gifTime-=l.delayTime,++r>=n.gifData.frames.length){if(--e.gifLoopCount<=0)return;r=0,a.clearRect(0,0,s.width,s.height)}e.gifFrame=r}t.scale(n.gifData.width/i,n.gifData.height/i)}else if(a){const e=n.ratio,s={x:-i,y:-i};t.drawImage(a,s.x,s.y,2*i,2*i/e)}t.globalAlpha=1}}getSidesCount(){return 12}async init(t){const e=t.actualOptions;if(e.preload&&this._engine.loadImage)for(const t of e.preload)await this._engine.loadImage(t)}loadShape(t){if("image"!==t.shape&&"images"!==t.shape)return;this._engine.images||(this._engine.images=[]);const e=t.shapeData;this._engine.images.find((t=>t.name===e.name||t.source===e.src))||this.loadImageShape(e).then((()=>{this.loadShape(t)}))}particleInit(t,e){if("image"!==e.shape&&"images"!==e.shape)return;this._engine.images||(this._engine.images=[]);const i=this._engine.images,s=e.shapeData,o=e.getFillColor(),n=i.find((t=>t.name===s.name||t.source===s.src));if(!n)return;const a=s.replaceColor??s.replace_color??n.replaceColor;n.loading?setTimeout((()=>{this.particleInit(t,e)})):(async()=>{let t;t=n.svgData&&o?await qs(n,s,o,e):{color:o,data:n,element:n.element,gif:n.gif,gifData:n.gifData,gifLoopCount:n.gifLoopCount,loaded:!0,ratio:s.width&&s.height?s.width/s.height:n.ratio??1,replaceColor:a,source:s.src},t.ratio||(t.ratio=1);const i={image:t,fill:s.fill??e.fill,close:s.close??e.close};e.image=i.image,e.fill=i.fill,e.close=i.close})()}}class Hs{constructor(){this.src="",this.gif=!1}load(t){t&&(void 0!==t.gif&&(this.gif=t.gif),void 0!==t.height&&(this.height=t.height),void 0!==t.name&&(this.name=t.name),void 0!==t.replaceColor&&(this.replaceColor=t.replaceColor),void 0!==t.src&&(this.src=t.src),void 0!==t.width&&(this.width=t.width))}}class Ws{constructor(t){this.id="imagePreloader",this._engine=t}getPlugin(){return{}}loadOptions(t,e){if(!e||!e.preload)return;t.preload||(t.preload=[]);const i=t.preload;for(const t of e.preload){const e=i.find((e=>e.name===t.name||e.src===t.src));if(e)e.load(t);else{const e=new Hs;e.load(t),i.push(e)}}}needsPlugin(){return!0}}async function Us(t,e=!0){!function(t){t.loadImage||(t.loadImage=async e=>{if(!e.name&&!e.src)throw new Error(`${f} no image source provided`);if(t.images||(t.images=[]),!t.images.find((t=>t.name===e.name||t.source===e.src)))try{const i={gif:e.gif??!1,name:e.name??e.src,source:e.src,type:e.src.substring(e.src.length-3),error:!1,loading:!0,replaceColor:e.replaceColor,ratio:e.width&&e.height?e.width/e.height:void 0};t.images.push(i);const s=e.gif?Bs:e.replaceColor?Fs:As;await s(i)}catch{throw new Error(`${f} ${e.name??e.src} not found`)}})}(t);const i=new Ws(t);await t.addPlugin(i,e),await t.addShape(["image","images"],new Vs(t),e)}class $s extends Ee{constructor(){super(),this.sync=!1}load(t){t&&(super.load(t),void 0!==t.sync&&(this.sync=t.sync))}}class Gs extends Ee{constructor(){super(),this.random.minimumValue=1e-4,this.sync=!1}load(t){t&&(super.load(t),void 0!==t.sync&&(this.sync=t.sync))}}class js{constructor(){this.count=0,this.delay=new $s,this.duration=new Gs}load(t){t&&(void 0!==t.count&&(this.count=t.count),this.delay.load(t.delay),this.duration.load(t.duration))}}class Ns{constructor(t){this.container=t}init(t){const e=this.container,i=t.options.life;i&&(t.life={delay:e.retina.reduceFactor?C(i.delay.value)*(i.delay.sync?1:_())/e.retina.reduceFactor*1e3:0,delayTime:0,duration:e.retina.reduceFactor?C(i.duration.value)*(i.duration.sync?1:_())/e.retina.reduceFactor*1e3:0,time:0,count:i.count},t.life.duration<=0&&(t.life.duration=-1),t.life.count<=0&&(t.life.count=-1),t.life&&(t.spawning=t.life.delay>0))}isEnabled(t){return!t.destroyed}loadOptions(t,...e){t.life||(t.life=new js);for(const i of e)t.life.load(i?.life)}update(t,e){if(!this.isEnabled(t)||!t.life)return;const i=t.life;let s=!1;if(t.spawning){if(i.delayTime+=e.value,!(i.delayTime>=t.life.delay))return;s=!0,t.spawning=!1,i.delayTime=0,i.time=0}if(-1===i.duration)return;if(t.spawning)return;if(s?i.time=0:i.time+=e.value,i.time<i.duration)return;if(i.time=0,t.life.count>0&&t.life.count--,0===t.life.count)return void t.destroy();const o=this.container.canvas.size,n=O(0,o.width),a=O(0,o.width);t.position.x=z(n),t.position.y=z(a),t.spawning=!0,i.delayTime=0,i.time=0,t.reset();const r=t.options.life;r&&(i.delay=1e3*C(r.delay.value),i.duration=1e3*C(r.duration.value))}}class Xs{draw(t,e,i){const s=e.shapeData;t.moveTo(-i/2,0),t.lineTo(i/2,0),t.lineCap=s?.cap??"butt"}getSidesCount(){return 1}}class Ys{init(){}isEnabled(t){return!j()&&!t.destroyed&&t.container.actualOptions.interactivity.events.onHover.parallax.enable}move(t){const e=t.container,i=e.actualOptions.interactivity.events.onHover.parallax;if(j()||!i.enable)return;const s=i.force,o=e.interactivity.mouse.position;if(!o)return;const n=e.canvas.size,a=n.width/2,r=n.height/2,c=i.smooth,l=t.getRadius()/s,h=(o.x-a)*l,d=(o.y-r)*l,{offset:u}=t;u.x+=(h-u.x)/c,u.y+=(d-u.y)/c}}class Js extends Fi{constructor(t){super(t)}clear(){}init(){}async interact(t){const e=this.container,i=t.retina.attractDistance??e.retina.attractDistance,s=t.getPosition(),o=e.particles.quadTree.queryCircle(s,i);for(const e of o){if(t===e||!e.options.move.attract.enable||e.destroyed||e.spawning)continue;const i=e.getPosition(),{dx:o,dy:n}=R(s,i),a=t.options.move.attract.rotate,r=o/(1e3*a.x),c=n/(1e3*a.y),l=e.size.value/t.size.value,h=1/l;t.velocity.x-=r*l,t.velocity.y-=c*l,e.velocity.x+=r*h,e.velocity.y+=c*h}}isEnabled(t){return t.options.move.attract.enable}reset(){}}function Zs(t,e,i,s,o,n){const a=k(t.options.collisions.absorb.speed*o.factor/10,0,s);t.size.value+=a/2,i.size.value-=a,s<=n&&(i.size.value=0,i.destroy())}const Qs=t=>{void 0===t.collisionMaxSpeed&&(t.collisionMaxSpeed=C(t.options.collisions.maxSpeed)),t.velocity.length>t.collisionMaxSpeed&&(t.velocity.length=t.collisionMaxSpeed)};function Ks(t,e){lt(ct(t),ct(e)),Qs(t),Qs(e)}function to(t,e,i,s){switch(t.options.collisions.mode){case"absorb":!function(t,e,i,s){const o=t.getRadius(),n=e.getRadius();void 0===o&&void 0!==n?t.destroy():void 0!==o&&void 0===n?e.destroy():void 0!==o&&void 0!==n&&(o>=n?Zs(t,0,e,n,i,s):Zs(e,0,t,o,i,s))}(t,e,i,s);break;case"bounce":Ks(t,e);break;case"destroy":!function(t,e){t.unbreakable||e.unbreakable||Ks(t,e),void 0===t.getRadius()&&void 0!==e.getRadius()?t.destroy():void 0!==t.getRadius()&&void 0===e.getRadius()?e.destroy():void 0!==t.getRadius()&&void 0!==e.getRadius()&&(t.getRadius()>=e.getRadius()?e:t).destroy()}(t,e)}}class eo extends Fi{constructor(t){super(t)}clear(){}init(){}async interact(t,e){if(t.destroyed||t.spawning)return;const i=this.container,s=t.getPosition(),o=t.getRadius(),n=i.particles.quadTree.queryCircle(s,2*o);for(const a of n){if(t===a||!a.options.collisions.enable||t.options.collisions.mode!==a.options.collisions.mode||a.destroyed||a.spawning)continue;const n=a.getPosition(),r=a.getRadius();if(Math.abs(Math.round(s.z)-Math.round(n.z))>o+r)continue;I(s,n)>o+r||to(t,a,e,i.retina.pixelRatio)}}isEnabled(t){return t.options.collisions.enable}reset(){}}class io extends _i{constructor(t,e,i,s){super(t,e,i),this.canvasSize=s,this.canvasSize={...s}}contains(t){const{width:e,height:i}=this.canvasSize,{x:s,y:o}=t;return super.contains(t)||super.contains({x:s-e,y:o})||super.contains({x:s-e,y:o-i})||super.contains({x:s,y:o-i})}intersects(t){if(super.intersects(t))return!0;const e=t,i=t,s={x:t.position.x-this.canvasSize.width,y:t.position.y-this.canvasSize.height};if(void 0!==i.radius){const t=new _i(s.x,s.y,2*i.radius);return super.intersects(t)}if(void 0!==e.size){const t=new xi(s.x,s.y,2*e.size.width,2*e.size.height);return super.intersects(t)}return!1}}class so{constructor(){this.blur=5,this.color=new le,this.color.value="#000",this.enable=!1}load(t){t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=le.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable))}}class oo{constructor(){this.enable=!1,this.frequency=1}load(t){t&&(void 0!==t.color&&(this.color=le.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity))}}class no{constructor(){this.blink=!1,this.color=new le,this.color.value="#fff",this.consent=!1,this.distance=100,this.enable=!1,this.frequency=1,this.opacity=1,this.shadow=new so,this.triangles=new oo,this.width=1,this.warp=!1}load(t){t&&(void 0!==t.id&&(this.id=t.id),void 0!==t.blink&&(this.blink=t.blink),this.color=le.create(this.color,t.color),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity),this.shadow.load(t.shadow),this.triangles.load(t.triangles),void 0!==t.width&&(this.width=t.width),void 0!==t.warp&&(this.warp=t.warp))}}function ao(t,e,i,s,o){const{dx:n,dy:a,distance:r}=R(t,e);if(!o||r<=i)return r;const c={x:Math.abs(n),y:Math.abs(a)},l=Math.min(c.x,s.width-c.x),h=Math.min(c.y,s.height-c.y);return Math.sqrt(l**2+h**2)}class ro extends Fi{constructor(t){super(t),this._setColor=t=>{if(!t.options.links)return;const e=this.linkContainer,i=t.options.links;let s=void 0===i.id?e.particles.linksColor:e.particles.linksColors.get(i.id);if(s)return;s=$t(i.color,i.blink,i.consent),void 0===i.id?e.particles.linksColor=s:e.particles.linksColors.set(i.id,s)},this.linkContainer=t}clear(){}init(){this.linkContainer.particles.linksColor=void 0,this.linkContainer.particles.linksColors=new Map}async interact(t){if(!t.options.links)return;t.links=[];const e=t.getPosition(),i=this.container,s=i.canvas.size;if(e.x<0||e.y<0||e.x>s.width||e.y>s.height)return;const o=t.options.links,n=o.opacity,a=t.retina.linksDistance??0,r=o.warp,c=r?new io(e.x,e.y,a,s):new _i(e.x,e.y,a),l=i.particles.quadTree.query(c);for(const i of l){const c=i.options.links;if(t===i||!c?.enable||o.id!==c.id||i.spawning||i.destroyed||!i.links||t.links.some((t=>t.destination===i))||i.links.some((e=>e.destination===t)))continue;const l=i.getPosition();if(l.x<0||l.y<0||l.x>s.width||l.y>s.height)continue;const h=ao(e,l,a,s,r&&c.warp);if(h>a)continue;const d=(1-h/a)*n;this._setColor(t),t.links.push({destination:i,opacity:d})}}isEnabled(t){return!!t.options.links?.enable}loadParticlesOptions(t,...e){t.links||(t.links=new no);for(const i of e)t.links.load(i?.links??i?.lineLinked??i?.line_linked)}reset(){}}function co(t,e){const i=((s=t.map((t=>t.id))).sort(((t,e)=>t-e)),s.join("_"));var s;let o=e.get(i);return void 0===o&&(o=_(),e.set(i,o)),o}class lo{constructor(t){this.container=t,this._drawLinkLine=(t,e)=>{const i=t.options.links;if(!i?.enable)return;const s=this.container,o=s.actualOptions,n=e.destination,a=t.getPosition(),r=n.getPosition();let c=e.opacity;s.canvas.draw((e=>{let l;const h=t.options.twinkle?.lines;if(h?.enable){const t=h.frequency,e=Tt(h.color);_()<t&&e&&(l=e,c=C(h.opacity))}if(!l){const e=void 0!==i.id?s.particles.linksColors.get(i.id):s.particles.linksColor;l=Ut(t,n,e)}if(!l)return;const d=t.retina.linksWidth??0,u=t.retina.linksDistance??0,{backgroundMask:p}=o;!function(t){let e=!1;const{begin:i,end:s,maxDistance:o,context:n,canvasSize:a,width:r,backgroundMask:c,colorLine:l,opacity:h,links:d}=t;if(I(i,s)<=o)Xt(n,i,s),e=!0;else if(d.warp){let t,r;const c=R(i,{x:s.x-a.width,y:s.y});if(c.distance<=o){const e=i.y-c.dy/c.dx*i.x;t={x:0,y:e},r={x:a.width,y:e}}else{const e=R(i,{x:s.x,y:s.y-a.height});if(e.distance<=o){const s=-(i.y-e.dy/e.dx*i.x)/(e.dy/e.dx);t={x:s,y:0},r={x:s,y:a.height}}else{const e=R(i,{x:s.x-a.width,y:s.y-a.height});if(e.distance<=o){const s=i.y-e.dy/e.dx*i.x;t={x:-s/(e.dy/e.dx),y:s},r={x:t.x+a.width,y:t.y+a.height}}}}t&&r&&(Xt(n,i,t),Xt(n,s,r),e=!0)}if(!e)return;n.lineWidth=r,c.enable&&(n.globalCompositeOperation=c.composite),n.strokeStyle=Vt(l,h);const{shadow:u}=d;if(u.enable){const t=Tt(u.color);t&&(n.shadowBlur=u.blur,n.shadowColor=Vt(t))}n.stroke()}({context:e,width:d,begin:a,end:r,maxDistance:u,canvasSize:s.canvas.size,links:i,backgroundMask:p,colorLine:l,opacity:c})}))},this._drawLinkTriangle=(t,e,i)=>{const s=t.options.links;if(!s?.enable)return;const o=s.triangles;if(!o.enable)return;const n=this.container,a=n.actualOptions,r=e.destination,c=i.destination,l=o.opacity??(e.opacity+i.opacity)/2;l<=0||n.canvas.draw((e=>{const i=t.getPosition(),h=r.getPosition(),d=c.getPosition(),u=t.retina.linksDistance??0;if(I(i,h)>u||I(d,h)>u||I(d,i)>u)return;let p=Tt(o.color);if(!p){const e=void 0!==s.id?n.particles.linksColors.get(s.id):n.particles.linksColor;p=Ut(t,r,e)}p&&function(t){const{context:e,pos1:i,pos2:s,pos3:o,backgroundMask:n,colorTriangle:a,opacityTriangle:r}=t;Yt(e,i,s,o),n.enable&&(e.globalCompositeOperation=n.composite),e.fillStyle=Vt(a,r),e.fill()}({context:e,pos1:i,pos2:h,pos3:d,backgroundMask:a.backgroundMask,colorTriangle:p,opacityTriangle:l})}))},this._drawTriangles=(t,e,i,s)=>{const o=i.destination;if(!t.links?.triangles.enable||!o.options.links?.triangles.enable)return;const n=o.links?.filter((t=>{const e=this._getLinkFrequency(o,t.destination);return o.options.links&&e<=o.options.links.frequency&&s.findIndex((e=>e.destination===t.destination))>=0}));if(n?.length)for(const s of n){const n=s.destination;this._getTriangleFrequency(e,o,n)>t.links.triangles.frequency||this._drawLinkTriangle(e,i,s)}},this._getLinkFrequency=(t,e)=>co([t,e],this._freqs.links),this._getTriangleFrequency=(t,e,i)=>co([t,e,i],this._freqs.triangles),this._freqs={links:new Map,triangles:new Map}}drawParticle(t,e){const{links:i,options:s}=e;if(!i||i.length<=0)return;const o=i.filter((t=>s.links&&this._getLinkFrequency(e,t.destination)<=s.links.frequency));for(const t of o)this._drawTriangles(s,e,t,o),t.opacity>0&&(e.retina.linksWidth??0)>0&&this._drawLinkLine(e,t)}async init(){this._freqs.links=new Map,this._freqs.triangles=new Map}particleCreated(t){if(t.links=[],!t.options.links)return;const e=this.container.retina.pixelRatio,{retina:i}=t,{distance:s,width:o}=t.options.links;i.linksDistance=s*e,i.linksWidth=o*e}particleDestroyed(t){t.links=[]}}class ho{constructor(){this.id="links"}getPlugin(t){return new lo(t)}loadOptions(){}needsPlugin(){return!0}}async function uo(t,e=!0){await async function(t,e=!0){await t.addInteractor("particlesLinks",(t=>new ro(t)),e)}(t,e),await async function(t,e=!0){const i=new ho;await t.addPlugin(i,e)}(t,e)}class po{draw(t,e,i){const s=this.getCenter(e,i),o=this.getSidesData(e,i),n=o.count.numerator*o.count.denominator,a=o.count.numerator/o.count.denominator,r=180*(a-2)/a,c=Math.PI-Math.PI*r/180;if(t){t.beginPath(),t.translate(s.x,s.y),t.moveTo(0,0);for(let e=0;e<n;e++)t.lineTo(o.length,0),t.translate(o.length,0),t.rotate(c)}}getSidesCount(t){const e=t.shapeData;return Math.round(C(e?.sides??e?.nb_sides??5))}}class fo extends po{getCenter(t,e){return{x:-e/(t.sides/3.5),y:-e/.76}}getSidesData(t,e){const i=t.sides;return{count:{denominator:1,numerator:i},length:2.66*e/(i/3)}}}class vo extends po{getCenter(t,e){return{x:-e,y:e/1.66}}getSidesCount(){return 3}getSidesData(t,e){return{count:{denominator:2,numerator:3},length:2*e}}}async function mo(t,e=!0){await async function(t,e=!0){await t.addShape("polygon",new fo,e)}(t,e),await async function(t,e=!0){await t.addShape("triangle",new vo,e)}(t,e)}class go{constructor(){this.enable=!1,this.speed=0,this.decay=0,this.sync=!1}load(t){t&&(void 0!==t.enable&&(this.enable=t.enable),void 0!==t.speed&&(this.speed=O(t.speed)),void 0!==t.decay&&(this.decay=O(t.decay)),void 0!==t.sync&&(this.sync=t.sync))}}class yo extends Ee{constructor(){super(),this.animation=new go,this.direction="clockwise",this.path=!1,this.value=0}load(t){t&&(super.load(t),void 0!==t.direction&&(this.direction=t.direction),this.animation.load(t.animation),void 0!==t.path&&(this.path=t.path))}}class bo{constructor(t){this.container=t}init(t){const e=t.options.rotate;if(!e)return;t.rotate={enable:e.animation.enable,value:C(e.value)*Math.PI/180},t.pathRotation=e.path;let i=e.direction;if("random"===i){i=Math.floor(2*_())>0?"counter-clockwise":"clockwise"}switch(i){case"counter-clockwise":case"counterClockwise":t.rotate.status="decreasing";break;case"clockwise":t.rotate.status="increasing"}const s=e.animation;s.enable&&(t.rotate.decay=1-C(s.decay),t.rotate.velocity=C(s.speed)/360*this.container.retina.reduceFactor,s.sync||(t.rotate.velocity*=_())),t.rotation=t.rotate.value}isEnabled(t){const e=t.options.rotate;return!!e&&(!t.destroyed&&!t.spawning&&e.animation.enable&&!e.path)}loadOptions(t,...e){t.rotate||(t.rotate=new yo);for(const i of e)t.rotate.load(i?.rotate)}update(t,e){this.isEnabled(t)&&(!function(t,e){const i=t.rotate,s=t.options.rotate;if(!i||!s)return;const o=s.animation,n=(i.velocity??0)*e.factor,a=2*Math.PI,r=i.decay??1;o.enable&&("increasing"===i.status?(i.value+=n,i.value>a&&(i.value-=a)):(i.value-=n,i.value<0&&(i.value+=a)),i.velocity&&1!==r&&(i.velocity*=r))}(t,e),t.rotation=t.rotate?.value??0)}}const wo=Math.sqrt(2);class xo{draw(t,e,i){const s=i/wo,o=2*s;t.rect(-s,-s,o,o)}getSidesCount(){return 4}}class _o{draw(t,e,i){const s=e.sides,o=e.starInset??2;t.moveTo(0,0-i);for(let e=0;e<s;e++)t.rotate(Math.PI/s),t.lineTo(0,0-i*o),t.rotate(Math.PI/s),t.lineTo(0,0-i)}getSidesCount(t){const e=t.shapeData;return Math.round(C(e?.sides??e?.nb_sides??5))}particleInit(t,e){const i=e.shapeData,s=C(i?.inset??2);e.starInset=s}}function ko(t,e,i,s,o){if(!e||!i.enable||(e.maxLoops??0)>0&&(e.loops??0)>(e.maxLoops??0))return;if(e.time||(e.time=0),(e.delayTime??0)>0&&e.time<(e.delayTime??0)&&(e.time+=t.value),(e.delayTime??0)>0&&e.time<(e.delayTime??0))return;const n=z(i.offset),a=(e.velocity??0)*t.factor+3.6*n,r=e.decay??1;o&&"increasing"!==e.status?(e.value-=a,e.value<0&&(e.loops||(e.loops=0),e.loops++,e.status="increasing",e.value+=e.value)):(e.value+=a,e.value>s&&(e.loops||(e.loops=0),e.loops++,o&&(e.status="decreasing",e.value-=e.value%s))),e.velocity&&1!==r&&(e.velocity*=r),e.value>s&&(e.value%=s)}class Mo{constructor(t){this.container=t}init(t){const e=this.container,i=t.options,s=ut(i.stroke,t.id,i.reduceDuplicates);t.strokeWidth=C(s.width)*e.retina.pixelRatio,t.strokeOpacity=C(s.opacity??1),t.strokeAnimation=s.color?.animation;const o=Dt(s.color)??t.getFillColor();o&&(t.strokeColor=jt(o,t.strokeAnimation,e.retina.reduceFactor))}isEnabled(t){const e=t.strokeAnimation,{strokeColor:i}=t;return!t.destroyed&&!t.spawning&&!!e&&(void 0!==i?.h.value&&i.h.enable||void 0!==i?.s.value&&i.s.enable||void 0!==i?.l.value&&i.l.enable)}update(t,e){this.isEnabled(t)&&function(t,e){if(!t.strokeColor||!t.strokeAnimation)return;const{h:i,s,l:o}=t.strokeColor,{h:n,s:a,l:r}=t.strokeAnimation;i&&ko(e,i,n,360,!1),s&&ko(e,s,a,100,!0),o&&ko(e,o,r,100,!0)}(t,e)}}const zo=["text","character","char"];class Co{draw(t,e,i,s){const o=e.shapeData;if(void 0===o)return;const n=o.value;if(void 0===n)return;void 0===e.text&&(e.text=ut(n,e.randomIndexData));const a=e.text,r=o.style??"",c=o.weight??"400",l=2*Math.round(i),h=o.font??"Verdana",d=e.fill,u=a.length*i/2;t.font=`${r} ${c} ${l}px "${h}"`;const p={x:-u,y:i/2};t.globalAlpha=s,d?t.fillText(a,p.x,p.y):t.strokeText(a,p.x,p.y),t.globalAlpha=1}getSidesCount(){return 12}async init(t){const e=t.actualOptions;if(zo.find((t=>J(t,e.particles.shape.type)))){const t=zo.map((t=>e.particles.shape.options[t])).find((t=>!!t)),i=[];dt(t,(t=>{i.push(Z(t.font,t.weight))})),await Promise.all(i)}}particleInit(t,e){if(!e.shape||!zo.includes(e.shape))return;const i=e.shapeData;if(void 0===i)return;const s=i.value;void 0!==s&&(e.text=ut(s,e.randomIndexData))}}async function Po(t,e=!0){Hi(t),await async function(t,e=!0){await t.addMover("parallax",(()=>new Ys),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalAttract",(e=>new is(t,e)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalBounce",(t=>new os(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalBubble",(t=>new ls(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalConnect",(t=>new fs(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalGrab",(t=>new ys(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalPause",(t=>new bs(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalPush",(t=>new xs(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalRemove",(t=>new ks(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalRepulse",(e=>new Ps(t,e)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("externalSlow",(t=>new Os(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("particlesAttract",(t=>new Js(t)),e)}(t,!1),await async function(t,e=!0){await t.addInteractor("particlesCollisions",(t=>new eo(t)),e)}(t,!1),await uo(t,!1),await async function(){b("ease-in-quad",(t=>t**2)),b("ease-out-quad",(t=>1-(1-t)**2)),b("ease-in-out-quad",(t=>t<.5?2*t**2:1-(-2*t+2)**2/2))}(),await Us(t,!1),await async function(t,e=!0){await t.addShape("line",new Xs,e)}(t,!1),await mo(t,!1),await async function(t,e=!0){await t.addShape(["edge","square"],new xo,e)}(t,!1),await async function(t,e=!0){await t.addShape("star",new _o,e)}(t,!1),await async function(t,e=!0){await t.addShape(zo,new Co,e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("life",(t=>new Ns(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("rotate",(t=>new bo(t)),e)}(t,!1),await async function(t,e=!0){await t.addParticleUpdater("strokeColor",(t=>new Mo(t)),e)}(t,!1),await ts(t,e)}return Po(qi),e})()));