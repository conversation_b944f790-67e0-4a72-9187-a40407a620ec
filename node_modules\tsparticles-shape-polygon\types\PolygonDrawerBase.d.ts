import { type ICoordinates, type IShapeDrawer, type Particle } from "tsparticles-engine";
import type { ISide } from "./ISide";
export declare abstract class PolygonDrawerBase implements IShapeDrawer {
    draw(context: CanvasRenderingContext2D, particle: Particle, radius: number): void;
    getSidesCount(particle: Particle): number;
    abstract getCenter(particle: Particle, radius: number): ICoordinates;
    abstract getSidesData(particle: Particle, radius: number): ISide;
}
