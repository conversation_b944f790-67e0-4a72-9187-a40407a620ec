/*! For license information please see tsparticles.updater.size.min.js.LICENSE.txt */
!function(e,o){if("object"==typeof exports&&"object"==typeof module)module.exports=o(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],o);else{var t="object"==typeof exports?o(require("tsparticles-engine")):o(e.window);for(var i in t)("object"==typeof exports?exports:e)[i]=t[i]}}(this,(e=>(()=>{"use strict";var o={961:o=>{o.exports=e}},t={};function i(e){var s=t[e];if(void 0!==s)return s.exports;var a=t[e]={exports:{}};return o[e](a,a.exports,i),a.exports}i.d=(e,o)=>{for(var t in o)i.o(o,t)&&!i.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},i.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var s={};return(()=>{i.r(s),i.d(s,{loadSizeUpdater:()=>t});var e=i(961);class o{init(o){const t=o.container,i=o.options.size.animation;i.enable&&(o.size.velocity=(o.retina.sizeAnimationSpeed??t.retina.sizeAnimationSpeed)/100*t.retina.reduceFactor,i.sync||(o.size.velocity*=(0,e.getRandom)()))}isEnabled(e){return!e.destroyed&&!e.spawning&&e.size.enable&&((e.size.maxLoops??0)<=0||(e.size.maxLoops??0)>0&&(e.size.loops??0)<(e.size.maxLoops??0))}reset(e){e.size.loops=0}update(o,t){this.isEnabled(o)&&function(o,t){const i=o.size;if(o.destroyed||!i||!i.enable||(i.maxLoops??0)>0&&(i.loops??0)>(i.maxLoops??0))return;const s=(i.velocity??0)*t.factor,a=i.min,n=i.max,r=i.decay??1;if(i.time||(i.time=0),(i.delayTime??0)>0&&i.time<(i.delayTime??0)&&(i.time+=t.value),!((i.delayTime??0)>0&&i.time<(i.delayTime??0))){switch(i.status){case"increasing":i.value>=n?(i.status="decreasing",i.loops||(i.loops=0),i.loops++):i.value+=s;break;case"decreasing":i.value<=a?(i.status="increasing",i.loops||(i.loops=0),i.loops++):i.value-=s}i.velocity&&1!==r&&(i.velocity*=r),function(e,o,t,i){switch(e.options.size.animation.destroy){case"max":o>=i&&e.destroy();break;case"min":o<=t&&e.destroy()}}(o,i.value,a,n),o.destroyed||(i.value=(0,e.clamp)(i.value,a,n))}}(o,t)}}async function t(e,t=!0){await e.addParticleUpdater("size",(()=>new o),t)}})(),s})()));