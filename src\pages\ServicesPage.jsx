import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Clock,
  DollarSign,
  Users,
  Star,
  MoreVertical,
  Eye,
  Copy,
  Archive
} from 'lucide-react'
import Card from '../components/ui/Card'
import Button from '../components/ui/Button'
import Modal from '../components/ui/Modal'

const ServicesPage = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showAddService, setShowAddService] = useState(false)
  const [selectedService, setSelectedService] = useState(null)
  const [showServiceDetails, setShowServiceDetails] = useState(false)

  const services = [
    {
      id: 1,
      name: 'Initial Consultation',
      category: 'Consultation',
      description: 'Comprehensive assessment and treatment planning session for new patients',
      duration: 60,
      price: 200,
      isActive: true,
      popularity: 95,
      totalBookings: 247,
      averageRating: 4.9,
      staff: ['Dr. <PERSON>', 'Dr. <PERSON>'],
      image: '🩺',
      features: ['Comprehensive Assessment', 'Treatment Planning', 'Medical History Review'],
      createdAt: '2024-01-01'
    },
    {
      id: 2,
      name: 'Follow-up Session',
      category: 'Follow-up',
      description: 'Progress review and treatment adjustment for existing patients',
      duration: 45,
      price: 150,
      isActive: true,
      popularity: 87,
      totalBookings: 189,
      averageRating: 4.8,
      staff: ['Dr. Sarah Johnson', 'Dr. Emily Davis'],
      image: '📋',
      features: ['Progress Review', 'Treatment Adjustment', 'Medication Review'],
      createdAt: '2024-01-01'
    },
    {
      id: 3,
      name: 'Individual Therapy',
      category: 'Therapy',
      description: 'One-on-one therapy session for various mental health conditions',
      duration: 50,
      price: 180,
      isActive: true,
      popularity: 92,
      totalBookings: 312,
      averageRating: 4.9,
      staff: ['Dr. Emily Davis', 'Dr. Michael Chen'],
      image: '🧠',
      features: ['Personalized Treatment', 'CBT Techniques', 'Progress Tracking'],
      createdAt: '2024-01-01'
    },
    {
      id: 4,
      name: 'Group Therapy',
      category: 'Group',
      description: 'Group therapy session for peer support and shared experiences',
      duration: 90,
      price: 120,
      isActive: true,
      popularity: 73,
      totalBookings: 98,
      averageRating: 4.6,
      staff: ['Dr. Sarah Johnson'],
      image: '👥',
      features: ['Peer Support', 'Group Dynamics', 'Shared Learning'],
      createdAt: '2024-01-01'
    },
    {
      id: 5,
      name: 'Emergency Session',
      category: 'Emergency',
      description: 'Urgent mental health support for crisis situations',
      duration: 30,
      price: 250,
      isActive: true,
      popularity: 45,
      totalBookings: 23,
      averageRating: 4.7,
      staff: ['Dr. Sarah Johnson', 'Dr. Michael Chen', 'Dr. Emily Davis'],
      image: '🚨',
      features: ['24/7 Availability', 'Crisis Intervention', 'Immediate Support'],
      createdAt: '2024-01-01'
    },
  ]

  const categories = ['all', 'Consultation', 'Follow-up', 'Therapy', 'Group', 'Emergency']

  const filteredServices = services.filter(service => {
    const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         service.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || service.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const [newService, setNewService] = useState({
    name: '',
    category: '',
    description: '',
    duration: 60,
    price: 0,
    features: [],
    staff: []
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Services Management</h1>
          <p className="mt-1 text-gray-600">Create and manage your service offerings</p>
        </div>
        <div className="mt-4 lg:mt-0">
          <Button
            icon={<Plus className="w-4 h-4" />}
            onClick={() => setShowAddService(true)}
          >
            Add New Service
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Services</p>
              <p className="text-3xl font-bold text-gray-900">{services.length}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-xl">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Services</p>
              <p className="text-3xl font-bold text-gray-900">{services.filter(s => s.isActive).length}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-xl">
              <Star className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Price</p>
              <p className="text-3xl font-bold text-gray-900">
                ${Math.round(services.reduce((acc, s) => acc + s.price, 0) / services.length)}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-xl">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Bookings</p>
              <p className="text-3xl font-bold text-gray-900">
                {services.reduce((acc, s) => acc + s.totalBookings, 0)}
              </p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-xl">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search services..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-full sm:w-64"
              />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
          <div className="text-sm text-gray-600">
            Showing {filteredServices.length} of {services.length} services
          </div>
        </div>
      </Card>

      {/* Services Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredServices.map((service, index) => (
          <motion.div
            key={service.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card hover className="h-full">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">{service.image}</div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{service.name}</h3>
                    <p className="text-sm text-gray-500">{service.category}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" size="sm" icon={<Eye className="w-4 h-4" />}
                    onClick={() => {
                      setSelectedService(service)
                      setShowServiceDetails(true)
                    }}
                  />
                  <Button variant="ghost" size="sm" icon={<Edit className="w-4 h-4" />} />
                  <Button variant="ghost" size="sm" icon={<MoreVertical className="w-4 h-4" />} />
                </div>
              </div>

              <p className="text-gray-600 text-sm mb-4 line-clamp-2">{service.description}</p>

              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <Clock className="w-4 h-4 mr-1" />
                    {service.duration} minutes
                  </div>
                  <div className="flex items-center text-gray-600">
                    <DollarSign className="w-4 h-4 mr-1" />
                    ${service.price}
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center text-gray-600">
                    <Star className="w-4 h-4 mr-1 text-yellow-400 fill-current" />
                    {service.averageRating} ({service.totalBookings} bookings)
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    service.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {service.isActive ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                  <span>Popularity</span>
                  <span>{service.popularity}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${service.popularity}%` }}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Features:</h4>
                <div className="flex flex-wrap gap-1">
                  {service.features.slice(0, 3).map((feature, idx) => (
                    <span key={idx} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">
                      {feature}
                    </span>
                  ))}
                  {service.features.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      +{service.features.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredServices.length === 0 && (
        <Card className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No services found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || selectedCategory !== 'all'
              ? 'Try adjusting your search or filter criteria'
              : 'Get started by creating your first service'
            }
          </p>
          <Button icon={<Plus className="w-4 h-4" />} onClick={() => setShowAddService(true)}>
            Add New Service
          </Button>
        </Card>
      )}

      {/* Add Service Modal */}
      <Modal
        isOpen={showAddService}
        onClose={() => setShowAddService(false)}
        title="Add New Service"
        size="lg"
      >
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Name
              </label>
              <input
                type="text"
                value={newService.name}
                onChange={(e) => setNewService({...newService, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter service name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={newService.category}
                onChange={(e) => setNewService({...newService, category: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select category</option>
                {categories.filter(c => c !== 'all').map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={newService.description}
              onChange={(e) => setNewService({...newService, description: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe your service"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Duration (minutes)
              </label>
              <input
                type="number"
                value={newService.duration}
                onChange={(e) => setNewService({...newService, duration: parseInt(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="15"
                step="15"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price ($)
              </label>
              <input
                type="number"
                value={newService.price}
                onChange={(e) => setNewService({...newService, price: parseFloat(e.target.value)})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                min="0"
                step="0.01"
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowAddService(false)}>
              Cancel
            </Button>
            <Button>
              Create Service
            </Button>
          </div>
        </div>
      </Modal>

      {/* Service Details Modal */}
      <Modal
        isOpen={showServiceDetails}
        onClose={() => setShowServiceDetails(false)}
        title="Service Details"
        size="lg"
      >
        {selectedService && (
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <div className="text-4xl">{selectedService.image}</div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{selectedService.name}</h2>
                <p className="text-gray-600">{selectedService.category}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Service Information</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span>{selectedService.duration} minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Price:</span>
                      <span>${selectedService.price}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className={selectedService.isActive ? 'text-green-600' : 'text-red-600'}>
                        {selectedService.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Performance</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Bookings:</span>
                      <span>{selectedService.totalBookings}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average Rating:</span>
                      <span className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current mr-1" />
                        {selectedService.averageRating}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Popularity:</span>
                      <span>{selectedService.popularity}%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Description</h3>
                  <p className="text-sm text-gray-600">{selectedService.description}</p>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Features</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedService.features.map((feature, idx) => (
                      <span key={idx} className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-2">Available Staff</h3>
                  <div className="space-y-1">
                    {selectedService.staff.map((staffMember, idx) => (
                      <div key={idx} className="text-sm text-gray-600">• {staffMember}</div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button variant="outline" icon={<Copy className="w-4 h-4" />}>
                Duplicate
              </Button>
              <Button variant="outline" icon={<Archive className="w-4 h-4" />}>
                Archive
              </Button>
              <Button icon={<Edit className="w-4 h-4" />}>
                Edit Service
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default ServicesPage
