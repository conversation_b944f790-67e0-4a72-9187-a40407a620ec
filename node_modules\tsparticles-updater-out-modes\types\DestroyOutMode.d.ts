import { type Container, type IDelta, OutMode, type OutModeAlt, type OutModeDirection, type Particle } from "tsparticles-engine";
import type { IOutModeManager } from "./IOutModeManager";
export declare class DestroyOutMode implements IOutModeManager {
    private readonly container;
    modes: (OutMode | OutModeAlt | keyof typeof OutMode)[];
    constructor(container: Container);
    update(particle: Particle, direction: OutModeDirection, _delta: <PERSON><PERSON><PERSON>, outMode: OutMode | OutModeAlt | keyof typeof OutMode): void;
}
