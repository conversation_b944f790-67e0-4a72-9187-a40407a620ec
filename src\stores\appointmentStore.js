import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useAppointmentStore = create(
  persist(
    (set, get) => ({
      appointments: [
        {
          id: 1,
          client: {
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+****************',
            avatar: 'SJ'
          },
          service: 'Initial Consultation',
          staff: 'Dr. <PERSON>',
          date: '2024-01-15',
          time: '10:00 AM',
          duration: 60,
          status: 'confirmed',
          notes: 'First-time patient, anxiety concerns',
          price: 200,
          location: 'Room 101',
          createdAt: '2024-01-10T10:00:00Z',
          updatedAt: '2024-01-10T10:00:00Z'
        },
        {
          id: 2,
          client: {
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+****************',
            avatar: 'MB'
          },
          service: 'Follow-up Session',
          staff: 'Dr. <PERSON>',
          date: '2024-01-15',
          time: '2:30 PM',
          duration: 45,
          status: 'pending',
          notes: 'Progress review for depression treatment',
          price: 150,
          location: 'Room 203',
          createdAt: '2024-01-11T14:30:00Z',
          updatedAt: '2024-01-11T14:30:00Z'
        },
        {
          id: 3,
          client: {
            name: '<PERSON>',
            email: '<EMAIL>',
            phone: '+****************',
            avatar: 'ED'
          },
          service: 'Therapy Session',
          staff: 'Dr. Emily Davis',
          date: '2024-01-16',
          time: '11:00 AM',
          duration: 50,
          status: 'completed',
          notes: 'PTSD therapy session, good progress',
          price: 180,
          location: 'Room 105',
          createdAt: '2024-01-12T11:00:00Z',
          updatedAt: '2024-01-16T12:00:00Z'
        }
      ],
      
      // Actions
      updateAppointmentStatus: (appointmentId, status, notes = '') => {
        set(state => ({
          appointments: state.appointments.map(appointment =>
            appointment.id === appointmentId
              ? {
                  ...appointment,
                  status,
                  notes: notes || appointment.notes,
                  updatedAt: new Date().toISOString()
                }
              : appointment
          )
        }))
      },

      cancelAppointment: (appointmentId, reason = '') => {
        set(state => ({
          appointments: state.appointments.map(appointment =>
            appointment.id === appointmentId
              ? {
                  ...appointment,
                  status: 'cancelled',
                  notes: reason ? `Cancelled: ${reason}` : 'Cancelled by user',
                  updatedAt: new Date().toISOString()
                }
              : appointment
          )
        }))
      },

      markAppointmentComplete: (appointmentId, notes = '') => {
        set(state => ({
          appointments: state.appointments.map(appointment =>
            appointment.id === appointmentId
              ? {
                  ...appointment,
                  status: 'completed',
                  notes: notes || appointment.notes,
                  updatedAt: new Date().toISOString()
                }
              : appointment
          )
        }))
      },

      rescheduleAppointment: (appointmentId, newDate, newTime) => {
        set(state => ({
          appointments: state.appointments.map(appointment =>
            appointment.id === appointmentId
              ? {
                  ...appointment,
                  date: newDate,
                  time: newTime,
                  status: 'confirmed',
                  notes: `${appointment.notes}\nRescheduled from ${appointment.date} ${appointment.time}`,
                  updatedAt: new Date().toISOString()
                }
              : appointment
          )
        }))
      },

      addAppointment: (appointmentData) => {
        const newAppointment = {
          ...appointmentData,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
        
        set(state => ({
          appointments: [...state.appointments, newAppointment]
        }))
      },

      deleteAppointment: (appointmentId) => {
        set(state => ({
          appointments: state.appointments.filter(appointment => appointment.id !== appointmentId)
        }))
      },

      getAppointmentById: (appointmentId) => {
        const { appointments } = get()
        return appointments.find(appointment => appointment.id === appointmentId)
      },

      getAppointmentsByStatus: (status) => {
        const { appointments } = get()
        return appointments.filter(appointment => appointment.status === status)
      },

      getAppointmentsByDate: (date) => {
        const { appointments } = get()
        return appointments.filter(appointment => appointment.date === date)
      },

      getAppointmentStats: () => {
        const { appointments } = get()
        return {
          total: appointments.length,
          confirmed: appointments.filter(a => a.status === 'confirmed').length,
          pending: appointments.filter(a => a.status === 'pending').length,
          completed: appointments.filter(a => a.status === 'completed').length,
          cancelled: appointments.filter(a => a.status === 'cancelled').length,
          totalRevenue: appointments
            .filter(a => a.status === 'completed')
            .reduce((sum, a) => sum + a.price, 0)
        }
      }
    }),
    {
      name: 'appointment-storage',
      partialize: (state) => ({ appointments: state.appointments })
    }
  )
)
