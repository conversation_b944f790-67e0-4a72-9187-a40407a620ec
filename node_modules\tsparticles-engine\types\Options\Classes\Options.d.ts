import { Background } from "./Background/Background";
import { BackgroundMask } from "./BackgroundMask/BackgroundMask";
import type { Container } from "../../Core/Container";
import type { Engine } from "../../Core/Engine";
import { FullScreen } from "./FullScreen/FullScreen";
import type { IOptionLoader } from "../Interfaces/IOptionLoader";
import type { IOptions } from "../Interfaces/IOptions";
import { Interactivity } from "./Interactivity/Interactivity";
import { ManualParticle } from "./ManualParticle";
import type { RangeValue } from "../../Types/RangeValue";
import type { RecursivePartial } from "../../Types/RecursivePartial";
import { Responsive } from "./Responsive";
import type { SingleOrMultiple } from "../../Types/SingleOrMultiple";
import { Theme } from "./Theme/Theme";
interface DefaultThemes {
    dark?: string;
    light?: string;
}
export declare class Options implements IOptions, IOptionLoader<IOptions> {
    [name: string]: unknown;
    autoPlay: boolean;
    background: Background;
    backgroundMask: BackgroundMask;
    defaultThemes: DefaultThemes;
    delay: RangeValue;
    detectRetina: boolean;
    duration: RangeValue;
    fpsLimit: number;
    fullScreen: FullScreen;
    interactivity: Interactivity;
    manualParticles: ManualParticle[];
    name?: string;
    particles: import("./Particles/ParticlesOptions").ParticlesOptions;
    pauseOnBlur: boolean;
    pauseOnOutsideViewport: boolean;
    preset?: SingleOrMultiple<string>;
    responsive: Responsive[];
    smooth: boolean;
    style: RecursivePartial<CSSStyleDeclaration>;
    themes: Theme[];
    zLayers: number;
    private readonly _container;
    private readonly _engine;
    constructor(engine: Engine, container: Container);
    get backgroundMode(): FullScreen;
    set backgroundMode(value: FullScreen);
    get fps_limit(): number;
    set fps_limit(value: number);
    get retina_detect(): boolean;
    set retina_detect(value: boolean);
    load(data?: RecursivePartial<IOptions>): void;
    setResponsive(width: number, pxRatio: number, defaultOptions: IOptions): number | undefined;
    setTheme(name?: string): void;
    private readonly _findDefaultTheme;
    private readonly _importPreset;
}
export {};
