import { type Container, type IShapeDrawer, type Particle } from "tsparticles-engine";
import type { StarParticle } from "./StarParticle";
export declare class StarDrawer implements IShapeDrawer {
    draw(context: CanvasRenderingContext2D, particle: StarParticle, radius: number): void;
    getSidesCount(particle: Particle): number;
    particleInit(container: Container, particle: StarParticle): void;
}
