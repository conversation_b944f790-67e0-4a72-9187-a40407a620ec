import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
})

// Database types for better TypeScript support
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          firstName: string
          lastName: string
          phone: string | null
          role: 'admin' | 'staff' | 'client'
          avatar: string | null
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id: string
          email: string
          firstName: string
          lastName: string
          phone?: string | null
          role?: 'admin' | 'staff' | 'client'
          avatar?: string | null
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          email?: string
          firstName?: string
          lastName?: string
          phone?: string | null
          role?: 'admin' | 'staff' | 'client'
          avatar?: string | null
          createdAt?: string
          updatedAt?: string
        }
      }
      services: {
        Row: {
          id: string
          name: string
          description: string
          duration: number
          price: number
          category: string
          isActive: boolean
          staffIds: string[]
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          name: string
          description: string
          duration: number
          price: number
          category: string
          isActive?: boolean
          staffIds?: string[]
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string
          duration?: number
          price?: number
          category?: string
          isActive?: boolean
          staffIds?: string[]
          createdAt?: string
          updatedAt?: string
        }
      }
      appointments: {
        Row: {
          id: string
          clientId: string
          staffId: string
          serviceId: string
          startTime: string
          endTime: string
          status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show'
          notes: string | null
          paymentStatus: 'pending' | 'paid' | 'refunded'
          paymentId: string | null
          reminderSent: boolean
          createdAt: string
          updatedAt: string
        }
        Insert: {
          id?: string
          clientId: string
          staffId: string
          serviceId: string
          startTime: string
          endTime: string
          status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show'
          notes?: string | null
          paymentStatus?: 'pending' | 'paid' | 'refunded'
          paymentId?: string | null
          reminderSent?: boolean
          createdAt?: string
          updatedAt?: string
        }
        Update: {
          id?: string
          clientId?: string
          staffId?: string
          serviceId?: string
          startTime?: string
          endTime?: string
          status?: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show'
          notes?: string | null
          paymentStatus?: 'pending' | 'paid' | 'refunded'
          paymentId?: string | null
          reminderSent?: boolean
          createdAt?: string
          updatedAt?: string
        }
      }
    }
  }
}
