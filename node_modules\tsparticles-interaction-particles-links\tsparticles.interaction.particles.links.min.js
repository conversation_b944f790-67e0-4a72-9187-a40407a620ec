/*! For license information please see tsparticles.interaction.particles.links.min.js.LICENSE.txt */
!function(i,t){if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("tsparticles-engine"));else if("function"==typeof define&&define.amd)define(["tsparticles-engine"],t);else{var n="object"==typeof exports?t(require("tsparticles-engine")):t(i.window);for(var e in n)("object"==typeof exports?exports:i)[e]=n[e]}}(this,(i=>(()=>{"use strict";var t={961:t=>{t.exports=i}},n={};function e(i){var s=n[i];if(void 0!==s)return s.exports;var o=n[i]={exports:{}};return t[i](o,o.exports,e),o.exports}e.d=(i,t)=>{for(var n in t)e.o(t,n)&&!e.o(i,n)&&Object.defineProperty(i,n,{enumerable:!0,get:t[n]})},e.o=(i,t)=>Object.prototype.hasOwnProperty.call(i,t),e.r=i=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(i,"__esModule",{value:!0})};var s={};return(()=>{e.r(s),e.d(s,{Links:()=>r,LinksShadow:()=>n,LinksTriangle:()=>o,loadParticlesLinksInteraction:()=>p});var i=e(961);class t extends i.Circle{constructor(i,t,n,e){super(i,t,n),this.canvasSize=e,this.canvasSize={...e}}contains(i){const{width:t,height:n}=this.canvasSize,{x:e,y:s}=i;return super.contains(i)||super.contains({x:e-t,y:s})||super.contains({x:e-t,y:s-n})||super.contains({x:e,y:s-n})}intersects(t){if(super.intersects(t))return!0;const n=t,e=t,s={x:t.position.x-this.canvasSize.width,y:t.position.y-this.canvasSize.height};if(void 0!==e.radius){const t=new i.Circle(s.x,s.y,2*e.radius);return super.intersects(t)}if(void 0!==n.size){const t=new i.Rectangle(s.x,s.y,2*n.size.width,2*n.size.height);return super.intersects(t)}return!1}}class n{constructor(){this.blur=5,this.color=new i.OptionsColor,this.color.value="#000",this.enable=!1}load(t){t&&(void 0!==t.blur&&(this.blur=t.blur),this.color=i.OptionsColor.create(this.color,t.color),void 0!==t.enable&&(this.enable=t.enable))}}class o{constructor(){this.enable=!1,this.frequency=1}load(t){t&&(void 0!==t.color&&(this.color=i.OptionsColor.create(this.color,t.color)),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity))}}class r{constructor(){this.blink=!1,this.color=new i.OptionsColor,this.color.value="#fff",this.consent=!1,this.distance=100,this.enable=!1,this.frequency=1,this.opacity=1,this.shadow=new n,this.triangles=new o,this.width=1,this.warp=!1}load(t){t&&(void 0!==t.id&&(this.id=t.id),void 0!==t.blink&&(this.blink=t.blink),this.color=i.OptionsColor.create(this.color,t.color),void 0!==t.consent&&(this.consent=t.consent),void 0!==t.distance&&(this.distance=t.distance),void 0!==t.enable&&(this.enable=t.enable),void 0!==t.frequency&&(this.frequency=t.frequency),void 0!==t.opacity&&(this.opacity=t.opacity),this.shadow.load(t.shadow),this.triangles.load(t.triangles),void 0!==t.width&&(this.width=t.width),void 0!==t.warp&&(this.warp=t.warp))}}function a(t,n,e,s,o){const{dx:r,dy:a,distance:l}=(0,i.getDistances)(t,n);if(!o||l<=e)return l;const c={x:Math.abs(r),y:Math.abs(a)},d=Math.min(c.x,s.width-c.x),h=Math.min(c.y,s.height-c.y);return Math.sqrt(d**2+h**2)}class l extends i.ParticlesInteractorBase{constructor(t){super(t),this._setColor=t=>{if(!t.options.links)return;const n=this.linkContainer,e=t.options.links;let s=void 0===e.id?n.particles.linksColor:n.particles.linksColors.get(e.id);if(s)return;const o=e.color;s=(0,i.getLinkRandomColor)(o,e.blink,e.consent),void 0===e.id?n.particles.linksColor=s:n.particles.linksColors.set(e.id,s)},this.linkContainer=t}clear(){}init(){this.linkContainer.particles.linksColor=void 0,this.linkContainer.particles.linksColors=new Map}async interact(n){if(!n.options.links)return;n.links=[];const e=n.getPosition(),s=this.container,o=s.canvas.size;if(e.x<0||e.y<0||e.x>o.width||e.y>o.height)return;const r=n.options.links,l=r.opacity,c=n.retina.linksDistance??0,d=r.warp,h=d?new t(e.x,e.y,c,o):new i.Circle(e.x,e.y,c),p=s.particles.quadTree.query(h);for(const i of p){const t=i.options.links;if(n===i||!t?.enable||r.id!==t.id||i.spawning||i.destroyed||!i.links||n.links.some((t=>t.destination===i))||i.links.some((i=>i.destination===n)))continue;const s=i.getPosition();if(s.x<0||s.y<0||s.x>o.width||s.y>o.height)continue;const h=a(e,s,c,o,d&&t.warp);if(h>c)continue;const p=(1-h/c)*l;this._setColor(n),n.links.push({destination:i,opacity:p})}}isEnabled(i){return!!i.options.links?.enable}loadParticlesOptions(i,...t){i.links||(i.links=new r);for(const n of t)i.links.load(n?.links??n?.lineLinked??n?.line_linked)}reset(){}}function c(t,n){const e=((s=t.map((i=>i.id))).sort(((i,t)=>i-t)),s.join("_"));var s;let o=n.get(e);return void 0===o&&(o=(0,i.getRandom)(),n.set(e,o)),o}class d{constructor(t){this.container=t,this._drawLinkLine=(t,n)=>{const e=t.options.links;if(!e?.enable)return;const s=this.container,o=s.actualOptions,r=n.destination,a=t.getPosition(),l=r.getPosition();let c=n.opacity;s.canvas.draw((n=>{let d;const h=t.options.twinkle?.lines;if(h?.enable){const t=h.frequency,n=(0,i.rangeColorToRgb)(h.color);(0,i.getRandom)()<t&&n&&(d=n,c=(0,i.getRangeValue)(h.opacity))}if(!d){const n=void 0!==e.id?s.particles.linksColors.get(e.id):s.particles.linksColor;d=(0,i.getLinkColor)(t,r,n)}if(!d)return;const p=t.retina.linksWidth??0,u=t.retina.linksDistance??0,{backgroundMask:y}=o;!function(t){let n=!1;const{begin:e,end:s,maxDistance:o,context:r,canvasSize:a,width:l,backgroundMask:c,colorLine:d,opacity:h,links:p}=t;if((0,i.getDistance)(e,s)<=o)(0,i.drawLine)(r,e,s),n=!0;else if(p.warp){let t,l;const c={x:s.x-a.width,y:s.y},d=(0,i.getDistances)(e,c);if(d.distance<=o){const i=e.y-d.dy/d.dx*e.x;t={x:0,y:i},l={x:a.width,y:i}}else{const n={x:s.x,y:s.y-a.height},r=(0,i.getDistances)(e,n);if(r.distance<=o){const i=-(e.y-r.dy/r.dx*e.x)/(r.dy/r.dx);t={x:i,y:0},l={x:i,y:a.height}}else{const n={x:s.x-a.width,y:s.y-a.height},r=(0,i.getDistances)(e,n);if(r.distance<=o){const i=e.y-r.dy/r.dx*e.x;t={x:-i/(r.dy/r.dx),y:i},l={x:t.x+a.width,y:t.y+a.height}}}}t&&l&&((0,i.drawLine)(r,e,t),(0,i.drawLine)(r,s,l),n=!0)}if(!n)return;r.lineWidth=l,c.enable&&(r.globalCompositeOperation=c.composite),r.strokeStyle=(0,i.getStyleFromRgb)(d,h);const{shadow:u}=p;if(u.enable){const t=(0,i.rangeColorToRgb)(u.color);t&&(r.shadowBlur=u.blur,r.shadowColor=(0,i.getStyleFromRgb)(t))}r.stroke()}({context:n,width:p,begin:a,end:l,maxDistance:u,canvasSize:s.canvas.size,links:e,backgroundMask:y,colorLine:d,opacity:c})}))},this._drawLinkTriangle=(t,n,e)=>{const s=t.options.links;if(!s?.enable)return;const o=s.triangles;if(!o.enable)return;const r=this.container,a=r.actualOptions,l=n.destination,c=e.destination,d=o.opacity??(n.opacity+e.opacity)/2;d<=0||r.canvas.draw((n=>{const e=t.getPosition(),h=l.getPosition(),p=c.getPosition(),u=t.retina.linksDistance??0;if((0,i.getDistance)(e,h)>u||(0,i.getDistance)(p,h)>u||(0,i.getDistance)(p,e)>u)return;let y=(0,i.rangeColorToRgb)(o.color);if(!y){const n=void 0!==s.id?r.particles.linksColors.get(s.id):r.particles.linksColor;y=(0,i.getLinkColor)(t,l,n)}y&&function(t){const{context:n,pos1:e,pos2:s,pos3:o,backgroundMask:r,colorTriangle:a,opacityTriangle:l}=t;(0,i.drawTriangle)(n,e,s,o),r.enable&&(n.globalCompositeOperation=r.composite),n.fillStyle=(0,i.getStyleFromRgb)(a,l),n.fill()}({context:n,pos1:e,pos2:h,pos3:p,backgroundMask:a.backgroundMask,colorTriangle:y,opacityTriangle:d})}))},this._drawTriangles=(i,t,n,e)=>{const s=n.destination;if(!i.links?.triangles.enable||!s.options.links?.triangles.enable)return;const o=s.links?.filter((i=>{const t=this._getLinkFrequency(s,i.destination);return s.options.links&&t<=s.options.links.frequency&&e.findIndex((t=>t.destination===i.destination))>=0}));if(o?.length)for(const e of o){const o=e.destination;this._getTriangleFrequency(t,s,o)>i.links.triangles.frequency||this._drawLinkTriangle(t,n,e)}},this._getLinkFrequency=(i,t)=>c([i,t],this._freqs.links),this._getTriangleFrequency=(i,t,n)=>c([i,t,n],this._freqs.triangles),this._freqs={links:new Map,triangles:new Map}}drawParticle(i,t){const{links:n,options:e}=t;if(!n||n.length<=0)return;const s=n.filter((i=>e.links&&this._getLinkFrequency(t,i.destination)<=e.links.frequency));for(const i of s)this._drawTriangles(e,t,i,s),i.opacity>0&&(t.retina.linksWidth??0)>0&&this._drawLinkLine(t,i)}async init(){this._freqs.links=new Map,this._freqs.triangles=new Map}particleCreated(i){if(i.links=[],!i.options.links)return;const t=this.container.retina.pixelRatio,{retina:n}=i,{distance:e,width:s}=i.options.links;n.linksDistance=e*t,n.linksWidth=s*t}particleDestroyed(i){i.links=[]}}class h{constructor(){this.id="links"}getPlugin(i){return new d(i)}loadOptions(){}needsPlugin(){return!0}}async function p(i,t=!0){await async function(i,t=!0){await i.addInteractor("particlesLinks",(i=>new l(i)),t)}(i,t),await async function(i,t=!0){const n=new h;await i.addPlugin(n,t)}(i,t)}})(),s})()));